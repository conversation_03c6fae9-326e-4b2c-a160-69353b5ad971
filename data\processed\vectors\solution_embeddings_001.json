{"solution_embeddings": {"collection_id": "SOL_EMB_001", "collection_name": "解决方案向量嵌入", "created_date": "2024-01-16T16:00:00Z", "model_info": {"model_name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "embedding_dimension": 384, "max_sequence_length": 512}, "total_solutions": 80, "solution_categories": ["immediate_action", "diagnostic_procedure", "repair_method", "preventive_measure", "emergency_response"]}, "solutions": [{"solution_id": "SOL_001", "solution_title": "变压器差动保护误动处理方案", "solution_category": "diagnostic_procedure", "applicable_faults": ["protection_malfunction", "CT_secondary_fault"], "urgency_level": "high", "description": "当变压器差动保护误动时的系统性诊断和处理流程", "detailed_steps": [{"step": 1, "action": "立即检查变压器外观", "description": "检查变压器本体、套管、冷却器等是否有异常", "expected_result": "确认变压器本体无故障", "time_required": "10分钟"}, {"step": 2, "action": "分析保护装置录波", "description": "查看差动保护动作时的电流波形和保护逻辑", "expected_result": "识别保护误动原因", "time_required": "20分钟"}, {"step": 3, "action": "检查CT二次回路", "description": "检查电流互感器二次端子、接线和回路完整性", "expected_result": "发现并修复回路故障", "time_required": "30分钟"}, {"step": 4, "action": "进行保护校验", "description": "对差动保护进行二次注流试验", "expected_result": "确认保护功能正常", "time_required": "60分钟"}], "required_tools": ["万用表", "绝缘电阻表", "保护测试仪", "录波分析软件"], "required_personnel": ["保护专业人员", "变压器专业人员", "运行人员"], "safety_precautions": ["确认设备停电", "验电接地", "设置安全围栏", "专人监护"], "embedding_vector": [0.0912, -0.0345, 0.0678, -0.0789, 0.0123, 0.0456, -0.0912, 0.0345, -0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, -0.0789, 0.0123, -0.0456, 0.0912, -0.0345, 0.0678, 0.0789, -0.0123, 0.0456, -0.0912, 0.0345, -0.0678, -0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, 0.0789, 0.0123, -0.0456, -0.0912, 0.0345, 0.0678, -0.0789, -0.0123, 0.0456, 0.0912, -0.0345, -0.0678, 0.0789, 0.0123, -0.0456, 0.0912, 0.0345, 0.0678, 0.0789, -0.0123, 0.0456, -0.0912, -0.0345, 0.0678, -0.0789, 0.0123, -0.0456, 0.0912, 0.0345, -0.0678, 0.0789, 0.0123, 0.0456, -0.0912, -0.0345, 0.0678, -0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, -0.0456, -0.0912, -0.0345, -0.0678, -0.0789, -0.0123, 0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, -0.0678, -0.0789, -0.0123, -0.0456, -0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, 0.0345, 0.0678, -0.0789, -0.0123, -0.0456, -0.0912, -0.0345, -0.0678, 0.0789, 0.0123, 0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, -0.0456, 0.0912, -0.0345, -0.0678, -0.0789, -0.0123, 0.0456, -0.0912, 0.0345, 0.0678, 0.0789, 0.0123, -0.0456, 0.0912, -0.0345, 0.0678, -0.0789, -0.0123, -0.0456, -0.0912, 0.0345, -0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, -0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, -0.0678, 0.0789, -0.0123, -0.0456, -0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, -0.0678, -0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, -0.0456, -0.0912, -0.0345, -0.0678, -0.0789, -0.0123, 0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, -0.0789, -0.0123, -0.0456, -0.0912, 0.0345, -0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, -0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, -0.0678, 0.0789, -0.0123, -0.0456, -0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, -0.0678, -0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, -0.0456, -0.0912, -0.0345, -0.0678, -0.0789, -0.0123, 0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, -0.0789, -0.0123, -0.0456, -0.0912, 0.0345, -0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, -0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, -0.0678, 0.0789, -0.0123, -0.0456, -0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, -0.0678, -0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, -0.0456, -0.0912, -0.0345, -0.0678, -0.0789, -0.0123, 0.0456, 0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, -0.0789, -0.0123, -0.0456, -0.0912, 0.0345, -0.0678, 0.0789, 0.0123, 0.0456, 0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, -0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, 0.0678, 0.0789, -0.0123, -0.0456, 0.0912, 0.0345, 0.0678, -0.0789, 0.0123, 0.0456, -0.0912, -0.0345, -0.0678, 0.0789, -0.0123, -0.0456, -0.0912, 0.0345, 0.0678, 0.0789, 0.0123, 0.0456], "success_rate": 0.92, "average_resolution_time": "2小时", "related_solutions": ["SOL_008", "SOL_015", "SOL_023"], "metadata": {"processing_date": "2024-01-16T16:00:00Z", "solution_complexity": "medium", "effectiveness_rating": 4.5, "confidence": 0.94}}, {"solution_id": "SOL_002", "solution_title": "断路器SF6气体泄漏应急处理", "solution_category": "emergency_response", "applicable_faults": ["gas_leakage", "breaker_malfunction"], "urgency_level": "critical", "description": "断路器SF6气体泄漏的紧急处理和修复方案", "detailed_steps": [{"step": 1, "action": "立即停运断路器", "description": "将断路器退出运行，转移负荷至其他设备", "expected_result": "断路器安全停运", "time_required": "15分钟"}, {"step": 2, "action": "检测泄漏点", "description": "使用SF6检漏仪定位气体泄漏位置", "expected_result": "准确定位泄漏源", "time_required": "30分钟"}, {"step": 3, "action": "更换密封件", "description": "更换老化或损坏的密封圈和垫片", "expected_result": "消除泄漏源", "time_required": "120分钟"}, {"step": 4, "action": "补充SF6气体", "description": "按规定压力补充合格的SF6气体", "expected_result": "气体压力恢复正常", "time_required": "45分钟"}], "required_tools": ["SF6检漏仪", "气体回收装置", "压力表", "密封件"], "required_personnel": ["断路器专业人员", "气体处理人员", "安全监护人员"], "safety_precautions": ["佩戴防护用品", "确保通风良好", "监测气体浓度", "设置警戒区域"], "embedding_vector": [-0.0678, 0.0912, -0.0456, 0.0789, -0.0123, 0.0345, 0.0678, -0.0912, 0.0456, -0.0789, 0.0123, -0.0345, -0.0678, -0.0912, -0.0456, 0.0789, -0.0123, 0.0345, -0.0678, 0.0912, -0.0456, -0.0789, 0.0123, -0.0345, 0.0678, -0.0912, 0.0456, 0.0789, -0.0123, -0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, 0.0123, -0.0345, -0.0678, 0.0912, 0.0456, -0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, 0.0123, -0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, 0.0456, -0.0789, -0.0123, -0.0345, 0.0678, 0.0912, -0.0456, 0.0789, 0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, 0.0789, 0.0123, 0.0345, 0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, 0.0912, 0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, -0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, 0.0789, 0.0123, 0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, -0.0345, 0.0678, -0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, 0.0912, 0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, -0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, 0.0789, 0.0123, 0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, -0.0345, 0.0678, -0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, 0.0912, 0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, -0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, 0.0789, 0.0123, 0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, -0.0345, 0.0678, -0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, 0.0912, 0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, -0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, 0.0789, 0.0123, 0.0345], "success_rate": 0.88, "average_resolution_time": "4小时", "related_solutions": ["SOL_012", "SOL_019", "SOL_025"], "metadata": {"processing_date": "2024-01-16T16:05:00Z", "solution_complexity": "high", "effectiveness_rating": 4.2, "confidence": 0.91}}], "solution_effectiveness": {"success_metrics": {"overall_success_rate": 0.89, "average_resolution_time": "3.2小时", "customer_satisfaction": 4.3, "cost_effectiveness": 0.85}, "performance_by_category": {"immediate_action": 0.95, "diagnostic_procedure": 0.91, "repair_method": 0.87, "preventive_measure": 0.83, "emergency_response": 0.89}}, "quality_metrics": {"solution_completeness": 0.92, "procedural_accuracy": 0.89, "safety_compliance": 0.96, "knowledge_coverage": 0.88}}