"""
运行方式分析推理链

分析电力系统运行方式和操作建议
"""

from typing import Dict, Any, List, Optional
from langchain.chains.base import Chain
from langchain.llms.base import BaseLLM
from pydantic import Field
from loguru import logger

from retriever.knowledge_base import KnowledgeBase
from ..prompts.prompt_manager import PromptManager
from ..tools.equipment_tool import EquipmentLocatorTool, EquipmentStatusTool


class OperationAnalysisChain(Chain):
    """运行方式分析推理链"""
    
    llm: BaseLLM = Field()
    knowledge_base: KnowledgeBase = Field()
    prompt_manager: PromptManager = Field()
    config: Dict[str, Any] = Field(default_factory=dict)
    
    # 工具
    equipment_tool: Optional[EquipmentLocatorTool] = None
    status_tool: Optional[EquipmentStatusTool] = None
    
    input_key: str = "operation_request"
    output_key: str = "analysis_result"
    
    def __init__(self, llm: BaseLLM, knowledge_base: KnowledgeBase, 
                 prompt_manager: PromptManager, config: Dict[str, Any], **kwargs):
        super().__init__(
            llm=llm, 
            knowledge_base=knowledge_base, 
            prompt_manager=prompt_manager, 
            config=config, 
            **kwargs
        )
        self._initialize_tools()
    
    def _initialize_tools(self):
        """初始化工具"""
        try:
            self.equipment_tool = EquipmentLocatorTool(self.config)
            self.status_tool = EquipmentStatusTool(self.config)
            logger.info("运行方式分析工具初始化完成")
            
        except Exception as e:
            logger.error(f"工具初始化失败: {str(e)}")
    
    @property
    def input_keys(self) -> List[str]:
        """输入键"""
        return [self.input_key]
    
    @property
    def output_keys(self) -> List[str]:
        """输出键"""
        return [self.output_key]
    
    def _call(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行运行方式分析"""
        try:
            operation_request = inputs[self.input_key]
            
            # 解析运行方式请求
            parsed_request = self._parse_operation_request(operation_request)
            
            # 收集相关信息
            system_info = self._collect_system_information(parsed_request)
            
            # 执行分析
            analysis_result = self._perform_operation_analysis(parsed_request, system_info)
            
            return {self.output_key: analysis_result}
            
        except Exception as e:
            logger.error(f"运行方式分析执行失败: {str(e)}")
            return {self.output_key: f"运行方式分析失败: {str(e)}"}
    
    def _parse_operation_request(self, request: str) -> Dict[str, Any]:
        """解析运行方式请求"""
        try:
            # 简单的关键词提取
            parsed = {
                "request_text": request,
                "operation_type": "unknown",
                "equipment_list": [],
                "special_conditions": [],
                "priority": "normal"
            }
            
            request_lower = request.lower()
            
            # 识别操作类型
            if any(word in request_lower for word in ["倒闸", "操作", "切换"]):
                parsed["operation_type"] = "switching"
            elif any(word in request_lower for word in ["检修", "维护", "停电"]):
                parsed["operation_type"] = "maintenance"
            elif any(word in request_lower for word in ["故障", "事故", "异常"]):
                parsed["operation_type"] = "emergency"
            elif any(word in request_lower for word in ["运行方式", "方式", "调整"]):
                parsed["operation_type"] = "operation_mode"
            
            # 识别优先级
            if any(word in request_lower for word in ["紧急", "急", "立即"]):
                parsed["priority"] = "high"
            elif any(word in request_lower for word in ["计划", "预定", "安排"]):
                parsed["priority"] = "scheduled"
            
            return parsed
            
        except Exception as e:
            logger.error(f"解析运行方式请求失败: {str(e)}")
            return {"request_text": request, "operation_type": "unknown"}
    
    def _collect_system_information(self, parsed_request: Dict[str, Any]) -> Dict[str, Any]:
        """收集系统信息"""
        try:
            system_info = {
                "equipment_status": {},
                "system_status": "normal",
                "load_condition": "normal",
                "weather_condition": "normal",
                "related_documents": []
            }
            
            # 搜索相关文档
            search_query = parsed_request.get("request_text", "")
            if search_query:
                search_results = self.knowledge_base.search(
                    query=search_query,
                    search_type="text",
                    top_k=3
                )
                
                if search_results.get("results"):
                    system_info["related_documents"] = search_results["results"]
            
            # 如果有设备工具，查询设备状态
            if self.status_tool and parsed_request.get("equipment_list"):
                for equipment in parsed_request["equipment_list"]:
                    try:
                        status = self.status_tool._run(equipment)
                        system_info["equipment_status"][equipment] = status
                    except Exception as e:
                        logger.warning(f"查询设备状态失败 {equipment}: {str(e)}")
            
            return system_info
            
        except Exception as e:
            logger.error(f"收集系统信息失败: {str(e)}")
            return {}
    
    def _perform_operation_analysis(self, parsed_request: Dict[str, Any], 
                                  system_info: Dict[str, Any]) -> str:
        """执行运行方式分析"""
        try:
            # 准备分析数据
            operation_type = parsed_request.get("operation_type", "unknown")
            request_text = parsed_request.get("request_text", "")
            
            # 格式化系统状态
            system_status = self._format_system_status(system_info)
            
            # 根据操作类型选择合适的提示词模板
            if operation_type == "switching":
                analysis = self._analyze_switching_operation(request_text, system_status)
            elif operation_type == "maintenance":
                analysis = self._analyze_maintenance_operation(request_text, system_status)
            elif operation_type == "emergency":
                analysis = self._analyze_emergency_operation(request_text, system_status)
            else:
                analysis = self._analyze_general_operation(request_text, system_status)
            
            return analysis
            
        except Exception as e:
            logger.error(f"执行运行方式分析失败: {str(e)}")
            return f"分析过程中出现错误: {str(e)}"
    
    def _format_system_status(self, system_info: Dict[str, Any]) -> str:
        """格式化系统状态信息"""
        try:
            status_parts = []
            
            # 设备状态
            equipment_status = system_info.get("equipment_status", {})
            if equipment_status:
                status_parts.append("设备状态:")
                for equipment, status in equipment_status.items():
                    status_parts.append(f"  {equipment}: {status[:200]}...")  # 截取前200字符
            
            # 系统状态
            system_status = system_info.get("system_status", "normal")
            status_parts.append(f"系统状态: {system_status}")
            
            # 负荷情况
            load_condition = system_info.get("load_condition", "normal")
            status_parts.append(f"负荷情况: {load_condition}")
            
            # 天气条件
            weather_condition = system_info.get("weather_condition", "normal")
            status_parts.append(f"天气条件: {weather_condition}")
            
            # 相关文档
            related_docs = system_info.get("related_documents", [])
            if related_docs:
                status_parts.append("相关文档:")
                for doc in related_docs[:2]:  # 最多显示2个文档
                    source = doc.get("metadata", {}).get("source", "unknown")
                    content = doc.get("content", "")[:100]
                    status_parts.append(f"  {source}: {content}...")
            
            return "\n".join(status_parts)
            
        except Exception as e:
            logger.error(f"格式化系统状态失败: {str(e)}")
            return "系统状态信息获取失败"
    
    def _analyze_switching_operation(self, request: str, system_status: str) -> str:
        """分析倒闸操作"""
        try:
            prompt = f"""你是一个电力系统倒闸操作专家，请分析以下倒闸操作请求：

操作请求：{request}

系统状态：
{system_status}

请提供：
1. 操作可行性分析
2. 操作步骤建议
3. 安全注意事项
4. 风险评估
5. 应急预案

分析结果："""
            
            response = self.llm.invoke(prompt)
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"倒闸操作分析失败: {str(e)}")
            return f"倒闸操作分析失败: {str(e)}"
    
    def _analyze_maintenance_operation(self, request: str, system_status: str) -> str:
        """分析检修操作"""
        try:
            prompt = f"""你是一个电力设备检修专家，请分析以下检修操作请求：

检修请求：{request}

系统状态：
{system_status}

请提供：
1. 检修方案建议
2. 停电范围分析
3. 安全措施要求
4. 检修时间安排
5. 恢复操作步骤

分析结果："""
            
            response = self.llm.invoke(prompt)
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"检修操作分析失败: {str(e)}")
            return f"检修操作分析失败: {str(e)}"
    
    def _analyze_emergency_operation(self, request: str, system_status: str) -> str:
        """分析应急操作"""
        try:
            prompt = f"""你是一个电力系统应急处置专家，请分析以下应急情况：

应急情况：{request}

系统状态：
{system_status}

请提供：
1. 紧急处置措施
2. 影响范围评估
3. 恢复方案
4. 预防措施
5. 上报要求

分析结果："""
            
            response = self.llm.invoke(prompt)
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"应急操作分析失败: {str(e)}")
            return f"应急操作分析失败: {str(e)}"
    
    def _analyze_general_operation(self, request: str, system_status: str) -> str:
        """分析一般运行方式"""
        try:
            # 使用运行方式分析模板
            prompt = self.prompt_manager.format_prompt(
                "operation_analysis",
                system_status=system_status,
                load_condition="正常",
                equipment_status="正常",
                weather_condition="正常",
                special_requirements=request
            )
            
            if not prompt:
                # 如果模板不存在，使用简单格式
                prompt = f"""请分析以下运行方式请求：

请求内容：{request}

系统状态：
{system_status}

请提供运行方式分析和建议。

分析结果："""
            
            response = self.llm.invoke(prompt)
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"一般运行方式分析失败: {str(e)}")
            return f"运行方式分析失败: {str(e)}"
    
    def analyze_operation(self, operation_request: str) -> Dict[str, Any]:
        """
        运行方式分析接口
        
        Args:
            operation_request: 运行方式请求
            
        Returns:
            分析结果
        """
        try:
            result = self._call({self.input_key: operation_request})
            
            return {
                "success": True,
                "request": operation_request,
                "analysis": result[self.output_key]
            }
            
        except Exception as e:
            logger.error(f"运行方式分析接口执行失败: {str(e)}")
            return {
                "success": False,
                "request": operation_request,
                "error": str(e)
            }
