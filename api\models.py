"""
API数据模型

定义API请求和响应的数据结构
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime


# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(description="操作是否成功")
    message: str = Field(default="", description="响应消息")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="时间戳")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(default=False)
    error_code: Optional[str] = Field(default=None, description="错误代码")
    error_detail: Optional[str] = Field(default=None, description="错误详情")


# 故障分析相关模型
class FaultAnalysisRequest(BaseModel):
    """故障分析请求"""
    description: str = Field(description="故障描述")
    equipment_info: Optional[Dict[str, Any]] = Field(default=None, description="设备信息")
    images: Optional[List[str]] = Field(default=None, description="相关图像路径列表")
    waveform_data: Optional[str] = Field(default=None, description="波形数据")
    priority: Optional[str] = Field(default="normal", description="优先级: low, normal, high")
    reporter: Optional[str] = Field(default=None, description="报告人")


class FaultAnalysisResponse(BaseResponse):
    """故障分析响应"""
    analysis_id: Optional[str] = Field(default=None, description="分析ID")
    report: Optional[Dict[str, Any]] = Field(default=None, description="分析报告")
    recommendations: Optional[List[str]] = Field(default=None, description="处理建议")
    similar_cases: Optional[List[Dict[str, Any]]] = Field(default=None, description="相似案例")


# 设备管理相关模型
class EquipmentInfo(BaseModel):
    """设备信息"""
    id: str = Field(description="设备ID")
    name: str = Field(description="设备名称")
    type: str = Field(description="设备类型")
    voltage_level: Optional[str] = Field(default=None, description="电压等级")
    manufacturer: Optional[str] = Field(default=None, description="制造商")
    install_date: Optional[str] = Field(default=None, description="安装日期")
    location: Optional[str] = Field(default=None, description="位置")
    status: Optional[str] = Field(default="unknown", description="状态")
    specifications: Optional[Dict[str, Any]] = Field(default=None, description="技术规格")


class EquipmentSearchRequest(BaseModel):
    """设备搜索请求"""
    query: Optional[str] = Field(default=None, description="搜索关键词")
    type: Optional[str] = Field(default=None, description="设备类型")
    voltage_level: Optional[str] = Field(default=None, description="电压等级")
    status: Optional[str] = Field(default=None, description="状态")
    location: Optional[str] = Field(default=None, description="位置")


class EquipmentSearchResponse(BaseResponse):
    """设备搜索响应"""
    equipment_list: List[EquipmentInfo] = Field(default=[], description="设备列表")
    total_count: int = Field(default=0, description="总数量")


class EquipmentStatusUpdate(BaseModel):
    """设备状态更新"""
    equipment_id: str = Field(description="设备ID")
    status: str = Field(description="新状态")
    notes: Optional[str] = Field(default=None, description="备注")


class MaintenanceScheduleResponse(BaseResponse):
    """维护计划响应"""
    schedule: List[Dict[str, Any]] = Field(default=[], description="维护计划列表")
    total_count: int = Field(default=0, description="总数量")


# 知识库相关模型
class KnowledgeSearchRequest(BaseModel):
    """知识库搜索请求"""
    query: str = Field(description="搜索查询")
    search_type: Optional[str] = Field(default="multimodal", description="搜索类型: text, image, multimodal")
    top_k: Optional[int] = Field(default=5, description="返回结果数量")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="搜索过滤条件")


class KnowledgeSearchResponse(BaseResponse):
    """知识库搜索响应"""
    results: Dict[str, Any] = Field(default={}, description="搜索结果")
    query: str = Field(description="搜索查询")
    search_type: str = Field(description="搜索类型")
    total_results: int = Field(default=0, description="结果总数")


class DocumentQARequest(BaseModel):
    """文档问答请求"""
    question: str = Field(description="问题")
    search_type: Optional[str] = Field(default="multimodal", description="搜索类型")
    context_limit: Optional[int] = Field(default=3, description="上下文文档数量限制")


class DocumentQAResponse(BaseResponse):
    """文档问答响应"""
    question: str = Field(description="问题")
    answer: str = Field(description="答案")
    references: List[Dict[str, Any]] = Field(default=[], description="参考文献")
    confidence: Optional[float] = Field(default=None, description="置信度")


# 文件上传相关模型
class FileUploadResponse(BaseResponse):
    """文件上传响应"""
    filename: str = Field(description="文件名")
    file_path: str = Field(description="文件路径")
    file_size: int = Field(description="文件大小")
    file_type: str = Field(description="文件类型")
    upload_time: str = Field(default_factory=lambda: datetime.now().isoformat(), description="上传时间")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="文件元数据")
    processing_results: Optional[Dict[str, Any]] = Field(default=None, description="处理结果")


class BatchUploadResponse(BaseResponse):
    """批量上传响应"""
    uploaded_files: List[FileUploadResponse] = Field(default=[], description="已上传文件列表")
    failed_files: List[Dict[str, str]] = Field(default=[], description="上传失败文件列表")
    total_files: int = Field(description="总文件数")
    success_count: int = Field(description="成功上传数")
    failed_count: int = Field(description="失败上传数")


# 运行方式分析相关模型
class OperationAnalysisRequest(BaseModel):
    """运行方式分析请求"""
    operation_request: str = Field(description="运行方式请求描述")
    equipment_list: Optional[List[str]] = Field(default=None, description="相关设备列表")
    priority: Optional[str] = Field(default="normal", description="优先级")
    requester: Optional[str] = Field(default=None, description="请求人")


class OperationAnalysisResponse(BaseResponse):
    """运行方式分析响应"""
    request: str = Field(description="原始请求")
    analysis: str = Field(description="分析结果")
    operation_type: Optional[str] = Field(default=None, description="操作类型")
    risk_level: Optional[str] = Field(default=None, description="风险等级")
    recommendations: Optional[List[str]] = Field(default=None, description="操作建议")


# 系统状态相关模型
class SystemStatus(BaseModel):
    """系统状态"""
    service_name: str = Field(description="服务名称")
    version: str = Field(description="版本")
    status: str = Field(description="状态")
    uptime: str = Field(description="运行时间")
    components: Dict[str, Any] = Field(default={}, description="组件状态")


class ComponentStatus(BaseModel):
    """组件状态"""
    name: str = Field(description="组件名称")
    status: str = Field(description="状态")
    last_check: str = Field(default_factory=lambda: datetime.now().isoformat(), description="最后检查时间")
    details: Optional[Dict[str, Any]] = Field(default=None, description="详细信息")


# OCR和图像分析相关模型
class OCRRequest(BaseModel):
    """OCR请求"""
    image_path: str = Field(description="图像路径")
    ocr_type: Optional[str] = Field(default="general", description="OCR类型: general, table")
    language: Optional[str] = Field(default="ch", description="语言")


class OCRResponse(BaseResponse):
    """OCR响应"""
    image_path: str = Field(description="图像路径")
    ocr_result: Dict[str, Any] = Field(description="OCR结果")
    confidence: float = Field(description="置信度")
    processing_time: float = Field(description="处理时间")


class DefectDetectionRequest(BaseModel):
    """缺陷检测请求"""
    image_path: str = Field(description="图像路径")
    detection_types: Optional[List[str]] = Field(default=None, description="检测类型列表")


class DefectDetectionResponse(BaseResponse):
    """缺陷检测响应"""
    image_path: str = Field(description="图像路径")
    defects: Dict[str, Any] = Field(description="检测到的缺陷")
    total_defects: int = Field(description="缺陷总数")
    processing_time: float = Field(description="处理时间")


# 波形分析相关模型
class WaveformAnalysisRequest(BaseModel):
    """波形分析请求"""
    data_source: str = Field(description="数据源: file 或 json")
    data_path: Optional[str] = Field(default=None, description="文件路径")
    data_content: Optional[Dict[str, Any]] = Field(default=None, description="JSON数据内容")
    analysis_type: Optional[str] = Field(default="comprehensive", description="分析类型")


class WaveformAnalysisResponse(BaseResponse):
    """波形分析响应"""
    analysis_result: Dict[str, Any] = Field(description="分析结果")
    anomalies: List[Dict[str, Any]] = Field(default=[], description="异常检测结果")
    recommendations: List[str] = Field(default=[], description="建议")
    processing_time: float = Field(description="处理时间")


# 统计和报告相关模型
class StatisticsRequest(BaseModel):
    """统计请求"""
    start_date: Optional[str] = Field(default=None, description="开始日期")
    end_date: Optional[str] = Field(default=None, description="结束日期")
    category: Optional[str] = Field(default=None, description="统计类别")


class StatisticsResponse(BaseResponse):
    """统计响应"""
    statistics: Dict[str, Any] = Field(description="统计数据")
    charts: Optional[List[Dict[str, Any]]] = Field(default=None, description="图表数据")
    period: Dict[str, str] = Field(description="统计周期")


# 配置相关模型
class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    section: str = Field(description="配置节")
    updates: Dict[str, Any] = Field(description="更新内容")


class ConfigResponse(BaseResponse):
    """配置响应"""
    config: Dict[str, Any] = Field(description="配置内容")
    section: Optional[str] = Field(default=None, description="配置节")
