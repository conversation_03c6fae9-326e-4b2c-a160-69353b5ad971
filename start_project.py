#!/usr/bin/env python3
"""
故障分析智能助手项目启动脚本
统一启动所有服务在端口5002
"""

import os
import sys
import time
import threading
import subprocess
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config_manager import get_config

def check_dependencies():
    """检查必要的依赖"""
    logger.info("检查项目依赖...")
    
    required_packages = [
        'flask',
        'fastapi',
        'uvicorn',
        'flask_cors',
        'loguru',
        'pydantic',
        'httpx'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            logger.warning(f"❌ {package} - 未安装")
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    logger.info("创建必要的目录...")
    
    directories = [
        "logs",
        "data/raw/uploads",
        "data/structured",
        "data/processed",
        "knowledge_base/text",
        "knowledge_base/images",
        "knowledge_base/mappings",
        "embeddings"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 创建目录: {directory}")

def start_web_server():
    """启动Web服务器"""
    logger.info("启动Web服务器...")
    try:
        from server.web_server import app
        config = get_config()
        
        app.run(
            host=config.get('server.host', '0.0.0.0'),
            port=config.get('server.port', 5002),
            debug=False,  # 生产模式
            threaded=True,
            use_reloader=False
        )
    except Exception as e:
        logger.error(f"Web服务器启动失败: {e}")

def start_api_server():
    """启动API服务器"""
    logger.info("启动API服务器...")
    try:
        import uvicorn
        from api.main import app
        config = get_config()
        
        uvicorn.run(
            app,
            host=config.get('server.host', '0.0.0.0'),
            port=config.get('server.port', 5002) + 1,  # API服务器使用5003端口
            log_level="info",
            reload=False
        )
    except Exception as e:
        logger.error(f"API服务器启动失败: {e}")

def start_optimized_server():
    """启动主服务器（使用完整功能的ui/app.py）"""
    logger.info("启动主服务器...")
    try:
        # 使用完整功能的ui/app.py而不是简化版servers/optimized_server.py
        from ui.app import app
        config = get_config()

        port = config.get('server.port', 5002)
        host = config.get('server.host', '0.0.0.0')
        debug = config.get('system.debug', False)

        logger.info(f"🚀 主服务器启动在 http://{host}:{port}")
        logger.info("📊 使用完整功能版本 (ui/app.py)")
        app.run(host=host, port=port, debug=debug, threaded=True)

    except Exception as e:
        logger.error(f"主服务器启动失败: {e}")

def print_startup_info():
    """打印启动信息"""
    config = get_config()
    port = config.get('server.port', 5002)
    
    print("\n" + "=" * 60)
    print("🎯 故障分析智能助手系统")
    print("=" * 60)
    print(f"📍 主要访问地址: http://localhost:{port}")
    print(f"📍 本地访问: http://127.0.0.1:{port}")
    print(f"📍 API接口: http://localhost:{port}/api/v1/")
    print(f"📍 健康检查: http://localhost:{port}/health")
    print(f"📍 系统状态: http://localhost:{port}/api/v1/status")
    print("=" * 60)
    print("🔧 功能模块:")
    print("   • 故障分析智能助手")
    print("   • 设备管理系统")
    print("   • 知识库检索")
    print("   • 文件上传管理")
    print("   • DeepSeek AI集成")
    print("=" * 60)
    print("⚠️  按 Ctrl+C 停止服务器")
    print("=" * 60)

def main():
    """主函数"""
    logger.add("logs/startup.log", rotation="1 day", retention="7 days")
    
    print("🚀 启动故障分析智能助手系统...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 打印启动信息
    print_startup_info()
    
    try:
        # 启动主服务器（优化服务器包含所有功能）
        start_optimized_server()
        
    except KeyboardInterrupt:
        logger.info("⚠️  用户中断，正在停止服务器...")
        print("\n⚠️  服务器已停止")
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        print(f"❌ 系统启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
