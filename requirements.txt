# 故障分析智能助手项目依赖

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 大语言模型相关
langchain==0.1.0
langchain-community==0.0.10
openai==1.6.1
transformers==4.36.2
torch==2.1.2
sentence-transformers==2.2.2

# 向量数据库和检索
faiss-cpu==1.7.4
chromadb==0.4.18
numpy==1.24.3
scipy==1.11.4

# 数据处理
pandas==2.1.4
openpyxl==3.1.2
python-docx==1.1.0
PyPDF2==3.0.1
pillow==10.1.0
opencv-python>=4.6.0

# OCR和图像处理
pytesseract==0.3.10
easyocr==1.7.0
# paddleocr==2.7.3  # 暂时注释，避免opencv版本冲突

# Web框架和模板
flask==3.0.0
flask-cors==4.0.0
jinja2==3.1.2
aiofiles==23.2.1
python-multipart==0.0.6

# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
# sqlite3 is built-in to Python

# 工具库
requests==2.31.0
httpx==0.25.2
tqdm==4.66.1
click==8.1.7
rich==13.7.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# 时间序列和科学计算
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
scikit-learn==1.3.2

# 异步和并发
asyncio
aiohttp==3.9.1
celery==5.3.4
redis==5.0.1
