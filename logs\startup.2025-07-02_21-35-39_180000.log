2025-07-02 21:35:39.185 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:35:39.278 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:35:39.278 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:35:39.304 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:35:39.306 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:35:39.306 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:35:39.306 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:35:39.332 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:35:39.332 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:35:39.333 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:35:39.339 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:35:39.339 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:35:39.340 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:35:39.340 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:35:39.340 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:35:39.340 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:35:39.340 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:35:39.341 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:35:39.360 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:35:40.060 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:35:40.153 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:35:40.153 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:35:40.172 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:35:40.174 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:35:40.174 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:35:40.174 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:35:40.198 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:35:40.198 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:35:40.198 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:35:40.198 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:35:40.199 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:35:40.199 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:35:40.199 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:35:40.199 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:35:40.200 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:35:40.200 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:35:40.203 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:35:40.213 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:37:45.985 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:37:46.086 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:37:46.087 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:37:46.106 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:37:46.109 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:37:46.109 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:37:46.109 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:37:46.133 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:37:46.133 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:37:46.133 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:37:46.134 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:37:46.134 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:37:46.134 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:37:46.134 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:37:46.134 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:37:46.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:37:46.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:37:46.136 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:37:46.155 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:38:18.624 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:38:18.720 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:38:18.720 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:38:18.739 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:38:18.741 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:38:18.741 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:38:18.741 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:38:18.766 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:38:18.766 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:38:18.766 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:38:18.766 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:38:18.766 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:38:18.767 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:38:18.767 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:38:18.767 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:38:18.767 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:38:18.767 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:38:18.769 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:38:18.778 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:38:19.455 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:38:19.553 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:38:19.553 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:38:19.574 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:38:19.576 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:38:19.577 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:38:19.577 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:38:19.601 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:38:19.601 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:38:19.602 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:38:19.602 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:38:19.602 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:38:19.602 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:38:19.602 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:38:19.603 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:38:19.603 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:38:19.603 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:38:19.604 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:38:19.616 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:42:21.239 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:42:21.337 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:42:21.337 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:42:21.359 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:42:21.362 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:42:21.362 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:42:21.362 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:42:21.388 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:42:21.388 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:42:21.389 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:42:21.389 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:42:21.389 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:42:21.390 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:42:21.390 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:42:21.390 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:42:21.390 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:42:21.390 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:42:21.392 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:42:21.414 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:44:58.731 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:44:58.830 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:44:58.830 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:44:58.852 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:44:58.855 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:44:58.855 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:44:58.855 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:44:58.882 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:44:58.882 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:44:58.883 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:44:58.883 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:44:58.883 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:44:58.883 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:44:58.884 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:44:58.884 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:44:58.884 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:44:58.884 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:44:58.886 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:44:58.910 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:46:05.529 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:46:05.623 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:46:05.623 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:46:05.644 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:46:05.646 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:46:05.646 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:46:05.646 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:46:05.674 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:46:05.674 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:46:05.674 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:46:05.675 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:46:05.675 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:46:05.675 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:46:05.675 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:46:05.676 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:46:05.676 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:46:05.676 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:46:05.677 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:46:05.697 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:54:00.425 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:54:00.521 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:54:00.521 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:54:00.541 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:54:00.544 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:54:00.545 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:54:00.545 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:54:00.572 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:54:00.572 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:54:00.572 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:54:00.572 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:54:00.573 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:54:00.573 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:54:00.573 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:54:00.573 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:54:00.573 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:54:00.574 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:54:00.575 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:54:00.587 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:54:01.324 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:54:01.420 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:54:01.421 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:54:01.440 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:54:01.443 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:54:01.443 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:54:01.443 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:54:01.468 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:54:01.468 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:54:01.468 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:54:01.468 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:54:01.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:54:01.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:54:01.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:54:01.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:54:01.470 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:54:01.470 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:54:01.471 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:54:01.482 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:55:53.517 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:55:53.610 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:55:53.610 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:55:53.629 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:55:53.631 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:55:53.632 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:55:53.632 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:55:53.654 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:55:53.655 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:55:53.655 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:55:53.655 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:55:53.655 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:55:53.655 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:55:53.657 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:55:53.657 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:55:53.657 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:55:53.657 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:55:53.659 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:55:53.668 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:55:54.598 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:55:54.703 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:55:54.704 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:55:54.727 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:55:54.730 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:55:54.730 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:55:54.730 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:55:54.763 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:55:54.764 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:55:54.764 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:55:54.764 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:55:54.765 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:55:54.765 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:55:54.765 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:55:54.766 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:55:54.766 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:55:54.766 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:55:54.768 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:55:54.783 | INFO     | __main__:start_optimized_server:117 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:59:24.899 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:59:25.003 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:59:25.004 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:59:25.026 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:59:25.028 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:59:25.029 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:59:25.029 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:59:25.057 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:59:25.058 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:59:25.058 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:59:25.058 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:59:25.058 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:59:25.059 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:59:25.059 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:59:25.059 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:59:25.059 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:59:25.059 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:59:25.061 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:59:25.144 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:59:25.258 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:59:25.258 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:59:25.284 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:59:25.288 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:59:25.288 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:59:25.288 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:59:25.320 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:59:25.321 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:59:25.321 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:59:25.321 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:59:25.322 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:59:25.322 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:59:25.322 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:59:25.323 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:59:25.323 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:59:25.323 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:59:25.325 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:59:25.408 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:59:25.469 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:59:25.469 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-02 21:59:25.510 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:59:25.510 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:59:25.532 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:59:25.534 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:59:25.535 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:59:25.535 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:59:25.563 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:59:25.564 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:59:25.564 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:59:25.564 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:59:25.564 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:59:25.565 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:59:25.565 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:59:25.565 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:59:25.565 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:59:25.566 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:59:25.568 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:59:25.643 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:59:25.643 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-02 21:59:25.877 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:59:25.877 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-02 21:59:45.182 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:59:45.272 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:59:45.273 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:59:45.292 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:59:45.294 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:59:45.294 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:59:45.294 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:59:45.317 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:59:45.318 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:59:45.318 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:59:45.318 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:59:45.318 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:59:45.319 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:59:45.319 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:59:45.319 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:59:45.319 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:59:45.319 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:59:45.320 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:59:45.595 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:59:45.595 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-02 21:59:46.288 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 21:59:46.378 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 21:59:46.379 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 21:59:46.398 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 21:59:46.400 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 21:59:46.400 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 21:59:46.400 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 21:59:46.424 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 21:59:46.425 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 21:59:46.425 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 21:59:46.425 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 21:59:46.425 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 21:59:46.425 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 21:59:46.426 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 21:59:46.426 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 21:59:46.426 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 21:59:46.426 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 21:59:46.429 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 21:59:46.700 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 21:59:46.701 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-02 23:08:06.024 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 23:08:06.165 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 23:08:06.166 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 23:08:06.198 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 23:08:06.202 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 23:08:06.203 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 23:08:06.203 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 23:08:06.253 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 23:08:06.253 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 23:08:06.253 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 23:08:06.253 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 23:08:06.254 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 23:08:06.254 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 23:08:06.254 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 23:08:06.254 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 23:08:06.255 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 23:08:06.255 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 23:08:06.257 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 23:08:06.593 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 23:08:06.593 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-02 23:08:07.334 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-02 23:08:07.458 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-02 23:08:07.458 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-02 23:08:07.480 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-02 23:08:07.482 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-02 23:08:07.482 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-02 23:08:07.482 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-02 23:08:07.509 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-02 23:08:07.510 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-02 23:08:07.510 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-02 23:08:07.510 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-02 23:08:07.511 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-02 23:08:07.511 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-02 23:08:07.511 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-02 23:08:07.511 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-02 23:08:07.511 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-02 23:08:07.512 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-02 23:08:07.513 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-02 23:08:07.792 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-02 23:08:07.792 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-03 15:46:33.564 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-03 15:46:33.734 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-03 15:46:33.734 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-03 15:46:33.766 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-03 15:46:33.771 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-03 15:46:33.772 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-03 15:46:33.772 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-03 15:46:33.826 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-03 15:46:33.826 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-03 15:46:33.827 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-03 15:46:33.827 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-03 15:46:33.827 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-03 15:46:33.828 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-03 15:46:33.828 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-03 15:46:33.828 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-03 15:46:33.828 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-03 15:46:33.829 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-03 15:46:33.830 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-03 15:46:44.305 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-03 15:46:44.305 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-03 15:46:45.023 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-03 15:46:45.128 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-03 15:46:45.128 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-03 15:46:45.148 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-03 15:46:45.151 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-03 15:46:45.151 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-03 15:46:45.151 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-03 15:46:45.178 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-03 15:46:45.178 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-03 15:46:45.178 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-03 15:46:45.179 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-03 15:46:45.179 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-03 15:46:45.180 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-03 15:46:45.180 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-03 15:46:45.180 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-03 15:46:45.181 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-03 15:46:45.181 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-03 15:46:45.183 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-03 15:46:54.185 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-03 15:46:54.186 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
