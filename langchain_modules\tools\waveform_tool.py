"""
波形分析工具

为LangChain提供电力系统波形分析功能
"""

import json
import numpy as np
from typing import Dict, Any, List, Optional
from langchain.tools import BaseTool
from pydantic import Field
from loguru import logger


class WaveformAnalysisTool(BaseTool):
    """波形分析工具"""
    
    name: str = "waveform_analysis_tool"
    description: str = """
    电力系统波形分析工具。用于分析电压、电流波形数据，检测异常和故障特征。
    输入: 波形数据文件路径或波形数据JSON
    输出: 波形分析结果，包括频率、幅值、谐波、异常检测等
    """
    
    def _run(self, waveform_input: str) -> str:
        """
        执行波形分析
        
        Args:
            waveform_input: 波形数据文件路径或JSON字符串
            
        Returns:
            波形分析结果的字符串描述
        """
        try:
            # 尝试解析输入
            waveform_data = self._parse_waveform_input(waveform_input)
            
            if not waveform_data:
                return "无法解析波形数据"
            
            # 执行各种分析
            analysis_results = {}
            
            # 基础统计分析
            basic_stats = self._analyze_basic_statistics(waveform_data)
            analysis_results["basic_stats"] = basic_stats
            
            # 频域分析
            frequency_analysis = self._analyze_frequency_domain(waveform_data)
            analysis_results["frequency"] = frequency_analysis
            
            # 谐波分析
            harmonic_analysis = self._analyze_harmonics(waveform_data)
            analysis_results["harmonics"] = harmonic_analysis
            
            # 异常检测
            anomaly_detection = self._detect_anomalies(waveform_data)
            analysis_results["anomalies"] = anomaly_detection
            
            # 格式化输出
            return self._format_analysis_results(analysis_results)
            
        except Exception as e:
            logger.error(f"波形分析工具执行失败: {str(e)}")
            return f"波形分析失败: {str(e)}"
    
    def _parse_waveform_input(self, waveform_input: str) -> Optional[Dict[str, Any]]:
        """解析波形输入数据"""
        try:
            # 尝试作为JSON解析
            if waveform_input.strip().startswith('{'):
                return json.loads(waveform_input)
            
            # 尝试作为文件路径
            try:
                with open(waveform_input, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except FileNotFoundError:
                logger.warning(f"波形文件不存在: {waveform_input}")
                return None
            
        except Exception as e:
            logger.error(f"解析波形数据失败: {str(e)}")
            return None
    
    def _analyze_basic_statistics(self, waveform_data: Dict[str, Any]) -> Dict[str, Any]:
        """基础统计分析"""
        try:
            results = {}
            
            for signal_name, signal_data in waveform_data.items():
                if isinstance(signal_data, list) and len(signal_data) > 0:
                    values = np.array(signal_data)
                    
                    stats = {
                        "mean": float(np.mean(values)),
                        "std": float(np.std(values)),
                        "min": float(np.min(values)),
                        "max": float(np.max(values)),
                        "rms": float(np.sqrt(np.mean(values**2))),
                        "peak_to_peak": float(np.max(values) - np.min(values)),
                        "samples": len(values)
                    }
                    
                    results[signal_name] = stats
            
            return results
            
        except Exception as e:
            logger.error(f"基础统计分析失败: {str(e)}")
            return {}
    
    def _analyze_frequency_domain(self, waveform_data: Dict[str, Any]) -> Dict[str, Any]:
        """频域分析"""
        try:
            results = {}
            
            for signal_name, signal_data in waveform_data.items():
                if isinstance(signal_data, list) and len(signal_data) > 0:
                    values = np.array(signal_data)
                    
                    # FFT分析
                    fft = np.fft.fft(values)
                    freqs = np.fft.fftfreq(len(values))
                    
                    # 找到主频率
                    magnitude = np.abs(fft)
                    dominant_freq_idx = np.argmax(magnitude[1:len(magnitude)//2]) + 1
                    dominant_freq = abs(freqs[dominant_freq_idx])
                    
                    freq_analysis = {
                        "dominant_frequency": float(dominant_freq),
                        "frequency_resolution": float(1.0 / len(values)),
                        "nyquist_frequency": float(0.5),
                        "spectral_centroid": float(np.sum(freqs[:len(freqs)//2] * magnitude[:len(magnitude)//2]) / np.sum(magnitude[:len(magnitude)//2]))
                    }
                    
                    results[signal_name] = freq_analysis
            
            return results
            
        except Exception as e:
            logger.error(f"频域分析失败: {str(e)}")
            return {}
    
    def _analyze_harmonics(self, waveform_data: Dict[str, Any]) -> Dict[str, Any]:
        """谐波分析"""
        try:
            results = {}
            
            for signal_name, signal_data in waveform_data.items():
                if isinstance(signal_data, list) and len(signal_data) > 0:
                    values = np.array(signal_data)
                    
                    # 简化的谐波分析
                    fft = np.fft.fft(values)
                    magnitude = np.abs(fft)
                    
                    # 假设基频为50Hz，计算谐波
                    fundamental_idx = 1  # 简化假设
                    fundamental_magnitude = magnitude[fundamental_idx]
                    
                    harmonics = {}
                    for h in range(2, 6):  # 2-5次谐波
                        if h * fundamental_idx < len(magnitude):
                            harmonic_magnitude = magnitude[h * fundamental_idx]
                            thd_contribution = (harmonic_magnitude / fundamental_magnitude) * 100 if fundamental_magnitude > 0 else 0
                            harmonics[f"harmonic_{h}"] = {
                                "magnitude": float(harmonic_magnitude),
                                "thd_percent": float(thd_contribution)
                            }
                    
                    # 总谐波失真
                    total_thd = np.sqrt(sum([h["thd_percent"]**2 for h in harmonics.values()]))
                    
                    harmonic_analysis = {
                        "fundamental_magnitude": float(fundamental_magnitude),
                        "total_thd_percent": float(total_thd),
                        "harmonics": harmonics
                    }
                    
                    results[signal_name] = harmonic_analysis
            
            return results
            
        except Exception as e:
            logger.error(f"谐波分析失败: {str(e)}")
            return {}
    
    def _detect_anomalies(self, waveform_data: Dict[str, Any]) -> Dict[str, Any]:
        """异常检测"""
        try:
            results = {}
            
            for signal_name, signal_data in waveform_data.items():
                if isinstance(signal_data, list) and len(signal_data) > 0:
                    values = np.array(signal_data)
                    
                    anomalies = []
                    
                    # 检测突变
                    diff = np.diff(values)
                    threshold = 3 * np.std(diff)
                    sudden_changes = np.where(np.abs(diff) > threshold)[0]
                    
                    if len(sudden_changes) > 0:
                        anomalies.append({
                            "type": "sudden_change",
                            "count": len(sudden_changes),
                            "positions": sudden_changes.tolist()[:10]  # 最多返回10个位置
                        })
                    
                    # 检测异常值
                    mean_val = np.mean(values)
                    std_val = np.std(values)
                    outliers = np.where(np.abs(values - mean_val) > 3 * std_val)[0]
                    
                    if len(outliers) > 0:
                        anomalies.append({
                            "type": "outlier",
                            "count": len(outliers),
                            "positions": outliers.tolist()[:10]
                        })
                    
                    # 检测饱和
                    max_val = np.max(values)
                    min_val = np.min(values)
                    saturation_high = np.where(values >= 0.95 * max_val)[0]
                    saturation_low = np.where(values <= 0.95 * min_val)[0]
                    
                    if len(saturation_high) > len(values) * 0.01:  # 超过1%的点饱和
                        anomalies.append({
                            "type": "saturation_high",
                            "count": len(saturation_high),
                            "percentage": float(len(saturation_high) / len(values) * 100)
                        })
                    
                    if len(saturation_low) > len(values) * 0.01:
                        anomalies.append({
                            "type": "saturation_low",
                            "count": len(saturation_low),
                            "percentage": float(len(saturation_low) / len(values) * 100)
                        })
                    
                    results[signal_name] = {
                        "total_anomalies": len(anomalies),
                        "anomaly_details": anomalies
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"异常检测失败: {str(e)}")
            return {}
    
    def _format_analysis_results(self, analysis_results: Dict[str, Any]) -> str:
        """格式化分析结果"""
        output_lines = ["波形分析结果:"]
        
        # 基础统计
        basic_stats = analysis_results.get("basic_stats", {})
        if basic_stats:
            output_lines.append("\n基础统计:")
            for signal, stats in basic_stats.items():
                output_lines.append(f"  {signal}:")
                output_lines.append(f"    RMS值: {stats.get('rms', 0):.3f}")
                output_lines.append(f"    峰峰值: {stats.get('peak_to_peak', 0):.3f}")
                output_lines.append(f"    标准差: {stats.get('std', 0):.3f}")
        
        # 频域分析
        frequency = analysis_results.get("frequency", {})
        if frequency:
            output_lines.append("\n频域分析:")
            for signal, freq_data in frequency.items():
                output_lines.append(f"  {signal}:")
                output_lines.append(f"    主频率: {freq_data.get('dominant_frequency', 0):.2f} Hz")
                output_lines.append(f"    频谱重心: {freq_data.get('spectral_centroid', 0):.4f}")
        
        # 谐波分析
        harmonics = analysis_results.get("harmonics", {})
        if harmonics:
            output_lines.append("\n谐波分析:")
            for signal, harmonic_data in harmonics.items():
                output_lines.append(f"  {signal}:")
                output_lines.append(f"    总谐波失真: {harmonic_data.get('total_thd_percent', 0):.2f}%")
                
                signal_harmonics = harmonic_data.get("harmonics", {})
                for harmonic_name, harmonic_info in signal_harmonics.items():
                    output_lines.append(f"    {harmonic_name}: {harmonic_info.get('thd_percent', 0):.2f}%")
        
        # 异常检测
        anomalies = analysis_results.get("anomalies", {})
        if anomalies:
            output_lines.append("\n异常检测:")
            for signal, anomaly_data in anomalies.items():
                total_anomalies = anomaly_data.get("total_anomalies", 0)
                if total_anomalies > 0:
                    output_lines.append(f"  {signal}: 检测到 {total_anomalies} 种异常")
                    
                    for anomaly in anomaly_data.get("anomaly_details", []):
                        anomaly_type = anomaly.get("type", "unknown")
                        count = anomaly.get("count", 0)
                        output_lines.append(f"    {anomaly_type}: {count} 个")
                else:
                    output_lines.append(f"  {signal}: 未检测到异常")
        
        return "\n".join(output_lines)
    
    async def _arun(self, waveform_input: str) -> str:
        """异步执行波形分析"""
        return self._run(waveform_input)
