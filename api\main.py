"""
FastAPI主应用

故障分析智能助手API服务
"""

import os
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from loguru import logger

from .routers import fault_analysis, equipment, knowledge, upload
from .models import SystemStatus
from core.fault_analyzer import FaultAnalyzer
from core.equipment_manager import EquipmentManager
from core.config_manager import get_config
from retriever.knowledge_base import KnowledgeBase


# 全局变量存储系统组件
system_components = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("正在启动故障分析智能助手服务...")
    
    try:
        # 获取配置管理器
        config_manager = get_config()

        # 初始化知识库
        logger.info("初始化知识库...")
        knowledge_base = KnowledgeBase()
        system_components["knowledge_base"] = knowledge_base

        # 初始化设备管理器
        logger.info("初始化设备管理器...")
        equipment_manager = EquipmentManager()
        system_components["equipment_manager"] = equipment_manager

        # 初始化故障分析器
        logger.info("初始化故障分析器...")
        fault_analyzer = FaultAnalyzer()
        system_components["fault_analyzer"] = fault_analyzer

        # 存储配置管理器
        system_components["config"] = config_manager
        
        logger.info("系统初始化完成")
        
    except Exception as e:
        logger.error(f"系统初始化失败: {str(e)}")
        raise
    
    yield
    
    # 关闭时清理
    logger.info("正在关闭故障分析智能助手服务...")
    system_components.clear()


# 创建FastAPI应用
app = FastAPI(
    title="故障分析智能助手",
    description="基于DeepSeek LLM的电力系统故障分析智能助手",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
config_manager = get_config()
app.add_middleware(
    CORSMiddleware,
    allow_origins=config_manager.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 静态文件服务
if not os.path.exists("static"):
    os.makedirs("static")
app.mount("/static", StaticFiles(directory="static"), name="static")

# 上传文件目录
if not os.path.exists("uploads"):
    os.makedirs("uploads")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 注册路由
app.include_router(fault_analysis.router, prefix="/api/v1/fault", tags=["故障分析"])
app.include_router(equipment.router, prefix="/api/v1/equipment", tags=["设备管理"])
app.include_router(knowledge.router, prefix="/api/v1/knowledge", tags=["知识库"])
app.include_router(upload.router, prefix="/api/v1/upload", tags=["文件上传"])


@app.get("/", response_model=dict)
async def root():
    """根路径"""
    return {
        "message": "故障分析智能助手API服务",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/api/v1/health", response_model=dict)
async def health_check():
    """健康检查"""
    try:
        # 检查系统组件状态
        status = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "components": {}
        }
        
        # 检查知识库
        if "knowledge_base" in system_components:
            try:
                kb = system_components["knowledge_base"]
                stats = kb.get_stats()
                status["components"]["knowledge_base"] = {
                    "status": "healthy",
                    "stats": stats
                }
            except Exception as e:
                status["components"]["knowledge_base"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        # 检查设备管理器
        if "equipment_manager" in system_components:
            try:
                em = system_components["equipment_manager"]
                stats = em.get_equipment_statistics()
                status["components"]["equipment_manager"] = {
                    "status": "healthy",
                    "stats": stats
                }
            except Exception as e:
                status["components"]["equipment_manager"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        # 检查故障分析器
        if "fault_analyzer" in system_components:
            status["components"]["fault_analyzer"] = {
                "status": "healthy"
            }
        
        return status
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@app.get("/api/v1/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    try:
        # 获取系统组件状态
        components_status = {}
        
        if "knowledge_base" in system_components:
            kb = system_components["knowledge_base"]
            stats = kb.get_stats()
            components_status["knowledge_base"] = {
                "status": "active",
                "document_count": stats.get("total_documents", 0),
                "image_count": stats.get("total_images", 0)
            }
        
        if "equipment_manager" in system_components:
            em = system_components["equipment_manager"]
            stats = em.get_equipment_statistics()
            components_status["equipment_manager"] = {
                "status": "active",
                "total_equipment": stats.get("total_equipment", 0),
                "maintenance_due": stats.get("maintenance_due", 0)
            }
        
        if "fault_analyzer" in system_components:
            components_status["fault_analyzer"] = {
                "status": "active"
            }
        
        return SystemStatus(
            service_name="故障分析智能助手",
            version="1.0.0",
            status="running",
            uptime="运行中",
            components=components_status
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


def get_system_component(component_name: str):
    """获取系统组件"""
    component = system_components.get(component_name)
    if not component:
        raise HTTPException(
            status_code=503, 
            detail=f"系统组件 {component_name} 不可用"
        )
    return component


# 导出获取组件的函数供路由使用
app.get_system_component = get_system_component


if __name__ == "__main__":
    import uvicorn

    # 获取配置
    config = get_config()

    # 配置日志
    logger.add("logs/api.log", rotation="1 day", retention="30 days")

    # 启动服务
    uvicorn.run(
        "api.main:app",
        host=config.get('server.host', '0.0.0.0'),
        port=config.get('server.port', 5002),
        reload=config.get('server.reload', True),
        log_level="info"
    )
