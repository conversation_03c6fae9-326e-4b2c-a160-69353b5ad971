#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek完整修复测试
验证DeepSeek-R1和DeepSeek-V3的流式输出和自然语言格式
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deepseek_r1_streaming():
    """测试DeepSeek-R1流式输出和自然语言格式"""
    print("🧪 测试DeepSeek-R1流式输出和自然语言格式")
    
    try:
        # 等待服务器启动
        time.sleep(2)
        
        # 测试健康检查
        health_response = requests.get("http://localhost:5002/health", timeout=10)
        if health_response.status_code != 200:
            print("❌ Web服务器未启动")
            return False
        
        print("✅ Web服务器运行正常")
        
        # 测试DeepSeek-R1流式分析
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象，请分析故障原因",
            "thinking_mode": True
        }
        
        print(f"🔍 发送DeepSeek-R1测试请求...")
        
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        if response.status_code != 200:
            print(f"❌ DeepSeek-R1请求失败: {response.status_code}")
            return False
        
        # 分析流式响应
        reasoning_parts = []
        final_parts = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if chunk_data.get('type') == 'reasoning':
                            content = chunk_data.get('content', '')
                            reasoning_parts.append(content)
                            if len(reasoning_parts) <= 3:
                                print(f"🧠 推理部分 {len(reasoning_parts)}: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'final':
                            content = chunk_data.get('content', '')
                            final_parts.append(content)
                            if len(final_parts) <= 3:
                                print(f"📋 最终部分 {len(final_parts)}: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'complete':
                            print("🏁 DeepSeek-R1分析完成")
                            break
                        
                        elif chunk_data.get('type') == 'error':
                            print(f"❌ 分析错误: {chunk_data.get('message')}")
                            return False
                        
                        # 限制测试时间
                        if chunk_count > 50:
                            print("⏰ 达到测试限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        continue
        
        # 验证结果
        success = True
        
        if reasoning_parts:
            total_reasoning = ''.join(reasoning_parts)
            print(f"✅ 推理过程: {len(reasoning_parts)} 部分, 总长度: {len(total_reasoning)} 字符")
            
            # 检查自然语言格式
            has_natural_language = any(
                marker in total_reasoning for marker in ["分析", "判断", "认为", "可以", "需要", "建议"]
            )
            print(f"   自然语言特征: {'✅ 是' if has_natural_language else '❌ 否'}")
        else:
            print("❌ 没有收到推理内容")
            success = False
        
        if final_parts:
            total_final = ''.join(final_parts)
            print(f"✅ 最终结果: {len(final_parts)} 部分, 总长度: {len(total_final)} 字符")
            
            # 检查自然语言格式
            has_structured_format = any(
                marker in total_final for marker in ["1.", "2.", "3.", "**", "##"]
            )
            has_natural_flow = any(
                marker in total_final for marker in ["根据", "从", "结合", "因此", "建议"]
            )
            
            print(f"   结构化格式: {'❌ 有' if has_structured_format else '✅ 无'}")
            print(f"   自然语言流畅性: {'✅ 是' if has_natural_flow else '❌ 否'}")
            
            if has_structured_format:
                success = False
                print("❌ 最终结果仍包含结构化格式，需要进一步优化")
        else:
            print("❌ 没有收到最终内容")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ DeepSeek-R1测试失败: {e}")
        return False

def test_deepseek_v3_streaming():
    """测试DeepSeek-V3流式输出"""
    print("\n🧪 测试DeepSeek-V3流式输出")
    
    try:
        # 测试DeepSeek-V3流式分析
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象，请分析故障原因",
            "thinking_mode": False
        }
        
        print(f"🔍 发送DeepSeek-V3测试请求...")
        
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        if response.status_code != 200:
            print(f"❌ DeepSeek-V3请求失败: {response.status_code}")
            return False
        
        # 分析流式响应
        final_parts = []
        reasoning_parts = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if chunk_data.get('type') == 'reasoning':
                            content = chunk_data.get('content', '')
                            reasoning_parts.append(content)
                            print(f"⚠️ DeepSeek-V3不应收到推理内容: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'final':
                            content = chunk_data.get('content', '')
                            final_parts.append(content)
                            if len(final_parts) <= 5:
                                print(f"🤖 V3实时输出 {len(final_parts)}: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'complete':
                            print("🏁 DeepSeek-V3分析完成")
                            break
                        
                        elif chunk_data.get('type') == 'error':
                            print(f"❌ 分析错误: {chunk_data.get('message')}")
                            return False
                        
                        # 限制测试时间
                        if chunk_count > 50:
                            print("⏰ 达到测试限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        continue
        
        # 验证结果
        success = True
        
        if reasoning_parts:
            print(f"⚠️ DeepSeek-V3不应有推理内容，但收到了 {len(reasoning_parts)} 部分")
            # 这不算错误，因为阿里云API可能返回reasoning_content
        
        if final_parts:
            total_final = ''.join(final_parts)
            print(f"✅ V3实时输出: {len(final_parts)} 部分, 总长度: {len(total_final)} 字符")
            
            # 检查自然语言格式
            has_natural_flow = any(
                marker in total_final for marker in ["根据", "从", "结合", "因此", "建议", "分析"]
            )
            has_streaming_effect = len(final_parts) > 1
            
            print(f"   自然语言流畅性: {'✅ 是' if has_natural_flow else '❌ 否'}")
            print(f"   流式输出效果: {'✅ 是' if has_streaming_effect else '❌ 否'}")
            
            if not has_natural_flow or not has_streaming_effect:
                success = False
        else:
            print("❌ 没有收到V3输出内容")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ DeepSeek-V3测试失败: {e}")
        return False

def test_prompt_templates():
    """测试提示词模板优化"""
    print("\n🧪 测试提示词模板优化")
    
    try:
        from langchain_modules.prompts.prompt_manager import PromptManager
        
        # 创建提示词管理器
        config = {"prompts": {"templates": {}}}
        prompt_manager = PromptManager(config)
        
        # 测试DeepSeek故障分析模板
        template = prompt_manager.get_template("deepseek_fault_analysis")
        
        if not template:
            print("❌ 无法获取DeepSeek故障分析模板")
            return False
        
        template_str = template.template
        
        # 检查自然语言格式要求
        checks = [
            ("自然语言", "自然语言" in template_str or "连贯" in template_str),
            ("避免编号", "编号" in template_str or "列表" in template_str),
            ("专业分析", "专业" in template_str and "分析" in template_str),
            ("thinking标签", "<thinking>" in template_str and "</thinking>" in template_str)
        ]
        
        success = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"   {check_name}: {status}")
            if not check_result:
                success = False
        
        print(f"✅ 模板长度: {len(template_str)} 字符")
        return success
        
    except Exception as e:
        print(f"❌ 提示词模板测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 DeepSeek完整修复测试")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    print("第一步：测试DeepSeek-R1流式输出和自然语言格式")
    r1_result = test_deepseek_r1_streaming()
    test_results.append(("DeepSeek-R1流式+自然语言", r1_result))
    
    print("\n第二步：测试DeepSeek-V3流式输出")
    v3_result = test_deepseek_v3_streaming()
    test_results.append(("DeepSeek-V3流式输出", v3_result))
    
    print("\n第三步：测试提示词模板优化")
    template_result = test_prompt_templates()
    test_results.append(("提示词模板优化", template_result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！DeepSeek完整修复成功！")
        print("\n📋 修复成果:")
        print("✅ DeepSeek-R1: 流式输出 + 思考过程分离 + 自然语言最终结果")
        print("✅ DeepSeek-V3: 实时流式输出 + 自然语言分析")
        print("✅ 提示词模板: 优化为自然语言格式")
        print("✅ 前端界面: 支持两种模型的不同显示方式")
        
        print("\n🔧 使用指南:")
        print("1. DeepSeek-R1: 显示完整思考过程 + 自然语言结论")
        print("2. DeepSeek-V3: 实时流式分析，无思考过程")
        print("3. 两种模型都支持实时显示，用户体验一致")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return all_passed

if __name__ == "__main__":
    main()
