"""
知识库API路由
"""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File
from loguru import logger
import os
import shutil

from ..models import (
    KnowledgeSearchRequest,
    KnowledgeSearchResponse,
    DocumentQARequest,
    DocumentQAResponse,
    BaseResponse
)


router = APIRouter()


def get_knowledge_base():
    """获取知识库依赖"""
    from ..main import app
    return app.get_system_component("knowledge_base")


@router.post("/search", response_model=KnowledgeSearchResponse)
async def search_knowledge(
    request: KnowledgeSearchRequest,
    knowledge_base=Depends(get_knowledge_base)
):
    """
    知识库搜索接口
    
    在知识库中搜索相关文档和图像
    """
    try:
        logger.info(f"知识库搜索: {request.query}, 类型: {request.search_type}")
        
        # 执行搜索
        results = knowledge_base.search(
            query=request.query,
            search_type=request.search_type,
            top_k=request.top_k
        )
        
        # 计算总结果数
        total_results = 0
        if results.get("results"):
            search_results = results["results"]
            if isinstance(search_results, dict):
                total_results = len(search_results.get("text", [])) + len(search_results.get("images", []))
            elif isinstance(search_results, list):
                total_results = len(search_results)
        
        return KnowledgeSearchResponse(
            success=True,
            message=f"搜索完成，找到 {total_results} 个结果",
            results=results,
            query=request.query,
            search_type=request.search_type,
            total_results=total_results
        )
        
    except Exception as e:
        logger.error(f"知识库搜索错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"知识库搜索失败: {str(e)}")


@router.get("/search", response_model=KnowledgeSearchResponse)
async def search_knowledge_get(
    query: str = Query(..., description="搜索查询"),
    search_type: str = Query("multimodal", description="搜索类型"),
    top_k: int = Query(5, description="返回结果数量"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    知识库搜索接口 (GET方法)
    
    使用GET方法进行简单的知识库搜索
    """
    try:
        logger.info(f"知识库搜索 (GET): {query}, 类型: {search_type}")
        
        # 执行搜索
        results = knowledge_base.search(
            query=query,
            search_type=search_type,
            top_k=top_k
        )
        
        # 计算总结果数
        total_results = 0
        if results.get("results"):
            search_results = results["results"]
            if isinstance(search_results, dict):
                total_results = len(search_results.get("text", [])) + len(search_results.get("images", []))
            elif isinstance(search_results, list):
                total_results = len(search_results)
        
        return KnowledgeSearchResponse(
            success=True,
            message=f"搜索完成，找到 {total_results} 个结果",
            results=results,
            query=query,
            search_type=search_type,
            total_results=total_results
        )
        
    except Exception as e:
        logger.error(f"知识库搜索错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"知识库搜索失败: {str(e)}")


@router.post("/qa", response_model=DocumentQAResponse)
async def knowledge_qa(
    request: DocumentQARequest,
    knowledge_base=Depends(get_knowledge_base)
):
    """
    知识库问答接口
    
    基于知识库内容回答用户问题
    """
    try:
        logger.info(f"知识库问答: {request.question}")
        
        # 首先搜索相关文档
        search_results = knowledge_base.search(
            query=request.question,
            search_type=request.search_type,
            top_k=request.context_limit
        )
        
        # 提取文档内容用于回答
        context_documents = []
        if search_results.get("results"):
            results = search_results["results"]
            
            # 处理文本结果
            text_results = results.get("text", [])
            for result in text_results:
                context_documents.append({
                    "type": "text",
                    "content": result.get("content", ""),
                    "source": result.get("metadata", {}).get("source", "unknown"),
                    "score": result.get("score", 0)
                })
            
            # 处理图像结果
            image_results = results.get("images", [])
            for result in image_results:
                if result.get("ocr_text"):
                    context_documents.append({
                        "type": "image",
                        "content": result.get("ocr_text", ""),
                        "source": result.get("filename", "unknown"),
                        "score": result.get("score", 0)
                    })
        
        # 生成回答（这里需要集成LLM）
        if context_documents:
            # 构建上下文
            context = "\n\n".join([
                f"[来源: {doc['source']}]\n{doc['content'][:500]}..."
                for doc in context_documents[:3]
            ])
            
            # 简化的回答生成（实际应该使用LLM）
            answer = f"基于知识库中的相关文档，针对您的问题：{request.question}\n\n"
            answer += "相关信息如下：\n" + context[:1000] + "..."
            
            # 提取引用
            references = [
                {
                    "type": doc["type"],
                    "source": doc["source"],
                    "score": doc["score"],
                    "content_preview": doc["content"][:100] + "..."
                }
                for doc in context_documents
            ]
        else:
            answer = "抱歉，我在知识库中没有找到与您的问题相关的信息。"
            references = []
        
        return DocumentQAResponse(
            success=True,
            message="问答完成",
            question=request.question,
            answer=answer,
            references=references,
            confidence=0.8 if context_documents else 0.1
        )
        
    except Exception as e:
        logger.error(f"知识库问答错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"知识库问答失败: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_knowledge_stats(
    knowledge_base=Depends(get_knowledge_base)
):
    """
    获取知识库统计信息
    
    返回知识库的统计数据
    """
    try:
        logger.info("获取知识库统计信息")
        
        # 获取统计信息
        stats = knowledge_base.get_stats()
        
        return {
            "success": True,
            "message": "统计信息获取成功",
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"获取知识库统计信息错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取知识库统计信息失败: {str(e)}")


@router.post("/documents/add", response_model=BaseResponse)
async def add_document(
    file: UploadFile = File(...),
    metadata: str = Query(None, description="文档元数据 (JSON格式)"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    添加文档到知识库
    
    上传并添加新文档到知识库
    """
    try:
        logger.info(f"添加文档到知识库: {file.filename}")
        
        # 保存上传的文件
        upload_dir = "uploads/documents"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 解析元数据
        doc_metadata = {}
        if metadata:
            import json
            try:
                doc_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                logger.warning(f"无效的元数据格式: {metadata}")
        
        # 添加文档到知识库
        success = knowledge_base.add_document(file_path, doc_metadata)
        
        if success:
            return BaseResponse(
                success=True,
                message=f"文档 {file.filename} 已成功添加到知识库"
            )
        else:
            # 删除上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=400, detail="文档添加失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加文档到知识库错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加文档失败: {str(e)}")


@router.post("/images/add", response_model=BaseResponse)
async def add_image(
    file: UploadFile = File(...),
    metadata: str = Query(None, description="图像元数据 (JSON格式)"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    添加图像到知识库
    
    上传并添加新图像到知识库
    """
    try:
        logger.info(f"添加图像到知识库: {file.filename}")
        
        # 检查文件类型
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="只支持图像文件")
        
        # 保存上传的文件
        upload_dir = "uploads/images"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 解析元数据
        img_metadata = {}
        if metadata:
            import json
            try:
                img_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                logger.warning(f"无效的元数据格式: {metadata}")
        
        # 添加图像到知识库
        success = knowledge_base.add_image(file_path, img_metadata)
        
        if success:
            return BaseResponse(
                success=True,
                message=f"图像 {file.filename} 已成功添加到知识库"
            )
        else:
            # 删除上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=400, detail="图像添加失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加图像到知识库错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加图像失败: {str(e)}")


@router.post("/rebuild", response_model=BaseResponse)
async def rebuild_knowledge_base(
    knowledge_base=Depends(get_knowledge_base)
):
    """
    重建知识库索引
    
    重新构建知识库的向量索引
    """
    try:
        logger.info("开始重建知识库索引")
        
        # 重建知识库
        success = knowledge_base.build_knowledge_base()
        
        if success:
            return BaseResponse(
                success=True,
                message="知识库索引重建完成"
            )
        else:
            raise HTTPException(status_code=500, detail="知识库索引重建失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重建知识库索引错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重建知识库索引失败: {str(e)}")


@router.get("/documents/list", response_model=Dict[str, Any])
async def list_documents(
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    获取文档列表
    
    返回知识库中的文档列表
    """
    try:
        logger.info(f"获取文档列表，limit: {limit}, offset: {offset}")
        
        # 这里需要实现文档列表功能
        # 目前返回示例数据
        documents = [
            {
                "id": "doc_001",
                "filename": "设备手册.pdf",
                "path": "data/documents/设备手册.pdf",
                "size": 1024000,
                "upload_time": "2024-01-01T00:00:00Z",
                "metadata": {"type": "manual", "category": "equipment"}
            },
            {
                "id": "doc_002", 
                "filename": "故障案例.docx",
                "path": "data/documents/故障案例.docx",
                "size": 512000,
                "upload_time": "2024-01-02T00:00:00Z",
                "metadata": {"type": "case", "category": "fault"}
            }
        ]
        
        # 分页
        total_count = len(documents)
        documents = documents[offset:offset + limit]
        
        return {
            "success": True,
            "message": f"找到 {total_count} 个文档",
            "documents": documents,
            "total_count": total_count
        }
        
    except Exception as e:
        logger.error(f"获取文档列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@router.get("/images/list", response_model=Dict[str, Any])
async def list_images(
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    获取图像列表
    
    返回知识库中的图像列表
    """
    try:
        logger.info(f"获取图像列表，limit: {limit}, offset: {offset}")
        
        # 这里需要实现图像列表功能
        # 目前返回示例数据
        images = [
            {
                "id": "img_001",
                "filename": "设备照片1.jpg",
                "path": "data/images/设备照片1.jpg",
                "size": 2048000,
                "upload_time": "2024-01-01T00:00:00Z",
                "ocr_text": "变压器铭牌信息...",
                "metadata": {"type": "equipment", "location": "主变区域"}
            },
            {
                "id": "img_002",
                "filename": "故障现象.png", 
                "path": "data/images/故障现象.png",
                "size": 1536000,
                "upload_time": "2024-01-02T00:00:00Z",
                "ocr_text": "故障录波图...",
                "metadata": {"type": "fault", "equipment": "断路器"}
            }
        ]
        
        # 分页
        total_count = len(images)
        images = images[offset:offset + limit]
        
        return {
            "success": True,
            "message": f"找到 {total_count} 个图像",
            "images": images,
            "total_count": total_count
        }
        
    except Exception as e:
        logger.error(f"获取图像列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图像列表失败: {str(e)}")
