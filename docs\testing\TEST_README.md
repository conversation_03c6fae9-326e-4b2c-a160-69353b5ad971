# 测试文档 - 故障分析智能助手系统

## 概述

本目录包含故障分析智能助手系统的所有测试代码，采用分层测试架构，确保系统的可靠性和稳定性。

## 目录结构

```
test/
├── __init__.py                 # 测试模块初始化
├── run_tests.py               # 统一测试运行器
├── pytest.ini                # pytest配置文件（项目根目录）
├── README.md                  # 本文档
├── CLEANUP_REPORT.md          # 测试文件清理报告
│
├── api/                       # API接口测试
│   ├── __init__.py
│   ├── test_api.py           # 综合故障分析API测试
│   ├── test_api_with_fallback.py  # 带回退机制的API测试
│   ├── test_new_api_config.py     # 新API配置测试
│   ├── test_real_data_api.py      # 真实数据API测试
│   └── test_simple_api.py         # 简单API测试
│
├── integration/               # 集成测试
│   ├── __init__.py
│   ├── test_complete_image_system.py    # 完整图像系统测试
│   ├── test_comprehensive_upload.py     # 综合上传功能测试
│   └── test_final_deepseek_integration.py  # DeepSeek集成测试
│
├── unit/                      # 单元测试
│   ├── __init__.py
│   ├── test_fallback_only.py         # 回退机制测试
│   ├── test_image_upload.py          # 图片上传测试
│   ├── test_intelligent_retrieval.py # 智能检索测试
│   ├── test_no_web_search.py         # 无网络搜索测试
│   ├── test_paths.py                 # 路径处理测试
│   ├── test_pure_text_output.py      # 纯文本输出测试
│   └── test_search_comprehensive.py  # 综合搜索测试
│
├── web/                       # Web界面测试
│   ├── __init__.py
│   ├── test_flask_debug.py           # Flask调试测试
│   ├── test_form_integration.py      # 表单集成测试
│   ├── test_frontend_deepseek_style.py  # DeepSeek风格前端测试
│   ├── test_javascript_fix.py        # JavaScript修复测试
│   ├── test_page_display.py          # 页面显示测试
│   └── test_seven_steps_display.py   # 七步骤显示测试
│
├── html/                      # HTML测试页面
│   ├── __init__.py
│   ├── api_test_page.html            # API测试页面
│   ├── debug_frontend.html           # 前端调试页面
│   ├── debug_search.html             # 搜索调试页面
│   ├── frontend_validation.html      # 前端验证页面
│   ├── simple_debug.html             # 简单调试页面
│   ├── test_batch_upload.html        # 批量上传测试页面
│   ├── test_content_formatting.html  # 内容格式化测试页面
│   ├── test_form_debug.html          # 表单调试页面
│   ├── test_form_fields.html         # 表单字段测试页面
│   ├── test_frontend_fix.html        # 前端修复测试页面
│   ├── test_image_upload.html        # 图片上传测试页面
│   ├── test_js_fixes.html            # JS修复测试页面
│   ├── test_search_page.html         # 搜索页面测试
│   ├── ui_test_frontend.html         # UI前端测试页面
│   └── ui_test_tabs.html             # UI标签页测试
│
├── performance/               # 性能测试
│   └── __init__.py
│
└── utils/                     # 测试工具
    └── __init__.py
```

## 测试类型说明

### 1. API测试 (api/)
测试系统的REST API接口，包括：
- 故障分析API
- 文件上传API
- 知识库搜索API
- DeepSeek集成API

### 2. 集成测试 (integration/)
测试系统各组件之间的集成，包括：
- 完整的图像处理流程
- 文件上传和处理流程
- AI模型集成测试

### 3. 单元测试 (unit/)
测试单个功能模块，包括：
- 搜索功能
- 文件处理
- 数据检索
- 文本处理

### 4. Web测试 (web/)
测试Web界面和前端功能，包括：
- 页面渲染
- 表单交互
- JavaScript功能
- 用户界面

### 5. HTML测试页面 (html/)
用于手动测试和调试的HTML页面，包括：
- 各种功能的测试界面
- 调试工具页面
- 验证页面

## 运行测试

### 使用统一测试运行器

```bash
# 运行所有测试
python test/run_tests.py

# 运行特定类型的测试
python test/run_tests.py --type api        # API测试
python test/run_tests.py --type unit       # 单元测试
python test/run_tests.py --type integration # 集成测试
python test/run_tests.py --type web        # Web测试

# 详细输出
python test/run_tests.py --verbose
```

### 使用pytest

```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest test/api/          # API测试
pytest test/unit/         # 单元测试
pytest test/integration/  # 集成测试
pytest test/web/          # Web测试

# 运行特定测试文件
pytest test/unit/test_search_comprehensive.py

# 使用标记运行测试
pytest -m unit            # 运行单元测试标记的测试
pytest -m integration     # 运行集成测试标记的测试
pytest -m api             # 运行API测试标记的测试
```

### 直接运行单个测试文件

```bash
# 直接运行Python测试文件
python test/unit/test_image_upload.py
python test/api/test_simple_api.py
```

## 测试配置

### pytest配置 (pytest.ini)
- 测试发现路径：`test/`
- 测试文件模式：`test_*.py`
- 输出格式：详细模式，短格式回溯
- 标记定义：unit, integration, api, web, performance, slow, smoke

### 环境要求
- Python 3.8+
- pytest 6.0+
- requests
- 其他依赖见项目根目录的requirements.txt

## 测试最佳实践

### 1. 测试文件命名
- 测试文件以 `test_` 开头
- 测试函数以 `test_` 开头
- 测试类以 `Test` 开头

### 2. 测试组织
- 按功能模块组织测试
- 单元测试放在 `unit/` 目录
- 集成测试放在 `integration/` 目录
- API测试放在 `api/` 目录
- Web测试放在 `web/` 目录

### 3. 测试标记
使用pytest标记对测试进行分类：
```python
import pytest

@pytest.mark.unit
def test_unit_function():
    pass

@pytest.mark.integration
def test_integration_function():
    pass

@pytest.mark.slow
def test_slow_function():
    pass
```

### 4. 测试数据
- 使用临时文件进行文件操作测试
- 避免硬编码测试数据
- 清理测试产生的临时文件

## 持续集成

测试可以集成到CI/CD流水线中：

```yaml
# GitHub Actions示例
- name: Run tests
  run: |
    python test/run_tests.py --type unit
    python test/run_tests.py --type integration
```

## 故障排除

### 常见问题
1. **服务器连接失败**：确保测试服务器正在运行
2. **超时错误**：增加测试超时时间
3. **依赖缺失**：安装所需的Python包

### 调试技巧
1. 使用 `--verbose` 参数获取详细输出
2. 单独运行失败的测试文件
3. 检查测试日志和错误信息
4. 使用HTML测试页面进行手动验证

## 贡献指南

### 添加新测试
1. 选择合适的测试目录
2. 遵循命名规范
3. 添加适当的测试标记
4. 更新相关文档

### 修改现有测试
1. 确保向后兼容性
2. 更新测试文档
3. 运行相关测试验证修改

---

**文档版本：** v1.0  
**最后更新：** 2025-07-02  
**维护者：** 故障分析智能助手开发团队
