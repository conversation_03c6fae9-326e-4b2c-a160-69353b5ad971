/* 故障分析智能助手 - 主样式文件 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.375rem;
}

/* 主要页面标签页内容 */
.main-tab-content {
    display: none;
}

.main-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

/* Bootstrap内部标签页内容（故障分析步骤） */
.tab-content {
    /* Bootstrap默认样式，用于故障分析步骤 */
}

/* Bootstrap标签页面板显示控制 */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane.show.active {
    display: block;
}



@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.card-header h5, .card-header h6 {
    margin: 0;
    color: #495057;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
}

/* 分析结果样式 */
#analysis-results .border {
    border-color: #dee2e6 !important;
}

#analysis-results h6 {
    color: #0d6efd;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

#fault-causes, #fault-logic, #recommendations {
    background-color: #f8f9fa !important;
    min-height: 100px;
    white-space: pre-wrap;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 历史记录样式 */
#analysis-history .list-group-item {
    border: none;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
}

#analysis-history .list-group-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
}

#analysis-history .list-group-item.active {
    background-color: #e7f1ff;
    border-left-color: #0d6efd;
}

/* 设备管理样式 */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 状态标签 */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-running {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-standby {
    background-color: #fff3cd;
    color: #664d03;
}

.status-maintenance {
    background-color: #f8d7da;
    color: #721c24;
}

.status-fault {
    background-color: #f5c2c7;
    color: #842029;
}

/* 知识库搜索结果 */
.search-result-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fff;
    transition: all 0.2s ease;
}

.search-result-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.search-result-title {
    font-weight: 600;
    color: #0d6efd;
    margin-bottom: 0.5rem;
}

.search-result-content {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
}

.search-result-meta {
    font-size: 0.8rem;
    color: #adb5bd;
    margin-top: 0.5rem;
}

/* 文件上传样式 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* 上传进度条 */
.upload-progress {
    margin-top: 1rem;
}

.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

/* 结果展示 */
.result-item {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.result-item-header {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.result-item-content {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

/* 警告和错误样式 */
.alert {
    border: none;
    border-radius: 0.375rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* 系统状态指示器 */
#system-status {
    font-size: 0.9rem;
}

#system-status .bi-circle-fill {
    font-size: 0.7rem;
    margin-right: 0.25rem;
}

/* 占位符样式 */
.placeholder-content {
    color: #adb5bd;
    text-align: center;
    padding: 2rem;
}

.placeholder-content i {
    opacity: 0.5;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 打印样式 */
@media print {
    .navbar, .btn, .card-header {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    body {
        background-color: #fff !important;
    }
}

/* ========== 简化的推理模式样式 ========== */

/* 简化的推理过程显示 */
.reasoning-process-container {
    margin-bottom: 20px;
}

.reasoning-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.reasoning-text {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
    margin: 0;
}

/* 简化的分析结果显示 */
.analysis-result-container {
    margin-top: 20px;
}

.analysis-content {
    font-size: 14px;
    line-height: 1.7;
    color: #333;
    white-space: pre-wrap;
}

/* ========== 设备管理增强样式 ========== */

/* 更新状态徽章样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 扩展状态样式 */
.status-offline { background-color: #e2e3e5; color: #41464b; }
.status-warning { background-color: #ffe5b4; color: #664d03; }
.status-unknown { background-color: #f8f9fa; color: #6c757d; }
.status-stopped { background-color: #e2e3e5; color: #495057; }

/* 健康状态徽章样式 */
.health-badge {
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.2rem;
}
.health-good { background-color: #d1e7dd; color: #0f5132; }
.health-fair { background-color: #fff3cd; color: #664d03; }
.health-poor { background-color: #ffe5b4; color: #664d03; }
.health-critical { background-color: #f8d7da; color: #842029; }
.health-unknown { background-color: #f8f9fa; color: #6c757d; }

/* 设备表格增强样式 */
.equipment-table {
    font-size: 0.9rem;
}
.equipment-row:hover {
    background-color: #f8f9fa;
}
.equipment-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    border-radius: 0.375rem;
}

/* 统计卡片样式 */
.stat-card {
    padding: 1rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease;
}
.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
}
.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}
.stat-percentage {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 500;
}

/* 状态和类型分解样式 */
.status-breakdown, .type-breakdown {
    max-height: 200px;
    overflow-y: auto;
}
.status-item, .type-item {
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
    margin-bottom: 0.5rem;
}
.status-item:hover, .type-item:hover {
    background-color: #e9ecef;
}

/* 参数显示样式 */
.parameters-display {
    font-size: 0.8rem;
}
.parameters-display div {
    margin-bottom: 0.2rem;
}

/* 设备详情模态框样式 */
.modal-lg {
    max-width: 900px;
}

/* 设备状态容器 */
.status-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* 设备统计面板 */
#equipment-stats-summary {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin-top: 1rem;
}

/* ========== 现代化设备管理界面样式 ========== */

/* 设备仪表板现代化 */
.equipment-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    border: none;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    padding: 2rem;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-header h4 {
    margin: 0;
    font-weight: 700;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dashboard-header p {
    margin: 0.75rem 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
    font-weight: 300;
}

.dashboard-content {
    background: white;
    padding: 2.5rem;
    min-height: 250px;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: modernSpin 1.2s linear infinite;
    margin-bottom: 1.5rem;
}

@keyframes modernSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 现代化统计卡片 */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 2rem 1.5rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    margin: 0.5rem 0;
    color: #2c3e50;
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-percentage {
    font-size: 0.9rem;
    color: #28a745;
    font-weight: 700;
    background: rgba(40, 167, 69, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    display: inline-block;
}

/* 设备列表现代化容器 */
.equipment-list-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: none;
}

.equipment-list-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-bottom: 2px solid #dee2e6;
}

.header-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.header-title h5 {
    margin: 0;
    font-weight: 700;
    font-size: 1.4rem;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.count-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.search-filter-bar {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 350px;
}

.search-input {
    width: 100%;
    padding: 1rem 1.25rem 1rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 30px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.search-icon {
    position: absolute;
    left: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.1rem;
}

.filter-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.filter-select {
    padding: 0.75rem 1.25rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 0.9rem;
    background: white;
    color: #495057;
    min-width: 140px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
}

.clear-filters-btn {
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.clear-filters-btn:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    color: #495057;
    transform: rotate(180deg) scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 现代化设备表格样式 */
.equipment-table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
    animation: fadeInUp 0.5s ease-out;
}

.equipment-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.equipment-table td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border: none;
}

/* 设备信息样式 */
.equipment-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.equipment-name {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
}

.equipment-id {
    font-size: 0.85rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
}

.equipment-model {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

/* 位置信息样式 */
.location-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.location-primary {
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-secondary {
    font-size: 0.85rem;
    color: #6c757d;
}

/* 现代化状态徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.status-running {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-standby {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.status-maintenance {
    background: linear-gradient(135deg, #fd7e14, #e83e8c);
    color: white;
}

.status-fault {
    background: linear-gradient(135deg, #dc3545, #6f42c1);
    color: white;
}

.status-offline {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

/* 运行参数样式 */
.equipment-params {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    font-size: 0.85rem;
}

.param-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
}

.param-label {
    color: #6c757d;
    font-weight: 500;
}

.param-value {
    color: #2c3e50;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

/* 操作按钮样式 */
.equipment-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-view {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.btn-status {
    background: linear-gradient(135deg, #fd7e14, #e83e8c);
    color: white;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .equipment-table {
        font-size: 0.8rem;
    }
    .stat-card {
        margin-bottom: 1rem;
        padding: 1.5rem 1rem;
    }
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
    }
    .equipment-icon {
        width: 24px;
        height: 24px;
    }
    .stat-number {
        font-size: 2rem;
    }
    .stat-icon {
        font-size: 2rem;
    }
    .dashboard-header {
        padding: 1.5rem;
    }
    .dashboard-content {
        padding: 1.5rem;
    }
    .equipment-list-header {
        padding: 1.5rem;
    }
    .search-box {
        min-width: 100%;
    }
    .search-filter-bar {
        flex-direction: column;
        align-items: stretch;
    }
    .filter-group {
        justify-content: center;
    }
}

/* 设备管理页面专用样式 */
.equipment-management .stat-item {
    padding: 0.5rem;
}

.equipment-management .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.equipment-management .stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

/* 设备状态标签 */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-running {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-standby {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.status-maintenance {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-fault {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.status-offline {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* 设备类型标签 */
.type-badge {
    background: #f8f9fa;
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    color: #495057;
    font-weight: 500;
}

/* 设备操作按钮 */
.equipment-management .btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 0.25rem;
}

.equipment-management .btn-view {
    background: #e3f2fd;
    color: #1976d2;
}

.equipment-management .btn-view:hover {
    background: #bbdefb;
}

.equipment-management .btn-edit {
    background: #fff3e0;
    color: #f57c00;
}

.equipment-management .btn-edit:hover {
    background: #ffe0b2;
}

.equipment-management .btn-delete {
    background: #ffebee;
    color: #d32f2f;
}

.equipment-management .btn-delete:hover {
    background: #ffcdd2;
}

/* 设备管理响应式设计 */
@media (max-width: 768px) {
    .equipment-management .row {
        flex-direction: column;
    }

    .equipment-management .col-md-4,
    .equipment-management .col-md-8 {
        width: 100%;
        max-width: 100%;
        margin-bottom: 1rem;
    }

    .equipment-management .table-responsive {
        font-size: 0.85rem;
    }

    .equipment-management .btn-action {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}



/* 流式思考过程样式 */
.deepseek-thinking-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
    margin-bottom: 20px;
}

.thinking-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.thinking-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.thinking-title {
    color: white;
    font-weight: 600;
    margin: 0;
}

.thinking-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    backdrop-filter: blur(10px);
}

.thinking-process-text {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.95);
}

.thinking-step {
    padding: 8px 12px;
    margin: 4px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border-left: 3px solid #28a745;
    transition: all 0.3s ease;
}

.thinking-step:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.step-indicator .badge {
    font-size: 11px;
    padding: 4px 8px;
    background-color: #007bff !important;
}

.step-content {
    flex: 1;
    color: rgba(255, 255, 255, 0.9);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate__animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.animate__fadeInUp {
    animation-name: fadeInUp;
}

/* 流式分析结果容器 */
.deepseek-result-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.result-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.result-icon {
    width: 40px;
    height: 40px;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-title {
    color: #333;
    font-weight: 600;
    margin: 0;
}
