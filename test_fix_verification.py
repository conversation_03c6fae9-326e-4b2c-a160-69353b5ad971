#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek-R1 修复验证脚本
验证核心修复功能是否正常工作
"""

import sys
import os
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_smart_split_function():
    """测试智能分离函数的核心功能"""
    print("🧪 测试智能分离函数核心功能")
    
    # 导入修复后的函数
    try:
        from ui.app import smart_split_reasoning_and_result
        print("✅ 成功导入智能分离函数")
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试标准thinking标签格式
    test_content = """<thinking>
这是一个变压器故障分析。从保护动作来看，差动保护首先动作，说明变压器内部存在不平衡电流。
结合现场观察到的套管渗油现象，我判断这是一个复合型故障。
油温监测显示68℃，超出正常范围，表明内部存在异常发热源。
色谱分析显示总烃含量2500ppm，比正常标准150ppm高出近17倍。
</thinking>

**基于上述思考的专业故障诊断报告：**

**故障前系统运行状态评估：** 故障发生前变压器运行在额定负荷的85%，系统电压稳定。

**设备技术特性核实：** 该变压器为110kV/10kV，容量31.5MVA，投运于2015年。

**保护动作机理分析：** 差动保护在0.02秒内动作，动作值为1.2倍整定值，保护动作正确。

**技术处理方案：** 建议立即停运该变压器，进行全面的绝缘电阻测试和介损测量。"""

    reasoning, final = smart_split_reasoning_and_result(test_content)
    
    # 验证分离效果
    success = True
    
    if not reasoning:
        print("❌ 推理过程为空")
        success = False
    elif "差动保护首先动作" not in reasoning:
        print("❌ 推理过程内容不正确")
        success = False
    else:
        print(f"✅ 推理过程正确提取: {len(reasoning)} 字符")
    
    if not final:
        print("❌ 最终结果为空")
        success = False
    elif "故障前系统运行状态评估" not in final:
        print("❌ 最终结果内容不正确")
        success = False
    else:
        print(f"✅ 最终结果正确提取: {len(final)} 字符")
    
    # 验证thinking标签是否被正确处理
    if "<thinking>" in final or "</thinking>" in final:
        print("❌ 最终结果中仍包含thinking标签")
        success = False
    else:
        print("✅ thinking标签正确清理")
    
    return success

def test_prompt_template():
    """测试提示词模板"""
    print("\n🧪 测试提示词模板")
    
    try:
        from langchain_modules.prompts.prompt_manager import PromptManager
        
        # 创建提示词管理器
        config = {"prompts": {"templates": {}}}
        prompt_manager = PromptManager(config)
        
        # 获取DeepSeek故障分析模板
        template = prompt_manager.get_template("deepseek_fault_analysis")
        
        if not template:
            print("❌ 无法获取提示词模板")
            return False
        
        # 检查模板内容
        template_str = template.template
        
        checks = [
            ("<thinking>", "包含thinking开始标签"),
            ("</thinking>", "包含thinking结束标签"),
            ("专业故障诊断报告", "包含专业报告要求"),
            ("故障前系统运行状态评估", "包含结构化分析要求")
        ]
        
        success = True
        for check_str, description in checks:
            if check_str in template_str:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少{description}")
                success = False
        
        print(f"✅ 模板长度: {len(template_str)} 字符")
        return success
        
    except Exception as e:
        print(f"❌ 提示词模板测试失败: {e}")
        return False

def test_thinking_tag_processing():
    """测试thinking标签处理逻辑"""
    print("\n🧪 测试thinking标签处理逻辑")
    
    # 模拟前端JavaScript的处理逻辑
    test_content = """<thinking>
这是推理过程的内容，包含专家的思考。
从技术角度分析，这个故障很复杂。
</thinking>

这是最终的分析结果内容。"""
    
    # 模拟前端清理逻辑
    cleaned_content = test_content
    cleaned_content = re.sub(r'<thinking>', '', cleaned_content, flags=re.IGNORECASE)
    cleaned_content = re.sub(r'</thinking>', '', cleaned_content, flags=re.IGNORECASE)
    cleaned_content = re.sub(r'^\d+\.\s*', '', cleaned_content, flags=re.MULTILINE)
    cleaned_content = re.sub(r'^[*-]\s*', '', cleaned_content, flags=re.MULTILINE)
    
    # 验证清理效果
    if "<thinking>" in cleaned_content or "</thinking>" in cleaned_content:
        print("❌ thinking标签未正确清理")
        return False
    else:
        print("✅ thinking标签正确清理")
    
    if "推理过程的内容" in cleaned_content:
        print("✅ 推理内容保留")
    else:
        print("❌ 推理内容丢失")
        return False
    
    if "最终的分析结果" in cleaned_content:
        print("✅ 最终结果内容保留")
    else:
        print("❌ 最终结果内容丢失")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 DeepSeek-R1 修复验证")
    print("=" * 50)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("智能分离函数", test_smart_split_function()))
    test_results.append(("提示词模板", test_prompt_template()))
    test_results.append(("thinking标签处理", test_thinking_tag_processing()))
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！DeepSeek-R1修复成功！")
        print("\n📋 修复效果:")
        print("✅ 智能分离函数能正确识别thinking标签")
        print("✅ 提示词模板包含正确的格式要求")
        print("✅ thinking标签处理逻辑正确")
        print("✅ 思考过程和最终结果能正确分离")
        
        print("\n🔧 使用说明:")
        print("1. 启动Web服务器: python ui/app.py")
        print("2. 访问 http://localhost:5002")
        print("3. 选择 DeepSeek-R1 模型")
        print("4. 输入故障描述进行分析")
        print("5. 观察思考过程和最终结果的分离显示")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    main()
