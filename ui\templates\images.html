<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片管理 - 故障诊断智能助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .image-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .image-thumbnail {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }
        .category-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
        }
        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 15px 10px 10px;
        }
        .search-filters {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-lightning-charge"></i> 故障诊断智能助手
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link active" href="/images">图片管理</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题和操作按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-images"></i> 图片管理</h2>
                <p class="text-muted">管理设备图片、技术图纸和故障记录</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="showAddImageModal()">
                    <i class="bi bi-plus-circle"></i> 添加图片
                </button>
                <button class="btn btn-outline-secondary" onclick="refreshImageList()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filters">
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">搜索图片</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search-input" 
                               placeholder="输入关键词搜索...">
                        <button class="btn btn-outline-secondary" onclick="searchImages()">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">图片类别</label>
                    <select class="form-select" id="category-filter" onchange="filterImages()">
                        <option value="">全部类别</option>
                        <option value="diagram">技术图纸</option>
                        <option value="photo">设备照片</option>
                        <option value="thermal">热成像图</option>
                        <option value="defect">缺陷图片</option>
                        <option value="maintenance">维护记录</option>
                        <option value="inspection">巡检照片</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">排序方式</label>
                    <select class="form-select" id="sort-order" onchange="sortImages()">
                        <option value="newest">最新上传</option>
                        <option value="oldest">最早上传</option>
                        <option value="name">按名称</option>
                        <option value="size">按大小</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">显示方式</label>
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-secondary active" 
                                onclick="setViewMode('grid')" id="grid-view-btn">
                            <i class="bi bi-grid-3x3"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" 
                                onclick="setViewMode('list')" id="list-view-btn">
                            <i class="bi bi-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary" id="total-images">0</h5>
                        <p class="card-text">总图片数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success" id="total-size">0 MB</h5>
                        <p class="card-text">总大小</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info" id="recent-uploads">0</h5>
                        <p class="card-text">本周上传</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning" id="categories-count">0</h5>
                        <p class="card-text">类别数量</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片列表 -->
        <div id="images-container">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载图片...</p>
            </div>
        </div>

        <!-- 分页 -->
        <nav aria-label="图片分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将在这里动态生成 -->
            </ul>
        </nav>
    </div>

    <!-- 图片详情模态框 -->
    <div class="modal fade" id="imageDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="image-detail-title">图片详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="text-center">
                                <img id="image-detail-img" class="img-fluid" style="max-height: 500px;">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div id="image-detail-info">
                                <!-- 图片详细信息将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editImage()">编辑</button>
                    <button type="button" class="btn btn-danger" onclick="deleteImage()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 引入主要的JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('图片管理页面加载完成');
            loadImageList();
        });
    </script>
</body>
</html>
