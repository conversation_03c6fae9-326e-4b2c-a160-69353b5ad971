"""
现场检查解析器

解析现场检查报告和巡检记录，提取关键信息
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from loguru import logger

from data_processing.text_processor import TextProcessor
from data_processing.ocr_processor import OCRProcessor


class InspectionParser:
    """现场检查解析器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.text_processor = TextProcessor(config.get("data_processing", {}))
        self.ocr_processor = OCRProcessor(config.get("data_processing", {}).get("ocr", {}))
        
        # 关键词模式
        self._init_patterns()
    
    def _init_patterns(self):
        """初始化解析模式"""
        # 设备状态关键词
        self.status_patterns = {
            "正常": ["正常", "良好", "无异常", "运行正常"],
            "异常": ["异常", "故障", "损坏", "破损", "锈蚀", "泄漏", "过热"],
            "需要关注": ["需要关注", "建议检查", "待观察", "轻微", "初期"]
        }
        
        # 设备类型识别
        self.equipment_patterns = {
            "变压器": ["变压器", "主变", "配变", "干变", "油变"],
            "断路器": ["断路器", "开关", "SF6开关", "真空开关"],
            "隔离开关": ["隔离开关", "刀闸", "隔离刀闸"],
            "电容器": ["电容器", "并联电容器", "串联电容器"],
            "避雷器": ["避雷器", "氧化锌避雷器", "金属氧化物避雷器"],
            "电缆": ["电缆", "电力电缆", "控制电缆"],
            "母线": ["母线", "汇流排", "软母线", "硬母线"]
        }
        
        # 缺陷类型模式
        self.defect_patterns = {
            "外观缺陷": ["锈蚀", "破损", "裂纹", "变形", "污秽"],
            "声音异常": ["异响", "放电声", "嗡嗡声", "噼啪声"],
            "温度异常": ["过热", "温升", "发热", "烫手"],
            "泄漏": ["漏油", "渗油", "气体泄漏", "SF6泄漏"],
            "绝缘问题": ["绝缘老化", "绝缘破损", "闪络", "击穿"]
        }
        
        # 时间模式
        self.time_pattern = re.compile(r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)')
        
        # 数值模式
        self.value_pattern = re.compile(r'(\d+\.?\d*)\s*([A-Za-z%℃°]+)')
    
    def parse_inspection_report(self, content: str, source_type: str = "text") -> Dict[str, Any]:
        """
        解析检查报告
        
        Args:
            content: 报告内容或图像路径
            source_type: 内容类型 ("text" 或 "image")
            
        Returns:
            解析结果字典
        """
        try:
            logger.info(f"开始解析检查报告，类型: {source_type}")
            
            # 如果是图像，先进行OCR
            if source_type == "image":
                ocr_result = self.ocr_processor.process_image(content)
                if not ocr_result.get("success"):
                    return {"success": False, "error": "OCR处理失败"}
                content = ocr_result.get("text", "")
            
            # 文本预处理
            cleaned_content = self.text_processor.clean_text(content)
            
            # 解析各个部分
            result = {
                "success": True,
                "inspection_info": self._extract_inspection_info(cleaned_content),
                "equipment_status": self._extract_equipment_status(cleaned_content),
                "defects": self._extract_defects(cleaned_content),
                "measurements": self._extract_measurements(cleaned_content),
                "recommendations": self._extract_recommendations(cleaned_content),
                "raw_content": content,
                "processed_content": cleaned_content
            }
            
            logger.info("检查报告解析完成")
            return result
            
        except Exception as e:
            logger.error(f"解析检查报告失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _extract_inspection_info(self, content: str) -> Dict[str, Any]:
        """提取检查基本信息"""
        info = {
            "date": None,
            "inspector": None,
            "location": None,
            "weather": None
        }
        
        # 提取日期
        time_matches = self.time_pattern.findall(content)
        if time_matches:
            info["date"] = time_matches[0]
        
        # 提取检查人员
        inspector_patterns = [
            r'检查人[：:]\s*([^\n\r，,。.]+)',
            r'巡检员[：:]\s*([^\n\r，,。.]+)',
            r'检查员[：:]\s*([^\n\r，,。.]+)'
        ]
        for pattern in inspector_patterns:
            match = re.search(pattern, content)
            if match:
                info["inspector"] = match.group(1).strip()
                break
        
        # 提取位置
        location_patterns = [
            r'位置[：:]\s*([^\n\r，,。.]+)',
            r'地点[：:]\s*([^\n\r，,。.]+)',
            r'站点[：:]\s*([^\n\r，,。.]+)'
        ]
        for pattern in location_patterns:
            match = re.search(pattern, content)
            if match:
                info["location"] = match.group(1).strip()
                break
        
        # 提取天气
        weather_patterns = [
            r'天气[：:]\s*([^\n\r，,。.]+)',
            r'气候[：:]\s*([^\n\r，,。.]+)'
        ]
        for pattern in weather_patterns:
            match = re.search(pattern, content)
            if match:
                info["weather"] = match.group(1).strip()
                break
        
        return info
    
    def _extract_equipment_status(self, content: str) -> List[Dict[str, Any]]:
        """提取设备状态信息"""
        equipment_status = []
        
        # 按段落分割
        paragraphs = content.split('\n')
        
        for paragraph in paragraphs:
            if not paragraph.strip():
                continue
            
            # 识别设备类型
            equipment_type = None
            for eq_type, patterns in self.equipment_patterns.items():
                if any(pattern in paragraph for pattern in patterns):
                    equipment_type = eq_type
                    break
            
            if not equipment_type:
                continue
            
            # 识别状态
            status = "未知"
            for status_type, patterns in self.status_patterns.items():
                if any(pattern in paragraph for pattern in patterns):
                    status = status_type
                    break
            
            equipment_status.append({
                "equipment_type": equipment_type,
                "status": status,
                "description": paragraph.strip(),
                "paragraph_index": len(equipment_status)
            })
        
        return equipment_status
    
    def _extract_defects(self, content: str) -> List[Dict[str, Any]]:
        """提取缺陷信息"""
        defects = []
        
        # 按句子分割
        sentences = re.split(r'[。.！!？?；;]', content)
        
        for i, sentence in enumerate(sentences):
            if not sentence.strip():
                continue
            
            # 检查是否包含缺陷关键词
            found_defects = []
            for defect_type, patterns in self.defect_patterns.items():
                for pattern in patterns:
                    if pattern in sentence:
                        found_defects.append({
                            "type": defect_type,
                            "keyword": pattern,
                            "description": sentence.strip(),
                            "sentence_index": i
                        })
            
            defects.extend(found_defects)
        
        return defects
    
    def _extract_measurements(self, content: str) -> List[Dict[str, Any]]:
        """提取测量数据"""
        measurements = []
        
        # 查找数值和单位
        value_matches = self.value_pattern.findall(content)
        
        for value, unit in value_matches:
            # 确定测量类型
            measurement_type = self._classify_measurement(unit, content)
            
            measurements.append({
                "value": float(value),
                "unit": unit,
                "type": measurement_type,
                "raw_text": f"{value}{unit}"
            })
        
        return measurements
    
    def _classify_measurement(self, unit: str, context: str) -> str:
        """分类测量类型"""
        unit_lower = unit.lower()
        
        if unit_lower in ['℃', '°c', 'c']:
            return "温度"
        elif unit_lower in ['v', 'kv', 'mv']:
            return "电压"
        elif unit_lower in ['a', 'ka', 'ma']:
            return "电流"
        elif unit_lower in ['mω', 'kω', 'ω']:
            return "电阻"
        elif unit_lower in ['%']:
            if "湿度" in context:
                return "湿度"
            else:
                return "百分比"
        elif unit_lower in ['mpa', 'kpa', 'pa']:
            return "压力"
        elif unit_lower in ['hz']:
            return "频率"
        else:
            return "其他"
    
    def _extract_recommendations(self, content: str) -> List[str]:
        """提取建议和措施"""
        recommendations = []
        
        # 建议关键词模式
        recommendation_patterns = [
            r'建议[：:]([^。.！!？?；;\n\r]+)',
            r'措施[：:]([^。.！!？?；;\n\r]+)',
            r'处理[：:]([^。.！!？?；;\n\r]+)',
            r'需要([^。.！!？?；;\n\r]*(?:检查|维修|更换|处理)[^。.！!？?；;\n\r]*)',
            r'应当([^。.！!？?；;\n\r]*(?:检查|维修|更换|处理)[^。.！!？?；;\n\r]*)'
        ]
        
        for pattern in recommendation_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                recommendation = match.strip()
                if recommendation and len(recommendation) > 3:
                    recommendations.append(recommendation)
        
        # 去重
        recommendations = list(set(recommendations))
        
        return recommendations
    
    def parse_patrol_log(self, content: str) -> Dict[str, Any]:
        """
        解析巡检日志
        
        Args:
            content: 巡检日志内容
            
        Returns:
            解析结果
        """
        try:
            logger.info("开始解析巡检日志")
            
            # 基本解析
            result = self.parse_inspection_report(content, "text")
            
            # 巡检特有信息
            patrol_info = {
                "route": self._extract_patrol_route(content),
                "duration": self._extract_patrol_duration(content),
                "checkpoints": self._extract_checkpoints(content)
            }
            
            result["patrol_info"] = patrol_info
            
            logger.info("巡检日志解析完成")
            return result
            
        except Exception as e:
            logger.error(f"解析巡检日志失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _extract_patrol_route(self, content: str) -> List[str]:
        """提取巡检路线"""
        route = []
        
        # 路线关键词
        route_patterns = [
            r'路线[：:]([^。.！!？?；;\n\r]+)',
            r'巡检路径[：:]([^。.！!？?；;\n\r]+)',
            r'检查路线[：:]([^。.！!？?；;\n\r]+)'
        ]
        
        for pattern in route_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                points = re.split(r'[→->→\s]+', match.strip())
                route.extend([point.strip() for point in points if point.strip()])
        
        return route
    
    def _extract_patrol_duration(self, content: str) -> Optional[str]:
        """提取巡检时长"""
        duration_patterns = [
            r'用时[：:]([^。.！!？?；;\n\r]+)',
            r'耗时[：:]([^。.！!？?；;\n\r]+)',
            r'时长[：:]([^。.！!？?；;\n\r]+)',
            r'(\d+)\s*小时\s*(\d+)\s*分钟?',
            r'(\d+)\s*分钟'
        ]
        
        for pattern in duration_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(0)
        
        return None
    
    def _extract_checkpoints(self, content: str) -> List[Dict[str, Any]]:
        """提取检查点信息"""
        checkpoints = []
        
        # 查找检查点标记
        checkpoint_patterns = [
            r'(\d+)[、.]\s*([^。.！!？?；;\n\r]+)',
            r'检查点\s*(\d+)[：:]([^。.！!？?；;\n\r]+)'
        ]
        
        for pattern in checkpoint_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if len(match) == 2:
                    checkpoints.append({
                        "number": match[0],
                        "description": match[1].strip(),
                        "status": self._determine_checkpoint_status(match[1])
                    })
        
        return checkpoints
    
    def _determine_checkpoint_status(self, description: str) -> str:
        """判断检查点状态"""
        for status_type, patterns in self.status_patterns.items():
            if any(pattern in description for pattern in patterns):
                return status_type
        return "未知"
    
    def generate_summary(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成检查报告摘要
        
        Args:
            parsed_data: 解析后的数据
            
        Returns:
            摘要信息
        """
        try:
            summary = {
                "total_equipment": len(parsed_data.get("equipment_status", [])),
                "total_defects": len(parsed_data.get("defects", [])),
                "status_distribution": {},
                "defect_distribution": {},
                "key_issues": [],
                "urgent_actions": []
            }
            
            # 统计设备状态分布
            for equipment in parsed_data.get("equipment_status", []):
                status = equipment.get("status", "未知")
                summary["status_distribution"][status] = summary["status_distribution"].get(status, 0) + 1
            
            # 统计缺陷类型分布
            for defect in parsed_data.get("defects", []):
                defect_type = defect.get("type", "其他")
                summary["defect_distribution"][defect_type] = summary["defect_distribution"].get(defect_type, 0) + 1
            
            # 提取关键问题
            for defect in parsed_data.get("defects", []):
                if defect.get("type") in ["泄漏", "绝缘问题", "温度异常"]:
                    summary["key_issues"].append(defect.get("description", ""))
            
            # 提取紧急措施
            for recommendation in parsed_data.get("recommendations", []):
                if any(keyword in recommendation for keyword in ["立即", "紧急", "马上", "尽快"]):
                    summary["urgent_actions"].append(recommendation)
            
            return summary
            
        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return {"error": str(e)}
