#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek-R1 思考过程和最终结果修复测试
测试修复后的智能分离和提示词功能
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.app import smart_split_reasoning_and_result, DeepSeekClient, DEEPSEEK_API_KEY, DEEPSEEK_API_BASE
from langchain_modules.prompts.prompt_manager import PromptManager

def test_smart_split_function():
    """测试智能分离函数"""
    print("🧪 测试智能分离函数")
    
    # 测试用例1：标准thinking标签格式
    test_content_1 = """<thinking>
让我仔细分析这个变压器故障情况。从保护动作来看，差动保护首先动作，这明显表明变压器内部存在不平衡电流，通常意味着绕组或铁芯出现了问题。

结合现场观察到的套管渗油现象，我倾向于判断这是一个复合型故障。油温监测显示68℃，这个温度明显超出了正常运行范围的52℃，表明内部存在异常发热源。

更关键的是，色谱分析结果显示总烃含量达到2500ppm，这个数值比正常标准的150ppm高出了近17倍，这强烈暗示着变压器内部存在严重的放电或过热问题。
</thinking>

**基于上述思考的专业故障诊断报告：**

**故障前系统运行状态评估：** 根据运行记录，故障发生前变压器运行在额定负荷的85%，系统电压稳定在110kV±2%范围内，无异常操作记录。

**设备技术特性核实：** 该变压器为110kV/10kV，容量31.5MVA，投运于2015年，运行年限8年，上次大修时间为2020年，绝缘等级良好。

**保护动作机理分析：** 差动保护在故障发生时0.02秒内动作，动作值为1.2倍整定值，保护动作正确。瓦斯保护同时动作，表明变压器内部确实存在气体产生。

**技术处理方案：** 建议立即停运该变压器，进行全面的绝缘电阻测试和介损测量。同时需要对套管进行详细检查，必要时更换密封件。"""

    reasoning, final = smart_split_reasoning_and_result(test_content_1)
    
    print(f"✅ 测试用例1 - thinking标签格式:")
    print(f"   推理过程长度: {len(reasoning)} 字符")
    print(f"   最终结果长度: {len(final)} 字符")
    print(f"   推理过程包含thinking内容: {'差动保护首先动作' in reasoning}")
    print(f"   最终结果包含报告内容: {'故障前系统运行状态评估' in final}")
    
    # 测试用例2：中文标记格式
    test_content_2 = """【思考过程】
这是一个典型的断路器拒动故障。从保护动作序列来看，过流保护已经正确动作，但断路器未能及时分闸，导致故障持续存在。

需要检查断路器的操作机构，特别是分闸线圈和操作电源。从现场反馈来看，操作电源电压正常，那么问题可能出现在机械部分。

【分析结果】
根据故障现象和技术分析，这是一起断路器机械故障导致的拒动事件。建议立即进行断路器检修，重点检查操作机构的机械部分。"""

    reasoning2, final2 = smart_split_reasoning_and_result(test_content_2)
    
    print(f"✅ 测试用例2 - 中文标记格式:")
    print(f"   推理过程长度: {len(reasoning2)} 字符")
    print(f"   最终结果长度: {len(final2)} 字符")
    print(f"   推理过程包含思考内容: {'断路器拒动故障' in reasoning2}")
    print(f"   最终结果包含分析内容: {'断路器机械故障' in final2}")

def test_prompt_template():
    """测试提示词模板"""
    print("\n🧪 测试提示词模板")
    
    try:
        # 创建提示词管理器
        config = {"prompts": {"templates": {}}}
        prompt_manager = PromptManager(config)
        
        # 测试DeepSeek故障分析模板
        template = prompt_manager.get_template("deepseek_fault_analysis")
        if template:
            formatted_prompt = template.format(
                fault_description="变压器差动保护动作，现场有异响",
                equipment_info="110kV主变，31.5MVA",
                operation_data="负荷85%，电压正常",
                history_data="无异常记录",
                image_analysis="套管渗油",
                question="请分析故障原因"
            )
            
            print(f"✅ 提示词模板测试成功")
            print(f"   模板包含thinking标签要求: {'<thinking>' in formatted_prompt}")
            print(f"   模板包含专业分析要求: {'专业故障诊断报告' in formatted_prompt}")
            print(f"   模板长度: {len(formatted_prompt)} 字符")
        else:
            print("❌ 提示词模板获取失败")
            
    except Exception as e:
        print(f"❌ 提示词模板测试失败: {e}")

def test_api_connection():
    """测试API连接"""
    print("\n🧪 测试DeepSeek API连接")
    
    try:
        client = DeepSeekClient(DEEPSEEK_API_KEY, DEEPSEEK_API_BASE)
        connection_ok = client.test_connection()
        
        if connection_ok:
            print("✅ DeepSeek API连接测试成功")
        else:
            print("❌ DeepSeek API连接测试失败")
            
    except Exception as e:
        print(f"❌ API连接测试异常: {e}")

def test_web_interface():
    """测试Web界面API"""
    print("\n🧪 测试Web界面API")
    
    try:
        # 测试健康检查
        response = requests.get('http://localhost:5002/api/v1/health', timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 健康检查成功")
            print(f"   DeepSeek API配置: {health_data.get('deepseek_api_configured')}")
            print(f"   R1模型: {health_data.get('deepseek_r1_model')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
        # 测试AI分析API
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高",
            "thinking_mode": True
        }
        
        response = requests.post(
            'http://localhost:5002/api/v1/ai-analysis',
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI分析API测试成功")
            print(f"   分析成功: {result.get('success')}")
            print(f"   思考模式: {result.get('thinking_mode')}")
            print(f"   分析时间: {result.get('analysis_time')}秒")
        else:
            print(f"❌ AI分析API测试失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ Web界面测试异常: {e}")

def main():
    """主测试函数"""
    print("🚀 DeepSeek-R1 修复效果测试")
    print("=" * 50)
    
    # 运行所有测试
    test_smart_split_function()
    test_prompt_template()
    test_api_connection()
    test_web_interface()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")
    print("\n📋 修复要点总结:")
    print("1. ✅ 修复了智能分离函数，支持标准thinking标签格式")
    print("2. ✅ 优化了提示词模板，确保正确的输出格式")
    print("3. ✅ 改进了流式处理逻辑，基于thinking标签分离")
    print("4. ✅ 更新了前端显示，正确处理thinking标签")
    print("\n🔧 使用建议:")
    print("- 使用DeepSeek-R1模式时，AI会自动使用thinking标签格式")
    print("- 思考过程和最终结果会自动分离显示")
    print("- 前端界面会正确展示推理过程和诊断报告")

if __name__ == "__main__":
    main()
