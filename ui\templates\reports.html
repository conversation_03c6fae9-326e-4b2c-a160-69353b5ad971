<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告生成 - 故障分析智能助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        .report-template {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .report-template:hover {
            border-color: #0d6efd;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .report-template.selected {
            border-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.05);
        }
        .report-preview {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-lightning-charge"></i>
                故障分析智能助手
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 返回主页
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <div class="row">
            <!-- 左侧：报告配置 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 报告配置</h5>
                    </div>
                    <div class="card-body">
                        <!-- 报告模板选择 -->
                        <div class="mb-4">
                            <h6>选择报告模板</h6>
                            <div class="report-template" data-template="fault-analysis">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-exclamation-triangle text-danger me-3" style="font-size: 1.5rem;"></i>
                                    <div>
                                        <h6 class="mb-1">故障分析报告</h6>
                                        <small class="text-muted">详细的设备故障分析报告</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="report-template" data-template="maintenance">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-tools text-success me-3" style="font-size: 1.5rem;"></i>
                                    <div>
                                        <h6 class="mb-1">维护报告</h6>
                                        <small class="text-muted">设备维护和检修报告</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="report-template" data-template="inspection">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-search text-info me-3" style="font-size: 1.5rem;"></i>
                                    <div>
                                        <h6 class="mb-1">巡检报告</h6>
                                        <small class="text-muted">设备巡检和状态报告</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="report-template" data-template="statistics">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-bar-chart text-warning me-3" style="font-size: 1.5rem;"></i>
                                    <div>
                                        <h6 class="mb-1">统计报告</h6>
                                        <small class="text-muted">设备运行统计分析报告</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 报告参数 -->
                        <form id="report-config-form">
                            <div class="mb-3">
                                <label for="report-title" class="form-label">报告标题</label>
                                <input type="text" class="form-control" id="report-title" 
                                       value="设备故障分析报告" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="report-period" class="form-label">报告周期</label>
                                <select class="form-select" id="report-period">
                                    <option value="daily">日报</option>
                                    <option value="weekly">周报</option>
                                    <option value="monthly" selected>月报</option>
                                    <option value="quarterly">季报</option>
                                    <option value="yearly">年报</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            
                            <div class="mb-3" id="custom-date-range" style="display: none;">
                                <label class="form-label">自定义日期范围</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="date" class="form-control" id="start-date">
                                    </div>
                                    <div class="col-6">
                                        <input type="date" class="form-control" id="end-date">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="equipment-filter" class="form-label">设备筛选</label>
                                <select class="form-select" id="equipment-filter" multiple>
                                    <option value="all" selected>全部设备</option>
                                    <option value="transformer">变压器</option>
                                    <option value="breaker">断路器</option>
                                    <option value="switch">隔离开关</option>
                                    <option value="cable">电缆</option>
                                    <option value="busbar">母线</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="report-format" class="form-label">输出格式</label>
                                <select class="form-select" id="report-format">
                                    <option value="pdf">PDF</option>
                                    <option value="word">Word文档</option>
                                    <option value="excel">Excel表格</option>
                                    <option value="html">HTML网页</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include-charts" checked>
                                    <label class="form-check-label" for="include-charts">
                                        包含图表
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include-images" checked>
                                    <label class="form-check-label" for="include-images">
                                        包含图片
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include-raw-data">
                                    <label class="form-check-label" for="include-raw-data">
                                        包含原始数据
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" onclick="generateReport()">
                                    <i class="bi bi-file-earmark-text"></i> 生成报告
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewReport()">
                                    <i class="bi bi-eye"></i> 预览报告
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧：报告预览和历史 -->
            <div class="col-md-8">
                <!-- 报告预览 -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-eye"></i> 报告预览</h5>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshPreview()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="downloadReport()">
                                <i class="bi bi-download"></i> 下载
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="report-preview" class="report-preview">
                            <div class="text-center text-muted p-4">
                                <i class="bi bi-file-earmark-text" style="font-size: 3rem;"></i>
                                <p class="mt-2">请选择报告模板并点击"预览报告"</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报告历史 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-clock-history"></i> 报告历史</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>报告名称</th>
                                        <th>类型</th>
                                        <th>生成时间</th>
                                        <th>格式</th>
                                        <th>大小</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="report-history-table">
                                    <!-- 报告历史将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/utils.js"></script>
    <script>
        let selectedTemplate = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeReports();
        });

        // 初始化报告页面
        function initializeReports() {
            initTemplateSelection();
            initFormEvents();
            loadReportHistory();
        }

        // 初始化模板选择
        function initTemplateSelection() {
            const templates = document.querySelectorAll('.report-template');
            templates.forEach(template => {
                template.addEventListener('click', function() {
                    // 清除其他选中状态
                    templates.forEach(t => t.classList.remove('selected'));
                    // 选中当前模板
                    this.classList.add('selected');
                    selectedTemplate = this.dataset.template;
                    
                    // 更新报告标题
                    updateReportTitle();
                });
            });
        }

        // 初始化表单事件
        function initFormEvents() {
            // 报告周期变化
            document.getElementById('report-period').addEventListener('change', function() {
                const customDateRange = document.getElementById('custom-date-range');
                if (this.value === 'custom') {
                    customDateRange.style.display = 'block';
                } else {
                    customDateRange.style.display = 'none';
                }
            });
        }

        // 更新报告标题
        function updateReportTitle() {
            const titleInput = document.getElementById('report-title');
            const titles = {
                'fault-analysis': '设备故障分析报告',
                'maintenance': '设备维护报告',
                'inspection': '设备巡检报告',
                'statistics': '设备运行统计报告'
            };
            
            if (selectedTemplate && titles[selectedTemplate]) {
                titleInput.value = titles[selectedTemplate];
            }
        }

        // 预览报告
        function previewReport() {
            if (!selectedTemplate) {
                showAlert('请先选择报告模板', 'warning');
                return;
            }

            const previewContainer = document.getElementById('report-preview');
            showLoading('report-preview', '正在生成预览...');

            // 模拟生成预览
            setTimeout(() => {
                const previewContent = generatePreviewContent();
                previewContainer.innerHTML = previewContent;
            }, 2000);
        }

        // 生成预览内容
        function generatePreviewContent() {
            const title = document.getElementById('report-title').value;
            const period = document.getElementById('report-period').value;
            
            return `
                <div class="report-content">
                    <div class="text-center mb-4">
                        <h2>${title}</h2>
                        <p class="text-muted">生成时间: ${formatDateTime(new Date())}</p>
                    </div>
                    
                    <div class="mb-4">
                        <h4>1. 概述</h4>
                        <p>本报告基于${getPeriodText(period)}的设备运行数据，对相关设备的运行状态、故障情况进行分析总结。</p>
                    </div>
                    
                    <div class="mb-4">
                        <h4>2. 主要发现</h4>
                        <ul>
                            <li>共监测设备156台，其中正常运行120台，占比76.9%</li>
                            <li>发现故障设备23台，主要集中在变压器和断路器</li>
                            <li>完成故障分析89次，诊断准确率达94.2%</li>
                            <li>预防性维护建议15项，已完成12项</li>
                        </ul>
                    </div>
                    
                    <div class="mb-4">
                        <h4>3. 故障分析</h4>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>设备类型</th>
                                        <th>故障数量</th>
                                        <th>主要原因</th>
                                        <th>处理状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>变压器</td>
                                        <td>12</td>
                                        <td>绝缘老化、过热</td>
                                        <td>已处理</td>
                                    </tr>
                                    <tr>
                                        <td>断路器</td>
                                        <td>8</td>
                                        <td>触头磨损、机构故障</td>
                                        <td>处理中</td>
                                    </tr>
                                    <tr>
                                        <td>电缆</td>
                                        <td>3</td>
                                        <td>接头过热、绝缘击穿</td>
                                        <td>已处理</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h4>4. 建议措施</h4>
                        <ol>
                            <li>加强对老旧变压器的监测，及时更换绝缘油</li>
                            <li>定期检查断路器触头状态，按计划进行维护</li>
                            <li>完善电缆接头的热成像检测</li>
                            <li>建立设备健康档案，实施状态检修</li>
                        </ol>
                    </div>
                </div>
            `;
        }

        // 获取周期文本
        function getPeriodText(period) {
            const texts = {
                'daily': '日',
                'weekly': '周',
                'monthly': '月',
                'quarterly': '季度',
                'yearly': '年',
                'custom': '自定义时间段'
            };
            return texts[period] || '月';
        }

        // 生成报告
        async function generateReport() {
            if (!selectedTemplate) {
                showAlert('请先选择报告模板', 'warning');
                return;
            }

            const config = getReportConfig();
            
            try {
                showAlert('正在生成报告，请稍候...', 'info');
                
                const response = await fetch('/api/v1/reports/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                });

                if (!response.ok) {
                    throw new Error('生成报告失败');
                }

                const result = await response.json();
                showAlert('报告生成成功！', 'success');
                
                // 刷新报告历史
                loadReportHistory();
                
                // 自动下载
                if (result.downloadUrl) {
                    window.open(result.downloadUrl, '_blank');
                }
                
            } catch (error) {
                console.error('生成报告失败:', error);
                showAlert('生成报告失败，请稍后重试', 'danger');
            }
        }

        // 获取报告配置
        function getReportConfig() {
            return {
                template: selectedTemplate,
                title: document.getElementById('report-title').value,
                period: document.getElementById('report-period').value,
                startDate: document.getElementById('start-date').value,
                endDate: document.getElementById('end-date').value,
                equipmentFilter: Array.from(document.getElementById('equipment-filter').selectedOptions).map(o => o.value),
                format: document.getElementById('report-format').value,
                includeCharts: document.getElementById('include-charts').checked,
                includeImages: document.getElementById('include-images').checked,
                includeRawData: document.getElementById('include-raw-data').checked
            };
        }

        // 加载报告历史
        function loadReportHistory() {
            const reports = [
                {
                    name: '设备故障分析报告_2024年1月',
                    type: '故障分析',
                    time: '2024-01-16 14:30:00',
                    format: 'PDF',
                    size: '2.3 MB'
                },
                {
                    name: '设备维护报告_2024年第1周',
                    type: '维护报告',
                    time: '2024-01-15 09:15:00',
                    format: 'Word',
                    size: '1.8 MB'
                },
                {
                    name: '设备巡检报告_2024-01-14',
                    type: '巡检报告',
                    time: '2024-01-14 16:45:00',
                    format: 'Excel',
                    size: '956 KB'
                }
            ];

            const tbody = document.getElementById('report-history-table');
            tbody.innerHTML = reports.map(report => `
                <tr>
                    <td>${report.name}</td>
                    <td><span class="badge bg-secondary">${report.type}</span></td>
                    <td>${report.time}</td>
                    <td><span class="badge bg-info">${report.format}</span></td>
                    <td>${report.size}</td>
                    <td>
                        <button class="btn btn-outline-primary btn-sm me-1" onclick="downloadHistoryReport('${report.name}')">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteHistoryReport('${report.name}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 刷新预览
        function refreshPreview() {
            previewReport();
        }

        // 下载报告
        function downloadReport() {
            showAlert('报告下载功能开发中...', 'info');
        }

        // 下载历史报告
        function downloadHistoryReport(reportName) {
            showAlert(`下载报告: ${reportName}`, 'info');
        }

        // 删除历史报告
        function deleteHistoryReport(reportName) {
            if (confirm(`确定要删除报告"${reportName}"吗？`)) {
                showAlert(`报告"${reportName}"已删除`, 'success');
                loadReportHistory();
            }
        }
    </script>
</body>
</html>
