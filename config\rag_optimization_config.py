"""
RAG检索优化配置
专门用于提升故障诊断系统的检索质量和专业性
"""

# 电力专业术语词典
POWER_SYSTEM_TERMS = {
    # 保护装置
    "差动保护": ["differential protection", "差流保护", "比率差动"],
    "零序保护": ["zero sequence protection", "接地保护", "单相接地"],
    "距离保护": ["distance protection", "阻抗保护", "方向保护"],
    "过流保护": ["overcurrent protection", "电流保护", "定时限过流"],
    "速断保护": ["instantaneous protection", "电流速断", "瞬时保护"],
    
    # 设备类型
    "变压器": ["transformer", "主变", "配变", "电力变压器"],
    "断路器": ["circuit breaker", "开关", "SF6断路器", "真空断路器"],
    "隔离开关": ["disconnector", "刀闸", "隔离刀闸"],
    "电流互感器": ["current transformer", "CT", "电流互感器"],
    "电压互感器": ["voltage transformer", "PT", "VT"],
    
    # 故障类型
    "短路故障": ["short circuit", "接地短路", "相间短路", "三相短路"],
    "接地故障": ["ground fault", "单相接地", "两相接地"],
    "过负荷": ["overload", "过载", "负荷过大"],
    "绝缘故障": ["insulation fault", "绝缘击穿", "绝缘老化"],
    
    # 技术参数
    "绝缘电阻": ["insulation resistance", "绝缘阻抗", "兆欧表"],
    "介质损耗": ["dielectric loss", "介损", "tgδ"],
    "色谱分析": ["dissolved gas analysis", "DGA", "油中气体"],
    "局部放电": ["partial discharge", "PD", "局放"],
    
    # 电压等级
    "110kV": ["110千伏", "110KV", "110kv"],
    "220kV": ["220千伏", "220KV", "220kv"],
    "35kV": ["35千伏", "35KV", "35kv"],
    "10kV": ["10千伏", "10KV", "10kv"],
}

# 故障模式关键词
FAULT_PATTERNS = {
    "变压器故障": [
        "油温异常", "套管渗油", "色谱超标", "差动保护动作",
        "绕组故障", "铁芯接地", "分接开关", "冷却系统"
    ],
    "线路故障": [
        "跳闸", "接地", "短路", "雷击", "风偏", "污闪",
        "导线断股", "绝缘子闪络", "杆塔倾斜"
    ],
    "开关设备故障": [
        "拒动", "误动", "SF6泄漏", "触头烧损", "操作机构",
        "辅助触点", "控制回路", "液压系统"
    ],
    "保护装置故障": [
        "保护误动", "保护拒动", "定值错误", "CT断线",
        "通信故障", "装置死机", "软件异常"
    ]
}

# 检索权重配置
RETRIEVAL_WEIGHTS = {
    "semantic_similarity": 0.4,    # 语义相似度权重
    "keyword_match": 0.3,          # 关键词匹配权重
    "technical_terms": 0.2,        # 专业术语权重
    "fault_pattern": 0.1           # 故障模式权重
}

# 相似度阈值配置
SIMILARITY_THRESHOLDS = {
    "high_quality": 0.8,      # 高质量匹配阈值
    "medium_quality": 0.6,    # 中等质量匹配阈值
    "low_quality": 0.4,       # 低质量匹配阈值
    "minimum": 0.2            # 最低匹配阈值
}

# 检索结果数量配置
RETRIEVAL_LIMITS = {
    "semantic_search": 10,     # 语义搜索结果数
    "keyword_search": 8,       # 关键词搜索结果数
    "technical_search": 6,     # 专业术语搜索结果数
    "final_results": 5         # 最终返回结果数
}

# 专业提示词模板
PROFESSIONAL_PROMPT_TEMPLATES = {
    "deepseek_r1_thinking": """你是白银市电力系统资深故障诊断专家，拥有20年以上的变电站运维和故障分析经验。

**专业身份定位：**
- 国家电网白银供电公司高级工程师
- 电力系统故障分析技术专家
- 具备丰富的110kV/220kV变电站运维经验
- 熟悉西北地区电力设备运行特点

**推理要求：**
请以专家内心思考的方式进行深度分析，展现完整的专业推理过程。输出应该是连贯的自然语言，体现一位经验丰富的电力专家在分析故障时的思维流程。

**技术分析要求：**
- 运用电力系统理论和实践经验
- 结合具体的技术参数和数据
- 体现专业的故障诊断思维
- 使用准确的电力专业术语
- 展现逻辑清晰的推理过程""",

    "deepseek_v3_analysis": """你是白银市电力系统故障诊断专家，请提供专业的自然语言分析。

**专业背景：**
- 电力系统高级工程师
- 专业从事变电站设备故障诊断
- 熟悉110kV/220kV电力设备运行特性
- 具备丰富的现场故障处理经验

**分析要求：**
请用专业而流畅的自然语言进行故障分析，避免生硬的条目式表述。分析应该体现电力专家的专业水平和丰富经验，使用准确的技术术语，提供具体的数据支撑。

**技术要求：**
- 必须包含具体的电力技术参数
- 使用标准的电力专业术语
- 结合白银地区的环境特点
- 提供可操作的技术建议"""
}

# RAG上下文增强配置
CONTEXT_ENHANCEMENT = {
    "max_context_length": 2000,        # 最大上下文长度
    "context_overlap": 200,            # 上下文重叠长度
    "relevance_boost": 1.2,            # 相关性提升因子
    "technical_term_boost": 1.5,       # 专业术语提升因子
    "recent_data_boost": 1.3           # 近期数据提升因子
}

# 查询扩展配置
QUERY_EXPANSION = {
    "synonym_expansion": True,          # 启用同义词扩展
    "technical_expansion": True,        # 启用专业术语扩展
    "context_expansion": True,          # 启用上下文扩展
    "max_expanded_terms": 5            # 最大扩展词数
}

# 结果重排序配置
RERANKING_CONFIG = {
    "enable_reranking": True,          # 启用重排序
    "rerank_model": "cross_encoder",   # 重排序模型
    "diversity_factor": 0.3,           # 多样性因子
    "freshness_factor": 0.2            # 新鲜度因子
}

# 质量评估配置
QUALITY_ASSESSMENT = {
    "min_content_length": 50,          # 最小内容长度
    "max_content_length": 1000,        # 最大内容长度
    "technical_density_threshold": 0.3, # 技术密度阈值
    "relevance_threshold": 0.5         # 相关性阈值
}

def get_optimized_retrieval_config():
    """获取优化的检索配置"""
    return {
        "power_terms": POWER_SYSTEM_TERMS,
        "fault_patterns": FAULT_PATTERNS,
        "weights": RETRIEVAL_WEIGHTS,
        "thresholds": SIMILARITY_THRESHOLDS,
        "limits": RETRIEVAL_LIMITS,
        "context_enhancement": CONTEXT_ENHANCEMENT,
        "query_expansion": QUERY_EXPANSION,
        "reranking": RERANKING_CONFIG,
        "quality": QUALITY_ASSESSMENT
    }

def get_professional_prompt_template(mode: str = "deepseek_v3"):
    """获取专业提示词模板"""
    if mode == "deepseek_r1":
        return PROFESSIONAL_PROMPT_TEMPLATES["deepseek_r1_thinking"]
    else:
        return PROFESSIONAL_PROMPT_TEMPLATES["deepseek_v3_analysis"]

def enhance_query_with_technical_terms(query: str) -> list:
    """使用专业术语增强查询"""
    enhanced_queries = [query]
    
    # 查找匹配的专业术语
    for term, synonyms in POWER_SYSTEM_TERMS.items():
        if term in query:
            for synonym in synonyms[:2]:  # 限制同义词数量
                enhanced_queries.append(query.replace(term, synonym))
    
    return enhanced_queries[:5]  # 限制扩展查询数量

def calculate_technical_relevance_score(content: str, query: str) -> float:
    """计算技术相关性得分"""
    score = 0.0
    content_lower = content.lower()
    query_lower = query.lower()
    
    # 基础相关性
    if query_lower in content_lower:
        score += 0.3
    
    # 专业术语匹配
    for term in POWER_SYSTEM_TERMS.keys():
        if term in content_lower:
            score += 0.1
    
    # 故障模式匹配
    for pattern, keywords in FAULT_PATTERNS.items():
        for keyword in keywords:
            if keyword in content_lower:
                score += 0.05
    
    return min(score, 1.0)  # 限制最大得分为1.0
