"""
优化的文件上传API路由

使用安全的文件上传工具和统一的配置管理
"""

from typing import List
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from loguru import logger
from datetime import datetime

from ..models import FileUploadResponse, BatchUploadResponse, BaseResponse
from core.security_utils import validate_and_save_file, get_file_uploader

router = APIRouter()


@router.post("/image", response_model=FileUploadResponse)
async def upload_image(
    file: UploadFile = File(...),
    title: str = Form(""),
    category: str = Form("general"),
    description: str = Form("")
):
    """
    上传图像文件
    
    支持JPG、PNG、BMP、TIFF、WebP等格式
    使用安全的文件验证和存储
    """
    try:
        logger.info(f"上传图像: {file.filename}, 标题: {title}, 类别: {category}")

        # 使用安全文件上传器
        file_info = validate_and_save_file(file, 'image', 'images')

        return FileUploadResponse(
            success=True,
            message="图像上传成功",
            filename=file_info["original_filename"],
            file_path=file_info["relative_path"],
            file_size=file_info["file_size"],
            file_type=file_info["file_type"],
            metadata={
                "title": title,
                "category": category,
                "description": description,
                "safe_filename": file_info["safe_filename"],
                "file_hash": file_info["file_hash"]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传图像错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"图像上传失败: {str(e)}")


@router.post("/document", response_model=FileUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(""),
    category: str = Form("general")
):
    """
    上传文档文件
    
    支持PDF、Word、Excel、文本文件等
    """
    try:
        logger.info(f"上传文档: {file.filename}")
        
        # 使用安全文件上传器
        file_info = validate_and_save_file(file, 'document', 'documents')
        
        return FileUploadResponse(
            success=True,
            message="文档上传成功",
            filename=file_info["original_filename"],
            file_path=file_info["relative_path"],
            file_size=file_info["file_size"],
            file_type=file_info["file_type"],
            metadata={
                "title": title,
                "category": category,
                "safe_filename": file_info["safe_filename"],
                "file_hash": file_info["file_hash"]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文档错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档上传失败: {str(e)}")


@router.post("/batch", response_model=BatchUploadResponse)
async def batch_upload(
    files: List[UploadFile] = File(...),
    category: str = Form("general")
):
    """
    批量上传文件
    
    支持同时上传多个文件
    """
    try:
        logger.info(f"批量上传 {len(files)} 个文件")
        
        if len(files) > 10:  # 限制批量上传数量
            raise HTTPException(status_code=400, detail="批量上传文件数量不能超过10个")
        
        upload_results = []
        failed_files = []
        
        for file in files:
            try:
                # 根据文件类型选择上传类型
                file_type = 'any'  # 允许任何类型
                if file.content_type and file.content_type.startswith('image/'):
                    file_type = 'image'
                    subdirectory = 'images'
                elif file.content_type in ['application/pdf', 'text/plain']:
                    file_type = 'document'
                    subdirectory = 'documents'
                else:
                    subdirectory = 'general'
                
                file_info = validate_and_save_file(file, file_type, subdirectory)
                
                upload_results.append({
                    "filename": file_info["original_filename"],
                    "file_path": file_info["relative_path"],
                    "file_size": file_info["file_size"],
                    "file_type": file_info["file_type"],
                    "status": "success",
                    "safe_filename": file_info["safe_filename"]
                })
                
            except Exception as e:
                logger.error(f"文件 {file.filename} 上传失败: {str(e)}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })
        
        return BatchUploadResponse(
            success=len(failed_files) == 0,
            message=f"批量上传完成，成功: {len(upload_results)}, 失败: {len(failed_files)}",
            total_files=len(files),
            successful_uploads=len(upload_results),
            failed_uploads=len(failed_files),
            upload_results=upload_results,
            failed_files=failed_files
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量上传错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量上传失败: {str(e)}")


@router.delete("/file/{file_path:path}", response_model=BaseResponse)
async def delete_file(file_path: str):
    """
    删除上传的文件
    
    安全删除指定路径的文件
    """
    try:
        logger.info(f"删除文件: {file_path}")
        
        file_uploader = get_file_uploader()
        success = file_uploader.delete_file(file_path)
        
        if success:
            return BaseResponse(
                success=True,
                message="文件删除成功"
            )
        else:
            raise HTTPException(status_code=404, detail="文件不存在或删除失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件删除失败: {str(e)}")


@router.get("/info/{file_path:path}")
async def get_file_info(file_path: str):
    """
    获取文件信息
    
    返回指定文件的详细信息
    """
    try:
        import os
        from pathlib import Path
        from core.config_manager import get_config
        
        config = get_config()
        upload_base_dir = config.get_upload_dir()
        full_path = Path(upload_base_dir) / file_path
        
        if not full_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        stat = full_path.stat()
        
        return {
            "filename": full_path.name,
            "file_path": str(full_path.relative_to(upload_base_dir)),
            "file_size": stat.st_size,
            "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "is_file": full_path.is_file()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")
