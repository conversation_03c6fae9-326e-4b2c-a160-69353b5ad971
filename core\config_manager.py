"""
统一配置管理模块

提供全局配置管理和环境变量处理
"""

import os
import yaml
from typing import Dict, Any, Optional
from loguru import logger


class ConfigManager:
    """配置管理器"""

    _instance = None
    _config = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if self._config is None:
            self.load_config()

    def load_config(self, config_path: Optional[str] = None) -> None:
        """加载配置文件"""
        if config_path is None:
            config_path = self._find_config_file()

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)

            # 处理环境变量替换
            self._process_env_variables(self._config)

            logger.info(f"配置文件加载成功: {config_path}")

        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self._config = self._get_default_config()

    def _find_config_file(self) -> str:
        """查找配置文件"""
        possible_paths = [
            "configs/config.yaml",
            "config.yaml",
            "../configs/config.yaml"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        raise FileNotFoundError("未找到配置文件")

    def _process_env_variables(self, config: Dict[str, Any]) -> None:
        """处理环境变量替换"""
        def replace_env_vars(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    obj[key] = replace_env_vars(value)
            elif isinstance(obj, list):
                return [replace_env_vars(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
                env_var = obj[2:-1]
                default_value = None
                if ":-" in env_var:
                    env_var, default_value = env_var.split(":-", 1)
                return os.getenv(env_var, default_value)
            return obj

        replace_env_vars(config)

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "system": {
                "name": "故障分析智能助手",
                "version": "1.0.0",
                "debug": False,
                "log_level": "INFO"
            },
            "server": {
                "host": "0.0.0.0",
                "port": 8000,
                "workers": 4,
                "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"]
            },
            "upload": {
                "max_file_size": 50,  # MB
                "allowed_extensions": [".jpg", ".png", ".pdf", ".txt", ".docx"],
                "upload_path": "./uploads"
            },
            "database": {
                "url": "sqlite:///./fault_diagnosis.db"
            },
            "llm": {
                "deepseek": {
                    "api_key": None,
                    "base_url": "https://api.deepseek.com",
                    "model_name": "deepseek-chat"
                }
            }
        }

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.get("server", {})

    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get("database", {})

    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self.get("llm", {})

    def get_upload_config(self) -> Dict[str, Any]:
        """获取上传配置"""
        return self.get("upload", {})

    def is_debug(self) -> bool:
        """是否为调试模式"""
        return self.get("system.debug", False)

    def get_cors_origins(self) -> list:
        """获取CORS允许的源"""
        return self.get("server.cors_origins", ["http://localhost:3000"])

    def get_max_file_size(self) -> int:
        """获取最大文件大小（字节）"""
        return self.get("upload.max_file_size", 50) * 1024 * 1024

    def get_allowed_file_types(self) -> list:
        """获取允许的文件类型"""
        return self.get("upload.allowed_extensions", [".jpg", ".png", ".pdf", ".txt"])

    def get_upload_dir(self) -> str:
        """获取上传目录"""
        return self.get("upload.upload_path", "./uploads")

    def get_api_key(self, service: str) -> Optional[str]:
        """获取API密钥"""
        return self.get(f"llm.{service}.api_key")

    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_keys = [
            "system.name",
            "server.host",
            "server.port"
        ]

        for key in required_keys:
            if self.get(key) is None:
                logger.error(f"缺少必需的配置项: {key}")
                return False

        return True


# 全局配置实例
config = ConfigManager()


def get_config() -> ConfigManager:
    """获取配置管理器实例"""
    return config


def load_config_from_file(config_path: str) -> None:
    """从指定文件加载配置"""
    config.load_config(config_path)


# 便捷函数
def get_server_port() -> int:
    """获取服务器端口"""
    return config.get("server.port", 8000)


def get_server_host() -> str:
    """获取服务器主机"""
    return config.get("server.host", "0.0.0.0")


def is_production() -> bool:
    """是否为生产环境"""
    return not config.is_debug()


def get_upload_dir() -> str:
    """获取上传目录"""
    return config.get("upload.upload_path", "./uploads")
