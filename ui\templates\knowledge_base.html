<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库 - 故障分析智能助手</title>
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        .container-fluid { width: 100%; padding: 15px; }
        .navbar { display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 1rem; background-color: #0d6efd; }
        .navbar-brand { color: white; font-size: 1.25rem; text-decoration: none; }
        .card { border: 1px solid #dee2e6; border-radius: 0.25rem; margin-bottom: 1rem; }
        .card-header { padding: 0.75rem 1rem; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold; }
        .card-body { padding: 1rem; }
        .form-control { width: 100%; padding: 0.375rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.25rem; }
        .btn { padding: 0.375rem 0.75rem; border: 1px solid transparent; border-radius: 0.25rem; cursor: pointer; margin-right: 0.5rem; }
        .btn-primary { background-color: #0d6efd; color: white; border-color: #0d6efd; }
        .btn-success { background-color: #198754; color: white; border-color: #198754; }
        .btn-info { background-color: #0dcaf0; color: black; border-color: #0dcaf0; }
        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
        .search-result { border: 1px solid #dee2e6; border-radius: 0.25rem; padding: 1rem; margin-bottom: 1rem; background-color: white; }
        .search-result:hover { background-color: #f8f9fa; cursor: pointer; }
        .result-title { font-weight: bold; color: #0d6efd; margin-bottom: 0.5rem; }
        .result-content { color: #6c757d; margin-bottom: 0.5rem; }
        .result-meta { font-size: 0.875rem; color: #6c757d; }
        .badge { padding: 0.25em 0.4em; font-size: 0.75em; border-radius: 0.25rem; }
        .badge-primary { background-color: #0d6efd; color: white; }
        .badge-success { background-color: #198754; color: white; }
        .badge-info { background-color: #0dcaf0; color: black; }
        .badge-secondary { background-color: #6c757d; color: white; }
        .spinner-border { width: 2rem; height: 2rem; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: spinner-border .75s linear infinite; }
        @keyframes spinner-border { to { transform: rotate(360deg); } }
        .d-none { display: none; }
        .text-center { text-align: center; }
        .text-muted { color: #6c757d; }
        .mb-3 { margin-bottom: 1rem; }
        .row { display: flex; flex-wrap: wrap; margin: -0.5rem; }
        .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; padding: 0.5rem; }
        .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; padding: 0.5rem; }
        .col-12 { flex: 0 0 100%; max-width: 100%; padding: 0.5rem; }
        .modal { position: fixed; top: 0; left: 0; z-index: 1050; display: none; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
        .modal.show { display: block; }
        .modal-dialog { position: relative; width: auto; margin: 1.75rem; max-width: 800px; margin-left: auto; margin-right: auto; }
        .modal-content { background-color: white; border-radius: 0.3rem; padding: 1rem; max-height: 80vh; overflow-y: auto; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; }
        .modal-title { font-size: 1.25rem; font-weight: bold; }
        .btn-close { background: none; border: none; font-size: 1.5rem; cursor: pointer; }
        .document-content { white-space: pre-wrap; font-family: monospace; background-color: #f8f9fa; padding: 1rem; border-radius: 0.25rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <a class="navbar-brand" href="/">📚 故障分析智能助手</a>
        <div>
            <a href="/" style="color: white; text-decoration: none; margin-right: 1rem;">首页</a>
            <a href="/fault-analysis" style="color: white; text-decoration: none; margin-right: 1rem;">故障分析</a>
            <a href="/equipment-management" style="color: white; text-decoration: none;">设备管理</a>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2>📚 知识库</h2>
                <p class="text-muted">电力系统故障诊断知识库检索</p>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        知识库搜索
                    </div>
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="mb-3">
                                <input type="text" class="form-control" id="searchQuery" 
                                    placeholder="输入搜索关键词，例如：变压器故障、断路器保护、差动保护..." required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">搜索类型:</label>
                                <select class="form-control" id="searchType">
                                    <option value="multimodal">多模态搜索（文本+图像）</option>
                                    <option value="text">仅文本搜索</option>
                                    <option value="image">仅图像搜索</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary" id="searchBtn">
                                <span id="searchSpinner" class="spinner-border spinner-border-sm d-none"></span>
                                搜索知识库
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        知识库统计
                    </div>
                    <div class="card-body">
                        <div id="knowledgeStats">
                            <div class="text-center">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果区域 -->
        <div class="row">
            <div class="col-12">
                <div class="card d-none" id="resultsCard">
                    <div class="card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>搜索结果</span>
                            <span id="resultCount" class="badge badge-primary">0 个结果</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="searchResults">
                            <!-- 搜索结果将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速访问区域 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        快速访问
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-info btn-sm" onclick="quickSearch('变压器故障')">变压器故障</button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info btn-sm" onclick="quickSearch('断路器保护')">断路器保护</button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info btn-sm" onclick="quickSearch('差动保护')">差动保护</button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info btn-sm" onclick="quickSearch('电缆故障')">电缆故障</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文档详情模态框 -->
    <div class="modal" id="documentModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="documentTitle">文档详情</h5>
                    <button type="button" class="btn-close" onclick="hideDocumentModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="documentContent">
                        <div class="text-center">加载中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 搜索表单处理
        document.getElementById('searchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const query = document.getElementById('searchQuery').value.trim();
            const searchType = document.getElementById('searchType').value;
            
            if (!query) {
                alert('请输入搜索关键词');
                return;
            }

            await performSearch(query, searchType);
        });

        async function performSearch(query, searchType = 'multimodal') {
            const searchBtn = document.getElementById('searchBtn');
            const spinner = document.getElementById('searchSpinner');
            const resultsCard = document.getElementById('resultsCard');
            
            // 显示加载状态
            searchBtn.disabled = true;
            spinner.classList.remove('d-none');
            
            try {
                const response = await fetch('/api/v1/knowledge/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        search_type: searchType,
                        limit: 10
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    displaySearchResults(result.results, query);
                    resultsCard.classList.remove('d-none');
                } else {
                    alert('搜索失败: ' + result.error);
                }
                
            } catch (error) {
                console.error('搜索请求失败:', error);
                alert('搜索请求失败，请检查网络连接');
            } finally {
                // 恢复按钮状态
                searchBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        function displaySearchResults(results, query) {
            const container = document.getElementById('searchResults');
            const countElement = document.getElementById('resultCount');
            
            countElement.textContent = `${results.length} 个结果`;
            
            if (results.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <p>未找到与 "${query}" 相关的内容</p>
                        <p>建议：</p>
                        <ul style="text-align: left; display: inline-block;">
                            <li>尝试使用不同的关键词</li>
                            <li>检查拼写是否正确</li>
                            <li>使用更通用的术语</li>
                        </ul>
                    </div>
                `;
                return;
            }

            const resultsHTML = results.map(result => `
                <div class="search-result" onclick="viewDocument('${result.source}', '${result.title}')">
                    <div class="result-title">${result.title}</div>
                    <div class="result-content">${result.content}</div>
                    <div class="result-meta">
                        <span class="badge ${getTypeBadgeClass(result.type)}">${getTypeText(result.type)}</span>
                        <span style="margin-left: 0.5rem;">相关度: ${(result.score * 100).toFixed(1)}%</span>
                        <span style="margin-left: 0.5rem;">来源: ${result.source}</span>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = resultsHTML;
        }

        function getTypeBadgeClass(type) {
            const typeMap = {
                'document': 'badge-primary',
                'case_study': 'badge-success',
                'fault_pattern': 'badge-info',
                'uploaded_file': 'badge-secondary',
                'image': 'badge-info'
            };
            return typeMap[type] || 'badge-secondary';
        }

        function getTypeText(type) {
            const typeMap = {
                'document': '文档',
                'case_study': '案例研究',
                'fault_pattern': '故障模式',
                'uploaded_file': '上传文件',
                'image': '图像'
            };
            return typeMap[type] || type;
        }

        async function viewDocument(docId, title) {
            const modal = document.getElementById('documentModal');
            const titleElement = document.getElementById('documentTitle');
            const contentElement = document.getElementById('documentContent');
            
            titleElement.textContent = title;
            contentElement.innerHTML = '<div class="text-center">加载中...</div>';
            modal.classList.add('show');
            
            try {
                const response = await fetch(`/api/v1/knowledge/document/${encodeURIComponent(docId)}`);
                const result = await response.json();
                
                if (result.success) {
                    const doc = result.document;
                    contentElement.innerHTML = `
                        <div class="document-content">${doc.content}</div>
                        ${doc.metadata ? `
                            <div class="mt-3">
                                <h6>元数据:</h6>
                                <pre>${JSON.stringify(doc.metadata, null, 2)}</pre>
                            </div>
                        ` : ''}
                    `;
                } else {
                    contentElement.innerHTML = '<div class="text-danger">加载文档失败: ' + result.error + '</div>';
                }
            } catch (error) {
                console.error('获取文档失败:', error);
                contentElement.innerHTML = '<div class="text-danger">网络请求失败</div>';
            }
        }

        function hideDocumentModal() {
            document.getElementById('documentModal').classList.remove('show');
        }

        function quickSearch(keyword) {
            document.getElementById('searchQuery').value = keyword;
            performSearch(keyword);
        }

        // 页面加载时获取知识库统计
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('/api/v1/status');
                const status = await response.json();
                
                const statsElement = document.getElementById('knowledgeStats');
                if (status.status === 'running') {
                    statsElement.innerHTML = `
                        <div><strong>知识库项目:</strong> ${status.data_statistics?.knowledge_base_items || 0}</div>
                        <div><strong>案例研究:</strong> ${status.data_statistics?.case_studies || 0}</div>
                        <div><strong>故障模式:</strong> ${status.data_statistics?.fault_patterns || 0}</div>
                        <div><strong>设备记录:</strong> ${status.data_statistics?.equipment_records || 0}</div>
                        <div class="mt-2">
                            <span class="badge badge-success">系统运行正常</span>
                        </div>
                    `;
                } else {
                    statsElement.innerHTML = '<div class="text-danger">系统状态异常</div>';
                }
            } catch (error) {
                console.error('获取系统状态失败:', error);
                document.getElementById('knowledgeStats').innerHTML = '<div class="text-danger">获取统计失败</div>';
            }
        });
    </script>
</body>
</html>
