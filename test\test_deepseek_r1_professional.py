#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek-R1 专业推理功能测试
基于Transformer架构和统计模式识别的测试验证
"""

import requests
import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DeepSeekR1ProfessionalTester:
    """DeepSeek-R1 专业推理测试器"""
    
    def __init__(self, base_url="http://localhost:5002"):
        self.base_url = base_url
        self.test_results = {}
        
    def test_technical_reasoning(self):
        """测试技术推理能力"""
        print("🧠 测试DeepSeek-R1技术推理能力...")
        
        # 复杂故障场景
        complex_query = """
        110kV主变压器T1发生故障，现场情况如下：
        1. 差动保护首先动作，跳开主变各侧开关
        2. 瓦斯保护随后动作，发出重瓦斯信号
        3. 现场检查发现A相套管有明显渗油痕迹
        4. 油温监测显示68℃（正常运行52℃）
        5. 色谱分析结果：总烃2500ppm，乙炔45ppm，氢气1200ppm
        6. 绝缘电阻测试：A相对地0.8MΩ（正常>1000MΩ）
        7. 变比测试正常，直流电阻略有增大
        """
        
        return self._test_streaming_analysis(complex_query, "technical_reasoning")
    
    def test_pattern_recognition(self):
        """测试模式识别能力"""
        print("🔍 测试DeepSeek-R1模式识别能力...")
        
        pattern_query = """
        220kV变电站连续发生以下异常：
        - 第1天：#1主变油温缓慢上升至65℃
        - 第3天：色谱分析总烃从150ppm升至800ppm
        - 第5天：局部放电监测发现异常信号
        - 第7天：差动保护预警，未动作
        - 第9天：A相套管发现轻微渗油
        请基于统计模式识别这种渐进性故障的发展规律。
        """
        
        return self._test_streaming_analysis(pattern_query, "pattern_recognition")
    
    def test_probability_inference(self):
        """测试概率推断能力"""
        print("📊 测试DeepSeek-R1概率推断能力...")
        
        probability_query = """
        基于以下不完整信息进行概率推断：
        - 保护动作：差动保护动作（确定）
        - 设备状态：套管渗油（疑似）
        - 参数异常：油温偏高（可能）
        - 历史记录：该设备运行15年，近期负荷较重
        请基于概率推断和统计关联分析可能的故障原因及其概率分布。
        """
        
        return self._test_streaming_analysis(probability_query, "probability_inference")
    
    def _test_streaming_analysis(self, query, test_type):
        """执行流式分析测试"""
        try:
            print(f"📝 测试查询: {query[:100]}...")
            
            # 发送流式请求
            response = requests.post(
                f"{self.base_url}/api/v1/analyze_stream",
                json={
                    "query": query,
                    "thinking_mode": True
                },
                stream=True,
                timeout=120
            )
            
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.status_code}")
                return False
            
            # 解析流式响应
            reasoning_content = ""
            final_content = ""
            reasoning_chunks = 0
            final_chunks = 0
            
            print("🌊 开始解析流式响应...")
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            
                            if data.get('type') == 'reasoning':
                                reasoning_content += data.get('content', '')
                                reasoning_chunks += 1
                                if reasoning_chunks <= 10 or reasoning_chunks % 100 == 0:
                                    print(f"🧠 推理 #{reasoning_chunks}: '{data.get('content', '')[:20]}...'")
                                    
                            elif data.get('type') == 'final':
                                final_content += data.get('content', '')
                                final_chunks += 1
                                if final_chunks <= 10 or final_chunks % 50 == 0:
                                    print(f"📋 结论 #{final_chunks}: '{data.get('content', '')[:20]}...'")
                                    
                            elif data.get('type') == 'complete':
                                print("🏁 完成信号")
                                break
                                
                        except json.JSONDecodeError:
                            continue
            
            # 分析结果质量
            result_analysis = self._analyze_reasoning_quality(
                reasoning_content, final_content, test_type
            )
            
            self.test_results[test_type] = result_analysis
            
            print(f"\n📊 {test_type} 测试结果:")
            print(f"   推理chunks: {reasoning_chunks}")
            print(f"   结论chunks: {final_chunks}")
            print(f"   推理内容长度: {len(reasoning_content)} 字符")
            print(f"   结论内容长度: {len(final_content)} 字符")
            print(f"   质量评分: {result_analysis['quality_score']:.1f}/10")
            
            return result_analysis['quality_score'] >= 7.0
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def _analyze_reasoning_quality(self, reasoning_content, final_content, test_type):
        """分析推理质量"""
        analysis = {
            "reasoning_length": len(reasoning_content),
            "final_length": len(final_content),
            "quality_score": 0.0,
            "technical_terms": 0,
            "reasoning_patterns": 0,
            "logical_coherence": 0.0
        }
        
        # 技术术语检查
        technical_terms = [
            "差动保护", "瓦斯保护", "变压器", "套管", "渗油", "色谱分析",
            "总烃", "乙炔", "氢气", "绝缘电阻", "局部放电", "概率", "统计",
            "模式识别", "推理", "分析", "诊断"
        ]
        
        found_terms = sum(1 for term in technical_terms if term in reasoning_content)
        analysis["technical_terms"] = found_terms
        
        # 推理模式检查
        reasoning_patterns = [
            "基于", "根据", "从", "可以推断", "表明", "说明", "因此",
            "所以", "由于", "考虑到", "结合", "综合", "分析"
        ]
        
        found_patterns = sum(1 for pattern in reasoning_patterns if pattern in reasoning_content)
        analysis["reasoning_patterns"] = found_patterns
        
        # 逻辑连贯性评估
        coherence_indicators = [
            "首先", "其次", "然后", "最后", "综上", "总结",
            "因果关系", "相关性", "概率", "可能性"
        ]
        
        coherence_score = sum(1 for indicator in coherence_indicators if indicator in reasoning_content)
        analysis["logical_coherence"] = min(coherence_score / 5.0, 1.0)
        
        # 计算综合质量评分
        quality_score = (
            min(found_terms / 10.0, 1.0) * 3.0 +  # 技术术语权重30%
            min(found_patterns / 8.0, 1.0) * 3.0 +  # 推理模式权重30%
            analysis["logical_coherence"] * 2.0 +    # 逻辑连贯性权重20%
            min(len(reasoning_content) / 2000.0, 1.0) * 1.0 +  # 内容充实度权重10%
            min(len(final_content) / 1000.0, 1.0) * 1.0        # 结论完整度权重10%
        )
        
        analysis["quality_score"] = quality_score
        
        return analysis
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始DeepSeek-R1专业推理综合测试...")
        print("=" * 60)
        
        tests = [
            ("technical_reasoning", self.test_technical_reasoning),
            ("pattern_recognition", self.test_pattern_recognition),
            ("probability_inference", self.test_probability_inference)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 执行测试: {test_name}")
            print("-" * 40)
            
            try:
                if test_func():
                    print(f"✅ {test_name} 测试通过")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} 测试失败")
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 测试总结:")
        print(f"   通过测试: {passed_tests}/{total_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！DeepSeek-R1专业推理功能正常")
        else:
            print("⚠️ 部分测试失败，需要进一步优化")
        
        return passed_tests == total_tests

if __name__ == "__main__":
    tester = DeepSeekR1ProfessionalTester()
    success = tester.run_comprehensive_test()
    sys.exit(0 if success else 1)
