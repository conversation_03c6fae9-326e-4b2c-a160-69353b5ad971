"""
知识库管理模块

统一管理文档、图像等知识资源
"""

import os
import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from loguru import logger

from data_processing.text_processor import TextProcessor
from data_processing.image_processor import ImageProcessor
from data_processing.ocr_processor import OCRProcessor
from .text_retriever import TextRetriever
from .multimodal_retriever import MultimodalRetriever


class KnowledgeBase:
    """知识库管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.text_path = config.get("text_path", "./knowledge_base/text")
        self.images_path = config.get("images_path", "./knowledge_base/images")
        self.mappings_path = config.get("mappings_path", "./knowledge_base/mappings")
        
        # 初始化处理器
        self.text_processor = TextProcessor(config.get("data_processing", {}))
        self.image_processor = ImageProcessor(config.get("data_processing", {}).get("image", {}))
        self.ocr_processor = OCRProcessor(config.get("data_processing", {}).get("ocr", {}))
        
        # 初始化检索器
        self.text_retriever = TextRetriever(config.get("vector_db", {}))
        self.multimodal_retriever = MultimodalRetriever(config)
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载现有知识库
        self._load_knowledge_base()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        try:
            for path in [self.text_path, self.images_path, self.mappings_path]:
                os.makedirs(path, exist_ok=True)
            logger.info("知识库目录检查完成")
            
        except Exception as e:
            logger.error(f"创建知识库目录失败: {str(e)}")
    
    def _load_knowledge_base(self):
        """加载现有知识库"""
        try:
            # 检查是否有现有的索引
            stats = self.text_retriever.get_index_stats()
            if stats.get("total_documents", 0) == 0:
                logger.info("未找到现有索引，开始构建知识库")
                self.build_knowledge_base()
            else:
                logger.info(f"加载现有知识库，包含 {stats['total_documents']} 个文档")
                
        except Exception as e:
            logger.error(f"加载知识库失败: {str(e)}")
    
    def build_knowledge_base(self) -> bool:
        """
        构建知识库
        
        Returns:
            是否成功
        """
        try:
            logger.info("开始构建知识库...")
            
            # 处理文本文档
            text_documents = self._process_text_documents()
            
            # 构建文本索引
            if text_documents:
                success = self.text_retriever.build_index(text_documents)
                if not success:
                    logger.error("文本索引构建失败")
                    return False
            
            # 处理图像（多模态检索器会自动处理）
            self._process_images()
            
            # 保存知识库元数据
            self._save_metadata()
            
            logger.info("知识库构建完成")
            return True
            
        except Exception as e:
            logger.error(f"知识库构建失败: {str(e)}")
            return False
    
    def _process_text_documents(self) -> List[Dict[str, Any]]:
        """处理文本文档"""
        try:
            if not os.path.exists(self.text_path):
                logger.warning(f"文本目录不存在: {self.text_path}")
                return []
            
            logger.info("开始处理文本文档...")
            documents = self.text_processor.batch_process(self.text_path)
            
            logger.info(f"文本文档处理完成，共 {len(documents)} 个文档块")
            return documents
            
        except Exception as e:
            logger.error(f"文本文档处理失败: {str(e)}")
            return []
    
    def _process_images(self):
        """处理图像文件"""
        try:
            if not os.path.exists(self.images_path):
                logger.warning(f"图像目录不存在: {self.images_path}")
                return
            
            logger.info("开始处理图像文件...")
            
            # 多模态检索器会自动处理图像目录
            # 这里可以添加额外的图像处理逻辑
            
            logger.info("图像文件处理完成")
            
        except Exception as e:
            logger.error(f"图像处理失败: {str(e)}")
    
    def _save_metadata(self):
        """保存知识库元数据"""
        try:
            metadata = {
                "version": "1.0",
                "created_at": datetime.now().isoformat(),
                "text_stats": self.text_retriever.get_index_stats(),
                "multimodal_stats": self.multimodal_retriever.get_stats(),
                "config": self.config
            }
            
            metadata_file = os.path.join(self.mappings_path, "knowledge_base_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"知识库元数据已保存: {metadata_file}")
            
        except Exception as e:
            logger.error(f"保存元数据失败: {str(e)}")
    
    def add_document(self, file_path: str, document_type: str = "auto") -> bool:
        """
        添加单个文档到知识库
        
        Args:
            file_path: 文件路径
            document_type: 文档类型 (text, image, auto)
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
            
            # 自动判断文档类型
            if document_type == "auto":
                extension = file_path.suffix.lower()
                if extension in ['.txt', '.md', '.pdf', '.docx']:
                    document_type = "text"
                elif extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                    document_type = "image"
                else:
                    logger.warning(f"无法识别文件类型: {extension}")
                    return False
            
            if document_type == "text":
                return self._add_text_document(str(file_path))
            elif document_type == "image":
                return self._add_image_document(str(file_path))
            else:
                logger.error(f"不支持的文档类型: {document_type}")
                return False
                
        except Exception as e:
            logger.error(f"添加文档失败: {str(e)}")
            return False
    
    def _add_text_document(self, file_path: str) -> bool:
        """添加文本文档"""
        try:
            # 处理文档
            documents = self.text_processor.process_file(file_path)
            
            if not documents:
                logger.error(f"文档处理失败: {file_path}")
                return False
            
            # 添加到索引
            success = self.text_retriever.add_documents(documents)
            
            if success:
                logger.info(f"成功添加文本文档: {file_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加文本文档失败: {str(e)}")
            return False
    
    def _add_image_document(self, file_path: str) -> bool:
        """添加图像文档"""
        try:
            success = self.multimodal_retriever.add_image(file_path)
            
            if success:
                logger.info(f"成功添加图像文档: {file_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加图像文档失败: {str(e)}")
            return False
    
    def search(self, query: str, search_type: str = "multimodal", top_k: int = 10) -> Dict[str, Any]:
        """
        搜索知识库
        
        Args:
            query: 查询文本
            search_type: 搜索类型 (text, image, multimodal)
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        try:
            if search_type == "text":
                results = self.text_retriever.search(query, top_k)
                return {"type": "text", "results": results}
            
            elif search_type == "image":
                results = self.multimodal_retriever.search_image_by_text(query, top_k)
                return {"type": "image", "results": results}
            
            elif search_type == "multimodal":
                results = self.multimodal_retriever.search_multimodal(query, top_k=top_k)
                return {"type": "multimodal", "results": results}
            
            else:
                logger.error(f"不支持的搜索类型: {search_type}")
                return {"type": "error", "results": []}
                
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return {"type": "error", "results": []}
    
    def search_by_image(self, image_path: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        以图搜图
        
        Args:
            image_path: 查询图像路径
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        return self.multimodal_retriever.search_by_image(image_path, top_k)
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取文档详情
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档信息
        """
        try:
            # 先尝试从文本检索器获取
            doc = self.text_retriever.get_document_by_id(doc_id)
            if doc:
                return {"type": "text", "document": doc}
            
            # 再尝试从图像检索器获取
            img_info = self.multimodal_retriever.get_image_info(doc_id)
            if img_info:
                return {"type": "image", "document": img_info}
            
            return None
            
        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        Returns:
            统计信息
        """
        try:
            text_stats = self.text_retriever.get_index_stats()
            multimodal_stats = self.multimodal_retriever.get_stats()
            
            stats = {
                "total_documents": text_stats.get("total_documents", 0),
                "total_images": multimodal_stats.get("images", 0),
                "total_items": text_stats.get("total_documents", 0) + multimodal_stats.get("images", 0),
                "text_stats": text_stats,
                "multimodal_stats": multimodal_stats,
                "directories": {
                    "text_path": self.text_path,
                    "images_path": self.images_path,
                    "mappings_path": self.mappings_path
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
    
    def rebuild_index(self) -> bool:
        """
        重建索引
        
        Returns:
            是否成功
        """
        try:
            logger.info("开始重建知识库索引...")
            
            # 清空现有索引
            self.text_retriever.clear_index()
            
            # 重新构建
            return self.build_knowledge_base()
            
        except Exception as e:
            logger.error(f"重建索引失败: {str(e)}")
            return False
