// 前端增强功能
// 提供更好的用户体验和交互

// 全局变量
let realTimeDataInterval = null;
let notificationQueue = [];

// 页面加载完成后初始化增强功能
document.addEventListener('DOMContentLoaded', function() {
    initEnhancedFeatures();
});

// 初始化增强功能
function initEnhancedFeatures() {
    console.log('初始化前端增强功能...');
    
    // 初始化实时数据更新
    initRealTimeUpdates();
    
    // 初始化通知系统
    initNotificationSystem();
    
    // 初始化搜索增强
    initSearchEnhancements();
    
    // 初始化图表功能
    initChartEnhancements();
    
    // 初始化键盘快捷键
    initKeyboardShortcuts();
    
    // 初始化响应式功能
    initResponsiveFeatures();
    
    console.log('前端增强功能初始化完成');
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次系统状态
    setInterval(updateSystemStatus, 30000);
    
    // 每60秒更新一次设备状态
    setInterval(updateEquipmentStatus, 60000);
}

// 更新系统状态
async function updateSystemStatus() {
    try {
        const response = await fetch('/api/v1/status');

        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('服务器返回非JSON响应:', text);
            throw new Error('服务器返回非JSON响应');
        }

        const data = await response.json();

        // 更新状态指示器
        updateStatusIndicators(data);

        // 更新统计数据
        updateDashboardStats(data);

        // 更新数据指示器
        updateDataIndicators(data);

    } catch (error) {
        console.error('更新系统状态失败:', error);

        // 显示错误状态
        const systemStatusElement = document.getElementById('system-status');
        if (systemStatusElement) {
            systemStatusElement.innerHTML = '<i class="bi bi-circle-fill text-danger"></i> 系统异常';
        }
    }
}

// 更新数据指示器
function updateDataIndicators(data) {
    try {
        // 更新设备数量指示器
        const equipmentCountElement = document.getElementById('equipment-count-indicator');
        if (equipmentCountElement && data.equipment_count !== undefined) {
            equipmentCountElement.textContent = data.equipment_count;
        }

        // 更新故障数量指示器
        const faultCountElement = document.getElementById('fault-count-indicator');
        if (faultCountElement && data.fault_count !== undefined) {
            faultCountElement.textContent = data.fault_count;
        }

        // 更新分析数量指示器
        const analysisCountElement = document.getElementById('analysis-count-indicator');
        if (analysisCountElement && data.analysis_count !== undefined) {
            analysisCountElement.textContent = data.analysis_count;
        }

        // 更新系统健康状态指示器
        const healthIndicatorElement = document.getElementById('health-indicator');
        if (healthIndicatorElement && data.system_health !== undefined) {
            const healthStatus = data.system_health;
            healthIndicatorElement.className = `badge ${healthStatus === 'healthy' ? 'bg-success' : 'bg-warning'}`;
            healthIndicatorElement.textContent = healthStatus === 'healthy' ? '正常' : '警告';
        }

        // 更新数据库连接状态指示器
        const dbStatusElement = document.getElementById('db-status-indicator');
        if (dbStatusElement && data.database_status !== undefined) {
            const dbStatus = data.database_status;
            dbStatusElement.className = `badge ${dbStatus === 'connected' ? 'bg-success' : 'bg-danger'}`;
            dbStatusElement.textContent = dbStatus === 'connected' ? '已连接' : '断开';
        }

        // 更新最后更新时间
        const lastUpdateElement = document.getElementById('last-update-time');
        if (lastUpdateElement) {
            lastUpdateElement.textContent = new Date().toLocaleString('zh-CN');
        }

    } catch (error) {
        console.error('更新数据指示器失败:', error);
    }
}

// 更新状态指示器
function updateStatusIndicators(data) {
    try {
        // 更新系统状态
        const systemStatusElement = document.getElementById('system-status');
        if (systemStatusElement && data.status) {
            const statusIcon = systemStatusElement.querySelector('i');
            if (data.status === 'healthy') {
                statusIcon.className = 'bi bi-circle-fill text-success';
                systemStatusElement.innerHTML = '<i class="bi bi-circle-fill text-success"></i> 系统正常';
            } else {
                statusIcon.className = 'bi bi-circle-fill text-warning';
                systemStatusElement.innerHTML = '<i class="bi bi-circle-fill text-warning"></i> 系统警告';
            }
        }

        // 更新API状态指示器
        const apiStatusElement = document.getElementById('api-status');
        if (apiStatusElement && data.api_status !== undefined) {
            const isOnline = data.api_status === 'online';
            apiStatusElement.className = `badge ${isOnline ? 'bg-success' : 'bg-danger'}`;
            apiStatusElement.textContent = isOnline ? 'API在线' : 'API离线';
        }

        // 更新服务状态指示器
        const serviceStatusElement = document.getElementById('service-status');
        if (serviceStatusElement && data.services) {
            const allServicesUp = Object.values(data.services).every(status => status === 'up');
            serviceStatusElement.className = `badge ${allServicesUp ? 'bg-success' : 'bg-warning'}`;
            serviceStatusElement.textContent = allServicesUp ? '服务正常' : '服务异常';
        }

    } catch (error) {
        console.error('更新状态指示器失败:', error);
    }
}

// 更新仪表盘统计数据
function updateDashboardStats(data) {
    try {
        // 更新设备总数
        const totalEquipmentElement = document.getElementById('total-equipment');
        if (totalEquipmentElement && data.equipment_count !== undefined) {
            totalEquipmentElement.textContent = data.equipment_count;
        }

        // 更新故障总数
        const faultCountElement = document.getElementById('fault-count');
        if (faultCountElement && data.fault_count !== undefined) {
            faultCountElement.textContent = data.fault_count;
        }

        // 更新分析总数
        const analysisCountElement = document.getElementById('analysis-count');
        if (analysisCountElement && data.analysis_count !== undefined) {
            analysisCountElement.textContent = data.analysis_count;
        }

        // 更新成功率
        const successRateElement = document.getElementById('success-rate');
        if (successRateElement && data.success_rate !== undefined) {
            successRateElement.textContent = data.success_rate + '%';
        }

        // 更新在线设备数
        const onlineEquipmentElement = document.getElementById('online-equipment');
        if (onlineEquipmentElement && data.online_equipment !== undefined) {
            onlineEquipmentElement.textContent = data.online_equipment;
        }

        // 更新离线设备数
        const offlineEquipmentElement = document.getElementById('offline-equipment');
        if (offlineEquipmentElement && data.offline_equipment !== undefined) {
            offlineEquipmentElement.textContent = data.offline_equipment;
        }

    } catch (error) {
        console.error('更新仪表盘统计失败:', error);
    }
}

// 更新设备状态
async function updateEquipmentStatus() {
    try {
        const response = await fetch('/api/v1/equipment');

        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('设备API返回非JSON响应:', text);
            throw new Error('设备API返回非JSON响应');
        }

        const data = await response.json();

        // 检查设备状态变化
        if (data.equipment) {
            checkEquipmentStatusChanges(data.equipment);
        }

    } catch (error) {
        console.error('更新设备状态失败:', error);
    }
}

// 检查设备状态变化
function checkEquipmentStatusChanges(newEquipmentList) {
    if (!window.equipmentList) return;
    
    newEquipmentList.forEach(newEquipment => {
        const oldEquipment = window.equipmentList.find(eq => eq.id === newEquipment.id);
        
        if (oldEquipment && oldEquipment.status !== newEquipment.status) {
            // 设备状态发生变化，显示通知
            showEnhancedNotification(
                `设备状态变化`,
                `${newEquipment.name} 状态从 ${oldEquipment.status} 变为 ${newEquipment.status}`,
                getStatusNotificationType(newEquipment.status)
            );
        }
    });
    
    // 更新全局设备列表
    window.equipmentList = newEquipmentList;
}

// 获取状态通知类型
function getStatusNotificationType(status) {
    switch(status) {
        case 'fault': return 'error';
        case 'warning': return 'warning';
        case 'maintenance': return 'info';
        case 'normal': return 'success';
        default: return 'info';
    }
}

// 初始化通知系统
function initNotificationSystem() {
    // 创建通知容器
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
}

// 显示增强通知
function showEnhancedNotification(title, message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notificationId = 'notification-' + Date.now();
    const notification = document.createElement('div');
    notification.id = notificationId;
    notification.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    notification.setAttribute('role', 'alert');
    
    notification.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                    data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // 使用Bootstrap Toast显示
    const bsToast = new bootstrap.Toast(notification, {
        autohide: true,
        delay: duration
    });
    
    bsToast.show();
    
    // 自动移除
    setTimeout(() => {
        if (document.getElementById(notificationId)) {
            notification.remove();
        }
    }, duration + 1000);
}

// 初始化搜索增强
function initSearchEnhancements() {
    // 设备搜索增强
    const equipmentSearch = document.getElementById('equipment-search');
    if (equipmentSearch) {
        let searchTimeout;
        
        equipmentSearch.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performEnhancedEquipmentSearch(e.target.value);
            }, 300);
        });
    }
    
    // 知识库搜索增强
    const knowledgeSearch = document.getElementById('knowledge-search');
    if (knowledgeSearch) {
        let searchTimeout;
        
        knowledgeSearch.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performEnhancedKnowledgeSearch(e.target.value);
            }, 500);
        });
    }
}

// 执行增强的设备搜索
async function performEnhancedEquipmentSearch(query) {
    if (!query.trim()) {
        // 如果搜索为空，显示所有设备
        if (window.loadEquipmentList) {
            window.loadEquipmentList();
        }
        return;
    }
    
    try {
        // 本地搜索
        const filteredEquipment = window.equipmentList?.filter(equipment => 
            equipment.name.toLowerCase().includes(query.toLowerCase()) ||
            equipment.type.toLowerCase().includes(query.toLowerCase()) ||
            equipment.location.toLowerCase().includes(query.toLowerCase())
        ) || [];
        
        // 更新显示
        if (window.updateEquipmentTableDisplay) {
            window.updateEquipmentTableDisplay(filteredEquipment);
        }
        
        // 显示搜索结果统计
        showSearchResultStats('设备', filteredEquipment.length, query);
        
    } catch (error) {
        console.error('设备搜索失败:', error);
    }
}

// 执行增强的知识库搜索
async function performEnhancedKnowledgeSearch(query) {
    if (!query.trim()) return;
    
    try {
        const response = await fetch(`/api/v1/knowledge/search?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        
        // 显示搜索结果
        displayKnowledgeSearchResults(data.results, query);
        
        // 显示搜索统计
        showSearchResultStats('知识库', data.results.length, query, data.search_time);
        
    } catch (error) {
        console.error('知识库搜索失败:', error);
        showEnhancedNotification('搜索失败', '知识库搜索服务暂时不可用', 'error');
    }
}

// 显示知识库搜索结果
function displayKnowledgeSearchResults(results, query) {
    const resultsContainer = document.getElementById('knowledge-search-results');
    if (!resultsContainer) return;
    
    if (results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                没有找到与 "${query}" 相关的内容
            </div>
        `;
        return;
    }
    
    const resultsHtml = results.map(result => `
        <div class="card mb-3">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-${getResultIcon(result.type)}"></i>
                    ${result.title}
                </h6>
                <p class="card-text">${result.content}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        来源: ${result.source} | 相关度: ${(result.relevance * 100).toFixed(0)}%
                    </small>
                    <small class="text-muted">${result.last_updated}</small>
                </div>
            </div>
        </div>
    `).join('');
    
    resultsContainer.innerHTML = resultsHtml;
}

// 获取结果图标
function getResultIcon(type) {
    switch(type) {
        case 'document': return 'file-text';
        case 'case_study': return 'clipboard-list';
        case 'prevention': return 'shield-alt';
        default: return 'info-circle';
    }
}

// 显示搜索结果统计
function showSearchResultStats(type, count, query, searchTime = null) {
    const statsElement = document.getElementById(`${type.toLowerCase()}-search-stats`);
    if (statsElement) {
        const timeInfo = searchTime ? ` (${searchTime})` : '';
        statsElement.textContent = `找到 ${count} 个与 "${query}" 相关的${type}结果${timeInfo}`;
    }
}

// 初始化图表增强 - 已禁用，避免与现代化设备管理界面冲突
function initChartEnhancements() {
    // 图表功能已集成到现代化设备管理界面中，此处不再初始化独立图表
    console.log('initChartEnhancements: 图表功能已集成到现代化界面中');
}

// 废弃的图表函数已删除 - 所有图表功能已完全集成到现代化设备统计显示中

// 初始化键盘快捷键
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + / 显示快捷键帮助
        if (e.ctrlKey && e.key === '/') {
            e.preventDefault();
            showKeyboardShortcutsHelp();
        }
        
        // Ctrl + F 聚焦搜索框
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            focusCurrentSearchBox();
        }
        
        // Esc 关闭模态框
        if (e.key === 'Escape') {
            closeTopModal();
        }
    });
}

// 聚焦当前标签页的搜索框
function focusCurrentSearchBox() {
    const currentTab = document.querySelector('.tab-pane.active');
    if (currentTab) {
        const searchBox = currentTab.querySelector('input[type="search"], input[type="text"]');
        if (searchBox) {
            searchBox.focus();
        }
    }
}

// 关闭顶层模态框
function closeTopModal() {
    const modals = document.querySelectorAll('.modal.show');
    if (modals.length > 0) {
        const topModal = modals[modals.length - 1];
        const bsModal = bootstrap.Modal.getInstance(topModal);
        if (bsModal) {
            bsModal.hide();
        }
    }
}

// 显示键盘快捷键帮助
function showKeyboardShortcutsHelp() {
    showEnhancedNotification(
        '键盘快捷键',
        'Ctrl+F: 搜索 | Ctrl+/: 帮助 | Esc: 关闭',
        'info',
        3000
    );
}

// 初始化响应式功能
function initResponsiveFeatures() {
    // 监听窗口大小变化
    window.addEventListener('resize', handleWindowResize);
    
    // 初始调用
    handleWindowResize();
}

// 处理窗口大小变化
function handleWindowResize() {
    const width = window.innerWidth;
    
    // 在小屏幕上隐藏某些列
    const tables = document.querySelectorAll('table.table-responsive');
    tables.forEach(table => {
        const hiddenCols = table.querySelectorAll('.d-none.d-md-table-cell');
        hiddenCols.forEach(col => {
            if (width < 768) {
                col.style.display = 'none';
            } else {
                col.style.display = '';
            }
        });
    });
}

// 导出增强功能到全局
window.EnhancedFeatures = {
    showNotification: showEnhancedNotification,
    updateSystemStatus: updateSystemStatus,
    performEquipmentSearch: performEnhancedEquipmentSearch,
    performKnowledgeSearch: performEnhancedKnowledgeSearch
};
