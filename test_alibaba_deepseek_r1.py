#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云DashScope DeepSeek-R1 专项测试
验证阿里云特有的响应格式处理
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_alibaba_deepseek_r1_complete_response():
    """测试阿里云DeepSeek-R1的完整响应"""
    print("🧪 测试阿里云DeepSeek-R1完整响应")
    
    try:
        # 阿里云DashScope配置
        api_key = "sk-a85369c572d34db5a9880547ebf0a021"
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试请求 - 让模型完整回答
        payload = {
            "model": "deepseek-r1",
            "messages": [
                {
                    "role": "user", 
                    "content": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象，请分析故障原因并给出处理建议"
                }
            ],
            "temperature": 0.3,
            "max_tokens": 8000,  # 增加token限制
            "stream": True
        }
        
        print(f"🌊 发送完整请求...")
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            stream=True,
            timeout=120  # 增加超时时间
        )
        
        if response.status_code != 200:
            print(f"❌ API调用失败: {response.text}")
            return False
        
        # 收集完整响应
        all_reasoning_content = ""
        chunk_count = 0
        
        print(f"🔍 开始收集完整响应...")
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    if data_str.strip() == '[DONE]':
                        print(f"🏁 响应完成")
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            
                            # 收集reasoning_content
                            if 'reasoning_content' in delta and delta['reasoning_content']:
                                reasoning_chunk = delta['reasoning_content']
                                all_reasoning_content += reasoning_chunk
                                
                                # 每100个chunk显示一次进度
                                if chunk_count % 100 == 0:
                                    print(f"📊 已处理 {chunk_count} chunks, 总长度: {len(all_reasoning_content)} 字符")
                        
                        # 限制最大chunk数，避免无限等待
                        if chunk_count > 1000:
                            print("⏰ 达到最大chunk限制，停止收集")
                            break
                            
                    except json.JSONDecodeError as e:
                        continue
        
        print(f"\n📊 完整响应分析:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   总内容长度: {len(all_reasoning_content)} 字符")
        
        if len(all_reasoning_content) > 500:
            print(f"   内容开头: {all_reasoning_content[:200]}...")
            print(f"   内容结尾: {all_reasoning_content[-200:]}")
            
            # 分析内容结构
            has_thinking_tags = '<thinking>' in all_reasoning_content and '</thinking>' in all_reasoning_content
            has_conclusion_markers = any(marker in all_reasoning_content for marker in [
                "基于上述分析", "综合以上", "因此", "所以", "总结", "建议", "处理方案"
            ])
            
            print(f"   包含thinking标签: {has_thinking_tags}")
            print(f"   包含结论标记: {has_conclusion_markers}")
            
            # 测试智能分离
            print(f"\n🔄 测试智能分离功能...")
            try:
                from ui.app import smart_split_reasoning_and_result
                reasoning_part, final_part = smart_split_reasoning_and_result(all_reasoning_content)
                
                print(f"   分离后推理部分: {len(reasoning_part)} 字符")
                print(f"   分离后最终部分: {len(final_part)} 字符")
                
                if final_part:
                    print(f"   最终部分预览: {final_part[:150]}...")
                    return True
                else:
                    print("❌ 智能分离未产生最终结果")
                    return False
                    
            except Exception as e:
                print(f"❌ 智能分离测试失败: {e}")
                return False
        else:
            print("❌ 响应内容太短，可能存在问题")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_web_interface_with_fix():
    """测试修复后的Web界面"""
    print("\n🧪 测试修复后的Web界面")
    
    try:
        # 等待服务器启动
        time.sleep(3)
        
        # 测试健康检查
        health_response = requests.get("http://localhost:5002/health", timeout=10)
        if health_response.status_code != 200:
            print("❌ Web服务器未启动")
            return False
        
        print("✅ Web服务器运行正常")
        
        # 测试DeepSeek-R1分析接口
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象，请分析故障原因",
            "thinking_mode": True
        }
        
        print(f"🔍 发送Web界面测试请求...")
        
        # 使用流式请求测试
        response = requests.post(
            "http://localhost:5002/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        if response.status_code != 200:
            print(f"❌ 流式分析请求失败: {response.status_code}")
            return False
        
        # 分析流式响应
        reasoning_parts = []
        final_parts = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if chunk_data.get('type') == 'reasoning':
                            content = chunk_data.get('content', '')
                            reasoning_parts.append(content)
                            if len(reasoning_parts) <= 5:  # 只显示前5个
                                print(f"🧠 推理部分 {len(reasoning_parts)}: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'final':
                            content = chunk_data.get('content', '')
                            final_parts.append(content)
                            print(f"📋 最终部分 {len(final_parts)}: {len(content)} 字符")
                            if len(final_parts) <= 3:  # 显示前3个最终部分
                                print(f"   内容: {content[:100]}...")
                        
                        elif chunk_data.get('type') == 'complete':
                            print("🏁 分析完成")
                            break
                        
                        elif chunk_data.get('type') == 'error':
                            print(f"❌ 分析错误: {chunk_data.get('message')}")
                            return False
                        
                        # 限制测试时间
                        if chunk_count > 100:
                            print("⏰ 达到测试限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        continue
        
        # 分析结果
        print(f"\n📊 Web界面处理结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理部分数: {len(reasoning_parts)}")
        print(f"   最终部分数: {len(final_parts)}")
        
        success = True
        
        if reasoning_parts:
            total_reasoning = ''.join(reasoning_parts)
            print(f"   推理总长度: {len(total_reasoning)} 字符")
        else:
            print("❌ 没有收到推理内容")
            success = False
        
        if final_parts:
            total_final = ''.join(final_parts)
            print(f"   最终总长度: {len(total_final)} 字符")
            print(f"   最终内容预览: {total_final[:200]}...")
        else:
            print("❌ 没有收到最终内容")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 阿里云DashScope DeepSeek-R1 专项测试")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    print("第一步：测试阿里云DeepSeek-R1完整响应")
    api_result = test_alibaba_deepseek_r1_complete_response()
    test_results.append(("阿里云API完整响应", api_result))
    
    print("\n第二步：测试修复后的Web界面")
    web_result = test_web_interface_with_fix()
    test_results.append(("修复后Web界面", web_result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 阿里云DeepSeek-R1修复成功！")
        print("\n📋 关键发现:")
        print("✅ 阿里云DashScope只返回reasoning_content字段")
        print("✅ 智能分离功能能正确处理阿里云格式")
        print("✅ Web界面能正确分离思考过程和最终结果")
        print("✅ 修复后的系统完全兼容阿里云DeepSeek-R1")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return all_passed

if __name__ == "__main__":
    main()
