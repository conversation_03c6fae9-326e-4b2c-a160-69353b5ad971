# FAISS故障诊断索引文件 (二进制格式模拟)
# 专门用于故障诊断知识的向量索引

FAISS_INDEX_HEADER:
  version: 1.7.4
  index_type: IndexHNSWFlat
  dimension: 384
  total_vectors: 1800
  metric_type: METRIC_INNER_PRODUCT
  created_time: 2024-01-15T17:15:00Z
  index_name: fault_diagnosis_index

INDEX_METADATA:
  training_size: 1800
  is_trained: true
  ntotal: 1800
  d: 384
  M: 16
  ef_construction: 200
  ef_search: 100

FAULT_CATEGORIES:
  - electrical_faults: 600 vectors
  - mechanical_faults: 450 vectors
  - thermal_faults: 350 vectors
  - insulation_faults: 250 vectors
  - protection_faults: 150 vectors

SEVERITY_DISTRIBUTION:
  - critical: 300 vectors
  - major: 450 vectors
  - moderate: 600 vectors
  - minor: 450 vectors

VECTOR_DATA:
  # 故障诊断向量数据
  # 按故障类型和严重程度组织
  
  # 电气故障向量 (0-599)
  vector_0: [0.2891, -0.1567, 0.4234, 0.0789, ...]  # 短路故障
  vector_1: [0.3902, -0.2678, 0.5345, 0.1890, ...]  # 接地故障
  vector_2: [0.4013, -0.3789, 0.6456, 0.2901, ...]  # 过电压故障
  ...
  
  # 机械故障向量 (600-1049)
  vector_600: [0.5124, -0.4890, 0.7567, 0.4012, ...]  # 机构卡涩
  vector_601: [0.6235, -0.5901, 0.8678, 0.5123, ...]  # 触头磨损
  vector_602: [0.7346, -0.6012, 0.9789, 0.6234, ...]  # 弹簧疲劳
  ...
  
  # 热故障向量 (1050-1399)
  vector_1050: [0.8457, -0.7123, 0.0890, 0.7345, ...]  # 过热故障
  vector_1051: [0.9568, -0.8234, 0.1901, 0.8456, ...]  # 局部过热
  vector_1052: [0.0679, -0.9345, 0.2012, 0.9567, ...]  # 冷却故障
  ...

DIAGNOSTIC_PATTERNS:
  symptom_patterns: 450
  cause_patterns: 380
  solution_patterns: 520
  prevention_patterns: 450

INDEX_STATISTICS:
  memory_usage: 2.7MB
  search_time_avg: 0.8ms
  build_time: 12.3s
  last_updated: 2024-01-15T17:15:00Z
  graph_connectivity: 0.95

SEARCH_PERFORMANCE:
  recall_at_1: 0.93
  recall_at_5: 0.97
  recall_at_10: 0.99
  precision_at_5: 0.89
  precision_at_10: 0.85
  
DIAGNOSTIC_ACCURACY:
  fault_type_accuracy: 0.91
  severity_accuracy: 0.87
  cause_identification: 0.84
  solution_relevance: 0.88

# 注意：这只是示例文本表示
# 实际FAISS文件是二进制格式，需要使用faiss库读取
