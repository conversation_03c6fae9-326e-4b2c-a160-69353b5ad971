# 故障分析智能助手 - 部署指南

本文档详细介绍了故障分析智能助手系统的各种部署方式和配置选项。

## 目录

- [快速开始](#快速开始)
- [Docker部署](#docker部署)
- [Kubernetes部署](#kubernetes部署)
- [生产环境配置](#生产环境配置)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)

## 快速开始

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB可用内存
- 至少10GB可用磁盘空间

### 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd fault-diagnosis-assistant

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 DEEPSEEK_API_KEY

# 一键部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

部署完成后访问：
- Web界面: https://localhost
- API文档: https://localhost/docs

## Docker部署

### 开发环境

```bash
# 启动开发环境
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境

```bash
# 使用生产配置
docker-compose -f docker-compose.prod.yml up -d

# 启用监控服务
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# 启用日志收集
docker-compose -f docker-compose.prod.yml --profile logging up -d
```

### 服务组件

| 服务 | 端口 | 描述 |
|------|------|------|
| fault-diagnosis-app | 8000, 3000 | 主应用服务 |
| redis | 6379 | 缓存服务 |
| postgres | 5432 | 数据库服务 |
| nginx | 80, 443 | 反向代理 |
| prometheus | 9090 | 监控服务 |
| grafana | 3001 | 可视化面板 |

## Kubernetes部署

### 前置要求

- Kubernetes 1.20+
- kubectl 配置正确
- Ingress Controller (如 nginx-ingress)
- 存储类 (StorageClass)

### 部署步骤

```bash
# 设置镜像仓库
export DOCKER_REGISTRY=your-registry.com

# 一键部署到K8s
chmod +x scripts/k8s-deploy.sh
./scripts/k8s-deploy.sh deploy

# 查看部署状态
./scripts/k8s-deploy.sh status

# 扩缩容
./scripts/k8s-deploy.sh scale 5
```

### 手动部署

```bash
# 创建命名空间和配置
kubectl apply -f k8s/namespace.yaml

# 部署数据库
kubectl apply -f k8s/database.yaml

# 部署应用
kubectl apply -f k8s/app-deployment.yaml

# 部署Ingress
kubectl apply -f k8s/ingress.yaml
```

## 生产环境配置

### 环境变量配置

关键环境变量：

```bash
# LLM配置
DEEPSEEK_API_KEY=your_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 数据库配置
DATABASE_URL=********************************/db
REDIS_URL=redis://:password@host:6379/0

# 安全配置
SECRET_KEY=your_secret_key
JWT_SECRET_KEY=your_jwt_secret

# 性能配置
WORKER_PROCESSES=4
LOG_LEVEL=WARNING
```

### SSL证书配置

#### 自签名证书（开发/测试）

```bash
cd nginx
chmod +x generate-ssl.sh
./generate-ssl.sh
```

#### Let's Encrypt证书（生产）

```bash
# 安装certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 复制证书
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/key.pem
```

### 数据备份

#### PostgreSQL备份

```bash
# 创建备份
docker exec fault-diagnosis-postgres-prod pg_dump -U postgres fault_diagnosis > backup.sql

# 恢复备份
docker exec -i fault-diagnosis-postgres-prod psql -U postgres fault_diagnosis < backup.sql
```

#### 数据卷备份

```bash
# 备份数据卷
docker run --rm -v fault-diagnosis_app_data:/data -v $(pwd):/backup alpine tar czf /backup/app_data.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v fault-diagnosis_app_data:/data -v $(pwd):/backup alpine tar xzf /backup/app_data.tar.gz -C /data
```

## 监控和日志

### Prometheus监控

访问 http://localhost:9090 查看Prometheus监控面板。

关键指标：
- 应用响应时间
- 错误率
- 资源使用率
- 数据库连接数

### Grafana可视化

访问 http://localhost:3001 查看Grafana面板。

默认登录：
- 用户名: admin
- 密码: admin（首次登录后需修改）

### 日志管理

#### 查看日志

```bash
# Docker Compose
docker-compose logs -f fault-diagnosis-app

# Kubernetes
kubectl logs -f deployment/fault-diagnosis-app -n fault-diagnosis
```

#### 日志配置

日志级别：
- DEBUG: 详细调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息

## 故障排除

### 常见问题

#### 1. 应用启动失败

```bash
# 检查日志
docker-compose logs fault-diagnosis-app

# 常见原因：
# - DEEPSEEK_API_KEY未设置
# - 数据库连接失败
# - 端口被占用
```

#### 2. 数据库连接失败

```bash
# 检查数据库状态
docker-compose ps postgres

# 检查数据库日志
docker-compose logs postgres

# 测试连接
docker exec -it fault-diagnosis-postgres psql -U postgres -d fault_diagnosis
```

#### 3. Redis连接失败

```bash
# 检查Redis状态
docker-compose ps redis

# 测试连接
docker exec -it fault-diagnosis-redis redis-cli ping
```

#### 4. SSL证书问题

```bash
# 重新生成证书
cd nginx
rm -rf ssl/
./generate-ssl.sh

# 重启nginx
docker-compose restart nginx
```

### 性能优化

#### 1. 内存优化

```bash
# 调整JVM参数（如果使用Java组件）
export JAVA_OPTS="-Xms2g -Xmx4g"

# 调整Redis内存限制
# 在redis.conf中设置: maxmemory 512mb
```

#### 2. 数据库优化

```sql
-- PostgreSQL优化
-- 在postgresql.conf中调整：
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
```

#### 3. 应用优化

```bash
# 增加worker进程数
export WORKER_PROCESSES=8

# 启用缓存
export ENABLE_CACHE=true
export CACHE_TTL=3600
```

### 健康检查

```bash
# 检查应用健康状态
curl -f http://localhost:8000/health

# 检查各组件状态
curl -f http://localhost:8000/api/v1/system/components
```

### 联系支持

如果遇到无法解决的问题，请提供以下信息：

1. 错误日志
2. 系统配置
3. 部署环境信息
4. 复现步骤

## 更新和维护

### 应用更新

```bash
# Docker Compose
./scripts/deploy.sh update

# Kubernetes
./scripts/k8s-deploy.sh update
```

### 定期维护

1. 定期备份数据
2. 更新SSL证书
3. 清理日志文件
4. 监控资源使用
5. 更新依赖包
