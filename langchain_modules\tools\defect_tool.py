"""
缺陷检测工具

为LangChain提供设备缺陷检测功能
"""

from typing import Dict, Any, List
from langchain.tools import BaseTool
from pydantic import Field
from loguru import logger

from data_processing.image_processor import ImageProcessor


class DefectDetectionTool(BaseTool):
    """缺陷检测工具"""
    
    name: str = "defect_detection_tool"
    description: str = """
    设备缺陷检测工具。用于检测图像中的设备缺陷，如锈蚀、裂纹、泄漏等。
    输入: 图像文件路径
    输出: 检测到的缺陷信息，包括缺陷类型、位置和置信度
    """
    
    image_processor: ImageProcessor = Field(exclude=True)
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        image_processor = ImageProcessor(config.get("image", {}))
        super().__init__(image_processor=image_processor, **kwargs)
    
    def _run(self, image_path: str) -> str:
        """
        执行缺陷检测
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            缺陷检测结果的字符串描述
        """
        try:
            defects = self.image_processor.detect_defects(image_path)
            
            if not defects or defects.get("total_defects", 0) == 0:
                return "未检测到明显的设备缺陷"
            
            # 格式化输出
            output_lines = [f"检测到 {defects['total_defects']} 个潜在缺陷:"]
            
            # 锈蚀检测结果
            rust_areas = defects.get("rust", [])
            if rust_areas:
                output_lines.append(f"\n锈蚀区域 ({len(rust_areas)} 个):")
                for i, rust in enumerate(rust_areas[:5]):  # 最多显示5个
                    bbox = rust.get("bbox", [0, 0, 0, 0])
                    area = rust.get("area", 0)
                    confidence = rust.get("confidence", 0.0)
                    output_lines.append(f"  {i+1}. 位置: ({bbox[0]}, {bbox[1]}, {bbox[2]}, {bbox[3]}), 面积: {area:.0f}, 置信度: {confidence:.2f}")
            
            # 裂纹检测结果
            crack_areas = defects.get("cracks", [])
            if crack_areas:
                output_lines.append(f"\n裂纹区域 ({len(crack_areas)} 个):")
                for i, crack in enumerate(crack_areas[:5]):
                    bbox = crack.get("bbox", [0, 0, 0, 0])
                    length = crack.get("length", 0)
                    confidence = crack.get("confidence", 0.0)
                    output_lines.append(f"  {i+1}. 位置: ({bbox[0]}, {bbox[1]}, {bbox[2]}, {bbox[3]}), 长度: {length:.0f}, 置信度: {confidence:.2f}")
            
            # 泄漏检测结果
            leak_areas = defects.get("leaks", [])
            if leak_areas:
                output_lines.append(f"\n泄漏区域 ({len(leak_areas)} 个):")
                for i, leak in enumerate(leak_areas[:5]):
                    bbox = leak.get("bbox", [0, 0, 0, 0])
                    area = leak.get("area", 0)
                    confidence = leak.get("confidence", 0.0)
                    output_lines.append(f"  {i+1}. 位置: ({bbox[0]}, {bbox[1]}, {bbox[2]}, {bbox[3]}), 面积: {area:.0f}, 置信度: {confidence:.2f}")
            
            return "\n".join(output_lines)
            
        except Exception as e:
            logger.error(f"缺陷检测工具执行失败: {str(e)}")
            return f"缺陷检测失败: {str(e)}"
    
    async def _arun(self, image_path: str) -> str:
        """异步执行缺陷检测"""
        return self._run(image_path)


class ImageAnalysisTool(BaseTool):
    """图像分析工具"""
    
    name: str = "image_analysis_tool"
    description: str = """
    图像分析工具。用于分析图像的基本特征，如颜色、纹理、边缘等。
    输入: 图像文件路径
    输出: 图像特征分析结果
    """
    
    image_processor: ImageProcessor = Field(exclude=True)
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        image_processor = ImageProcessor(config.get("image", {}))
        super().__init__(image_processor=image_processor, **kwargs)
    
    def _run(self, image_path: str) -> str:
        """
        执行图像分析
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像分析结果的字符串描述
        """
        try:
            result = self.image_processor.process_image(image_path)
            
            if not result or not result.get("features"):
                return "图像分析失败，无法提取特征"
            
            features = result.get("features", {})
            metadata = result.get("metadata", {})
            
            # 格式化输出
            output_lines = ["图像分析结果:"]
            
            # 基本信息
            output_lines.append(f"文件名: {metadata.get('file_name', 'unknown')}")
            output_lines.append(f"文件大小: {metadata.get('file_size', 0)} 字节")
            output_lines.append(f"原始尺寸: {result.get('original_size', 'unknown')}")
            output_lines.append(f"处理后尺寸: {result.get('processed_size', 'unknown')}")
            
            # 基础特征
            basic = features.get("basic", {})
            if basic:
                output_lines.append(f"\n基础特征:")
                output_lines.append(f"  宽度: {basic.get('width', 0)}")
                output_lines.append(f"  高度: {basic.get('height', 0)}")
                output_lines.append(f"  宽高比: {basic.get('aspect_ratio', 0.0):.2f}")
                output_lines.append(f"  面积: {basic.get('area', 0)}")
            
            # 颜色特征
            color = features.get("color", {})
            if color:
                mean_bgr = color.get("mean_bgr", [0, 0, 0])
                output_lines.append(f"\n颜色特征:")
                output_lines.append(f"  平均BGR值: ({mean_bgr[0]:.1f}, {mean_bgr[1]:.1f}, {mean_bgr[2]:.1f})")
            
            # 纹理特征
            texture = features.get("texture", {})
            if texture:
                output_lines.append(f"\n纹理特征:")
                output_lines.append(f"  纹理均值: {texture.get('mean', 0.0):.2f}")
                output_lines.append(f"  纹理标准差: {texture.get('std', 0.0):.2f}")
                output_lines.append(f"  纹理熵: {texture.get('entropy', 0.0):.2f}")
            
            # 边缘特征
            edge = features.get("edge", {})
            if edge:
                output_lines.append(f"\n边缘特征:")
                output_lines.append(f"  边缘密度: {edge.get('edge_density', 0.0):.4f}")
                output_lines.append(f"  边缘总数: {edge.get('total_edges', 0)}")
            
            return "\n".join(output_lines)
            
        except Exception as e:
            logger.error(f"图像分析工具执行失败: {str(e)}")
            return f"图像分析失败: {str(e)}"
    
    async def _arun(self, image_path: str) -> str:
        """异步执行图像分析"""
        return self._run(image_path)
