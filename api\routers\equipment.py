"""
设备管理API路由
"""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Query
from loguru import logger

from ..models import (
    EquipmentInfo,
    EquipmentSearchRequest,
    EquipmentSearchResponse,
    EquipmentStatusUpdate,
    MaintenanceScheduleResponse,
    BaseResponse
)


router = APIRouter()


def get_equipment_manager():
    """获取设备管理器依赖"""
    from ..main import app
    return app.get_system_component("equipment_manager")


@router.get("/", response_model=EquipmentSearchResponse)
async def list_equipment(
    query: str = Query(None, description="搜索关键词"),
    type: str = Query(None, description="设备类型"),
    voltage_level: str = Query(None, description="电压等级"),
    status: str = Query(None, description="状态"),
    location: str = Query(None, description="位置"),
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取设备列表
    
    支持多种条件筛选和搜索
    """
    try:
        logger.info(f"获取设备列表，查询条件: query={query}, type={type}, voltage_level={voltage_level}")
        
        # 根据不同条件获取设备
        if query:
            equipment_list = equipment_manager.search_equipment(query)
        elif type:
            equipment_list = equipment_manager.get_equipment_by_type(type)
        elif voltage_level:
            equipment_list = equipment_manager.get_equipment_by_voltage_level(voltage_level)
        else:
            # 获取所有设备
            equipment_list = list(equipment_manager.equipment_db.values())
        
        # 状态过滤
        if status:
            equipment_list = [eq for eq in equipment_list if eq.get("status") == status]
        
        # 位置过滤
        if location:
            equipment_list = [eq for eq in equipment_list if location.lower() in eq.get("location", "").lower()]
        
        # 分页
        total_count = len(equipment_list)
        equipment_list = equipment_list[offset:offset + limit]
        
        # 转换为响应模型
        equipment_info_list = []
        for eq in equipment_list:
            equipment_info = EquipmentInfo(
                id=eq.get("id", ""),
                name=eq.get("name", ""),
                type=eq.get("type", ""),
                voltage_level=eq.get("voltage_level"),
                manufacturer=eq.get("manufacturer"),
                install_date=eq.get("install_date"),
                location=eq.get("location"),
                status=eq.get("status", "unknown"),
                specifications=eq.get("specifications")
            )
            equipment_info_list.append(equipment_info)
        
        return EquipmentSearchResponse(
            success=True,
            message=f"找到 {total_count} 个设备",
            equipment_list=equipment_info_list,
            total_count=total_count
        )
        
    except Exception as e:
        logger.error(f"获取设备列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")


@router.get("/{equipment_id}", response_model=Dict[str, Any])
async def get_equipment_detail(
    equipment_id: str,
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取设备详细信息
    
    根据设备ID获取完整的设备信息
    """
    try:
        logger.info(f"获取设备详情: {equipment_id}")
        
        equipment_info = equipment_manager.get_equipment_info(equipment_id)
        
        if not equipment_info:
            raise HTTPException(status_code=404, detail=f"设备不存在: {equipment_id}")
        
        # 获取状态历史
        status_history = equipment_manager.get_equipment_status_history(equipment_id, days=30)
        
        return {
            "success": True,
            "message": "设备信息获取成功",
            "equipment": equipment_info,
            "status_history": status_history[-10:]  # 最近10条状态记录
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备详情错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备详情失败: {str(e)}")


@router.post("/", response_model=BaseResponse)
async def create_equipment(
    equipment: EquipmentInfo,
    equipment_manager=Depends(get_equipment_manager)
):
    """
    创建新设备
    
    添加新的设备信息到系统
    """
    try:
        logger.info(f"创建设备: {equipment.id}")
        
        # 转换为字典格式
        equipment_dict = equipment.dict()
        
        # 添加设备
        success = equipment_manager.add_equipment(equipment_dict)
        
        if success:
            return BaseResponse(
                success=True,
                message=f"设备 {equipment.id} 创建成功"
            )
        else:
            raise HTTPException(status_code=400, detail="设备创建失败，可能ID已存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建设备错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建设备失败: {str(e)}")


@router.put("/{equipment_id}", response_model=BaseResponse)
async def update_equipment(
    equipment_id: str,
    updates: Dict[str, Any],
    equipment_manager=Depends(get_equipment_manager)
):
    """
    更新设备信息
    
    更新指定设备的信息
    """
    try:
        logger.info(f"更新设备: {equipment_id}")
        
        # 检查设备是否存在
        if not equipment_manager.get_equipment_info(equipment_id):
            raise HTTPException(status_code=404, detail=f"设备不存在: {equipment_id}")
        
        # 更新设备信息
        success = equipment_manager.update_equipment(equipment_id, updates)
        
        if success:
            return BaseResponse(
                success=True,
                message=f"设备 {equipment_id} 更新成功"
            )
        else:
            raise HTTPException(status_code=400, detail="设备更新失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新设备错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新设备失败: {str(e)}")


@router.post("/status", response_model=BaseResponse)
async def update_equipment_status(
    status_update: EquipmentStatusUpdate,
    equipment_manager=Depends(get_equipment_manager)
):
    """
    更新设备状态
    
    更新设备的运行状态
    """
    try:
        logger.info(f"更新设备状态: {status_update.equipment_id} -> {status_update.status}")
        
        # 更新设备状态
        success = equipment_manager.update_equipment_status(
            equipment_id=status_update.equipment_id,
            status=status_update.status,
            notes=status_update.notes
        )
        
        if success:
            return BaseResponse(
                success=True,
                message=f"设备 {status_update.equipment_id} 状态已更新为 {status_update.status}"
            )
        else:
            raise HTTPException(status_code=400, detail="设备状态更新失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新设备状态错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新设备状态失败: {str(e)}")


@router.get("/{equipment_id}/history", response_model=Dict[str, Any])
async def get_equipment_history(
    equipment_id: str,
    days: int = Query(30, description="查询天数"),
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取设备状态历史
    
    获取指定设备的状态变更历史
    """
    try:
        logger.info(f"获取设备状态历史: {equipment_id}, 天数: {days}")
        
        # 检查设备是否存在
        if not equipment_manager.get_equipment_info(equipment_id):
            raise HTTPException(status_code=404, detail=f"设备不存在: {equipment_id}")
        
        # 获取状态历史
        history = equipment_manager.get_equipment_status_history(equipment_id, days)
        
        return {
            "success": True,
            "message": "状态历史获取成功",
            "equipment_id": equipment_id,
            "history": history,
            "total_records": len(history)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备状态历史错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备状态历史失败: {str(e)}")


@router.get("/maintenance/schedule", response_model=MaintenanceScheduleResponse)
async def get_maintenance_schedule(
    days_ahead: int = Query(30, description="提前天数"),
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取维护计划
    
    获取即将到期的设备维护计划
    """
    try:
        logger.info(f"获取维护计划，提前天数: {days_ahead}")
        
        # 获取维护计划
        schedule = equipment_manager.get_maintenance_schedule(days_ahead)
        
        return MaintenanceScheduleResponse(
            success=True,
            message=f"找到 {len(schedule)} 个维护计划",
            schedule=schedule,
            total_count=len(schedule)
        )
        
    except Exception as e:
        logger.error(f"获取维护计划错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取维护计划失败: {str(e)}")


@router.get("/statistics/overview", response_model=Dict[str, Any])
async def get_equipment_statistics(
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取设备统计信息
    
    获取设备的统计概览
    """
    try:
        logger.info("获取设备统计信息")
        
        # 获取统计信息
        stats = equipment_manager.get_equipment_statistics()
        
        return {
            "success": True,
            "message": "统计信息获取成功",
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"获取设备统计信息错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备统计信息失败: {str(e)}")


@router.get("/types/list", response_model=Dict[str, Any])
async def get_equipment_types(
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取设备类型列表
    
    返回系统中所有的设备类型
    """
    try:
        logger.info("获取设备类型列表")
        
        # 从设备数据库中提取所有类型
        types = set()
        for equipment in equipment_manager.equipment_db.values():
            equipment_type = equipment.get("type")
            if equipment_type:
                types.add(equipment_type)
        
        types_list = sorted(list(types))
        
        return {
            "success": True,
            "message": "设备类型获取成功",
            "types": types_list,
            "total_types": len(types_list)
        }
        
    except Exception as e:
        logger.error(f"获取设备类型列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备类型列表失败: {str(e)}")


@router.get("/voltage-levels/list", response_model=Dict[str, Any])
async def get_voltage_levels(
    equipment_manager=Depends(get_equipment_manager)
):
    """
    获取电压等级列表

    返回系统中所有的电压等级
    """
    try:
        logger.info("获取电压等级列表")

        # 从设备数据库中提取所有电压等级
        voltage_levels = set()
        for equipment in equipment_manager.equipment_db.values():
            voltage_level = equipment.get("voltage_level")
            if voltage_level:
                voltage_levels.add(voltage_level)

        voltage_levels_list = sorted(list(voltage_levels))

        return {
            "success": True,
            "message": "电压等级获取成功",
            "voltage_levels": voltage_levels_list,
            "total_levels": len(voltage_levels_list)
        }

    except Exception as e:
        logger.error(f"获取电压等级列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取电压等级列表失败: {str(e)}")



