#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask Web应用 - 故障分析智能助手
基于真实数据的完整实现
"""

import os
import sys
import json
import requests
import time
import traceback
import uuid
import re
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入知识库模块
try:
    from retriever.knowledge_base import KnowledgeBase
    KNOWLEDGE_BASE_AVAILABLE = True
except ImportError as e:
    KNOWLEDGE_BASE_AVAILABLE = False

# 配置Flask应用，指定正确的静态文件和模板路径
app = Flask(__name__,
           static_folder='static',
           template_folder='templates')

# 配置文件上传
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB最大文件大小
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(__file__), '..', 'uploads')

CORS(app)



# 阿里云DeepSeek API配置 - 根据用户提供的配置更新
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_API_BASE = "https://dashscope.aliyuncs.com/compatible-mode/v1"
DEEPSEEK_API_ENDPOINT = "/chat/completions"
DEEPSEEK_R1_MODEL = "deepseek-r1"  # 阿里云DeepSeek-R1模型（推理模式）
DEEPSEEK_CHAT_MODEL = "deepseek-v3"  # 阿里云DeepSeek-V3模型（普通对话模式）

# DeepSeek-R1 专业推理配置 - 基于Transformer架构优化
class DeepSeekR1Config:
    """
    DeepSeek-R1 专业配置类
    基于Transformer自注意力机制和统计模式识别的优化参数
    """

    # 核心推理参数 - 基于概率预测优化
    REASONING_TEMPERATURE = 0.1  # 低温度提高推理一致性和准确性
    REASONING_TOP_P = 0.85       # 核采样，平衡创造性和准确性
    REASONING_MAX_TOKENS = 8192  # 支持完整推理链条

    # 专业领域适配参数
    FAULT_ANALYSIS_TEMPERATURE = 0.05  # 故障分析需要极高准确性
    TECHNICAL_REASONING_DEPTH = "high"  # 技术推理深度

    # 上下文窗口优化
    CONTEXT_WINDOW_SIZE = 32768  # 大上下文窗口支持复杂推理
    ATTENTION_PATTERN = "causal"  # 因果注意力模式

    # 推理过程控制
    REASONING_CHAIN_LENGTH = "adaptive"  # 自适应推理链长度
    THINKING_PROCESS_VISIBILITY = True   # 思考过程可见性

    # 专业术语和模式识别增强
    TECHNICAL_VOCABULARY_WEIGHT = 1.2    # 技术词汇权重增强
    DOMAIN_PATTERN_BOOST = 1.5           # 领域模式识别增强

class RealDataManager:
    """真实数据管理器 - 加载和处理项目中的实际数据"""

    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.data_dir = os.path.join(self.project_root, 'data')
        self.knowledge_base_dir = os.path.join(self.project_root, 'knowledge_base')

        # 缓存数据
        self._fault_patterns = None
        self._equipment_database = None
        self._case_studies = None

    def load_fault_patterns(self):
        """加载故障模式数据 - 优先使用白银市真实数据"""
        if self._fault_patterns is None:
            try:
                fault_patterns = {}

                # 1. 优先加载白银市增强故障模式数据
                enhanced_patterns_file = os.path.join(self.data_dir, 'structured', 'enhanced_fault_patterns_baiyin.json')
                if os.path.exists(enhanced_patterns_file):
                    with open(enhanced_patterns_file, 'r', encoding='utf-8') as f:
                        enhanced_data = json.load(f)
                        fault_patterns.update(enhanced_data)
                        print(f"  ✅ 加载了白银市故障模式数据")

                # 2. 加载基础故障模式数据
                basic_patterns_file = os.path.join(self.data_dir, 'structured', 'fault_patterns_001.json')
                if os.path.exists(basic_patterns_file):
                    with open(basic_patterns_file, 'r', encoding='utf-8') as f:
                        basic_data = json.load(f)
                        # 合并数据，白银市数据优先
                        for key, value in basic_data.items():
                            if key not in fault_patterns:
                                fault_patterns[key] = value
                        print(f"  ✅ 加载了基础故障模式数据")

                # 3. 从集成数据库加载故障模式
                integrated_db_path = os.path.join(self.data_dir, 'integrated', 'baiyin_integrated_database.json')
                if os.path.exists(integrated_db_path):
                    with open(integrated_db_path, 'r', encoding='utf-8') as f:
                        integrated_data = json.load(f)

                    # 提取故障模式数据
                    fault_pattern_sources = integrated_data.get('data_sources', {}).get('fault_patterns', [])
                    for source in fault_pattern_sources:
                        pattern_data = source.get('data', {})
                        if pattern_data:
                            fault_patterns.update(pattern_data)
                            print(f"  ✅ 从集成数据库加载故障模式")

                self._fault_patterns = fault_patterns

            except Exception as e:
                print(f"❌ 加载故障模式数据失败: {e}")
                self._fault_patterns = {}
        return self._fault_patterns

    def load_equipment_database(self):
        """加载设备数据库 - 优先使用白银市真实数据"""
        if self._equipment_database is None:
            try:
                equipment_database = {}

                # 1. 优先加载白银市增强设备数据库
                enhanced_equipment_file = os.path.join(self.data_dir, 'structured', 'enhanced_equipment_database_baiyin.json')
                if os.path.exists(enhanced_equipment_file):
                    with open(enhanced_equipment_file, 'r', encoding='utf-8') as f:
                        enhanced_data = json.load(f)
                        equipment_database.update(enhanced_data)
                        print(f"  ✅ 加载了白银市设备数据库")

                # 2. 加载白银市电站数据
                power_stations_file = os.path.join(self.data_dir, 'structured', 'baiyin_power_stations_20250703.json')
                if os.path.exists(power_stations_file):
                    with open(power_stations_file, 'r', encoding='utf-8') as f:
                        stations_data = json.load(f)
                        if 'power_stations' not in equipment_database:
                            equipment_database['power_stations'] = []
                        equipment_database['power_stations'].extend(stations_data.get('power_stations', []))
                        print(f"  ✅ 加载了白银市电站数据")

                # 3. 从集成数据库加载设备数据
                integrated_db_path = os.path.join(self.data_dir, 'integrated', 'baiyin_integrated_database.json')
                if os.path.exists(integrated_db_path):
                    with open(integrated_db_path, 'r', encoding='utf-8') as f:
                        integrated_data = json.load(f)

                    # 提取设备数据
                    equipment_sources = integrated_data.get('data_sources', {}).get('equipment_data', [])
                    for source in equipment_sources:
                        equipment_data = source.get('data', {})
                        if equipment_data:
                            # 检查equipment_data的类型
                            if isinstance(equipment_data, dict):
                                # 合并设备数据，处理不同的数据结构
                                for key, value in equipment_data.items():
                                    if key not in equipment_database:
                                        equipment_database[key] = value
                                    elif isinstance(value, list):
                                        # 如果目标也是列表，则扩展
                                        if isinstance(equipment_database.get(key), list):
                                            equipment_database[key].extend(value)
                                        else:
                                            # 如果目标不是列表，则直接赋值
                                            equipment_database[key] = value
                                    elif isinstance(value, dict):
                                        # 如果是字典，则合并
                                        if isinstance(equipment_database.get(key), dict):
                                            equipment_database[key].update(value)
                                        else:
                                            equipment_database[key] = value
                            elif isinstance(equipment_data, list):
                                # 如果equipment_data是列表，将其作为设备列表处理
                                if 'equipment_list' not in equipment_database:
                                    equipment_database['equipment_list'] = []
                                equipment_database['equipment_list'].extend(equipment_data)
                            print(f"  ✅ 从集成数据库加载设备数据")

                # 4. 回退到基础设备数据库
                basic_equipment_file = os.path.join(self.data_dir, 'structured', 'equipment_database_001.json')
                if os.path.exists(basic_equipment_file) and not equipment_database:
                    with open(basic_equipment_file, 'r', encoding='utf-8') as f:
                        basic_data = json.load(f)
                        equipment_database.update(basic_data)
                        print(f"  ✅ 加载了基础设备数据库")

                self._equipment_database = equipment_database

            except Exception as e:
                print(f"❌ 加载设备数据库失败: {e}")
                self._equipment_database = {}
        return self._equipment_database

    def load_case_studies(self):
        """加载案例研究数据 - 优先使用白银市真实数据"""
        if self._case_studies is None:
            try:
                case_studies = {}

                # 1. 优先加载白银市集成数据库中的案例
                integrated_db_path = os.path.join(self.data_dir, 'integrated', 'baiyin_integrated_database.json')
                if os.path.exists(integrated_db_path):
                    print("📚 加载白银市集成数据库中的案例...")
                    with open(integrated_db_path, 'r', encoding='utf-8') as f:
                        integrated_data = json.load(f)

                    # 提取故障案例数据
                    fault_cases = integrated_data.get('data_sources', {}).get('fault_cases', [])
                    for i, case_source in enumerate(fault_cases):
                        case_content = case_source.get('content', '')
                        if case_content:
                            case_key = f"baiyin_case_{i+1}"
                            case_studies[case_key] = case_content
                            print(f"  ✅ 加载白银案例: {case_key}")

                # 2. 加载知识库中的案例研究文件
                case_dir = os.path.join(self.knowledge_base_dir, 'text', 'case_studies')
                if os.path.exists(case_dir):
                    for filename in os.listdir(case_dir):
                        if filename.endswith('.md'):
                            filepath = os.path.join(case_dir, filename)
                            with open(filepath, 'r', encoding='utf-8') as f:
                                content = f.read()
                                case_key = filename.replace('.md', '')
                                case_studies[case_key] = content
                                print(f"  ✅ 加载案例文件: {filename}")

                self._case_studies = case_studies

            except Exception as e:
                print(f"❌ 加载案例研究数据失败: {e}")
                self._case_studies = {}
        return self._case_studies

    def _extract_title(self, content):
        """从markdown内容中提取标题"""
        lines = content.split('\n')
        for line in lines:
            if line.startswith('# '):
                return line[2:].strip()
        return "未知案例"

    def get_knowledge_base(self):
        """获取知识库数据"""
        knowledge_base = {}

        # 加载图像数据
        images_dir = os.path.join(self.knowledge_base_dir, 'images')
        if os.path.exists(images_dir):
            for filename in os.listdir(images_dir):
                if filename.endswith('.json'):
                    try:
                        filepath = os.path.join(images_dir, filename)
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            knowledge_base[f'images_{filename}'] = data
                    except Exception as e:
                        print(f"加载图像数据失败 {filename}: {e}")

        # 加载文本数据
        text_dir = os.path.join(self.knowledge_base_dir, 'text')
        if os.path.exists(text_dir):
            for root, _, files in os.walk(text_dir):
                for filename in files:
                    if filename.endswith(('.md', '.txt')):
                        try:
                            filepath = os.path.join(root, filename)
                            with open(filepath, 'r', encoding='utf-8') as f:
                                content = f.read()
                                relative_path = os.path.relpath(filepath, text_dir)
                                knowledge_base[f'text_{relative_path}'] = content
                        except Exception as e:
                            print(f"加载文本数据失败 {filename}: {e}")

        return knowledge_base

    def get_case_studies(self):
        """获取案例研究数据"""
        return self.load_case_studies()

    def get_fault_patterns(self):
        """获取故障模式数据"""
        return self.load_fault_patterns()

    def get_equipment_data(self):
        """获取设备数据"""
        return self.load_equipment_database()

    def save_equipment_data(self, equipment_data):
        """保存设备数据到文件"""
        try:

            # 保存到主要的设备数据库文件
            equipment_db_file = os.path.join(self.data_dir, 'structured', 'equipment_database_001.json')

            # 确保目录存在
            os.makedirs(os.path.dirname(equipment_db_file), exist_ok=True)

            # 构建完整的数据库结构
            total_equipment = 0
            try:
                total_equipment = sum(len(category_data) for category_data in equipment_data.values() if isinstance(category_data, list))
            except Exception as count_error:
                total_equipment = 0

            database_structure = {
                "database_info": {
                    "version": "1.0",
                    "last_updated": datetime.now().isoformat(),
                    "total_equipment": total_equipment,
                    "station_name": "白银市电力系统"
                }
            }

            # 添加设备数据
            database_structure.update(equipment_data)

            # 保存到文件
            try:
                # 先尝试JSON序列化，检查是否有序列化问题
                json_str = json.dumps(database_structure, ensure_ascii=False, indent=2)

                # 写入文件
                with open(equipment_db_file, 'w', encoding='utf-8') as f:
                    f.write(json_str)

            except (TypeError, ValueError) as json_error:
                raise json_error
            except IOError as io_error:
                raise io_error

            # 更新缓存
            self._equipment_database = equipment_data

            return True

        except Exception as e:
            traceback.print_exc()
            return False

    def search_relevant_data(self, query, equipment_type=None, fault_type=None):
        """根据查询搜索相关数据"""
        results = {
            'fault_patterns': [],
            'equipment_info': [],
            'case_studies': [],
            'diagnostic_methods': [],
            'repair_actions': []
        }

        # 搜索故障模式
        fault_patterns = self.load_fault_patterns()
        if equipment_type == '变压器' and 'transformer_faults' in fault_patterns:
            for fault in fault_patterns['transformer_faults']:
                if self._is_relevant(query, fault):
                    results['fault_patterns'].append(fault)
        elif equipment_type == '断路器' and 'circuit_breaker_faults' in fault_patterns:
            for fault in fault_patterns['circuit_breaker_faults']:
                if self._is_relevant(query, fault):
                    results['fault_patterns'].append(fault)

        # 搜索设备信息
        equipment_db = self.load_equipment_database()
        if equipment_type == '变压器' and 'transformers' in equipment_db:
            results['equipment_info'] = equipment_db['transformers']
        elif equipment_type == '断路器' and 'circuit_breakers' in equipment_db:
            results['equipment_info'] = equipment_db['circuit_breakers']

        # 搜索案例研究
        case_studies = self.load_case_studies()
        for case in case_studies:
            # 安全处理不同类型的案例数据
            if isinstance(case, dict):
                case_content = case.get('content', str(case))
            elif isinstance(case, str):
                case_content = case
            else:
                case_content = str(case)

            if self._is_case_relevant(query, case_content, equipment_type):
                results['case_studies'].append(case)

        return results

    def _is_relevant(self, query, fault_data):
        """判断故障数据是否与查询相关"""
        query_lower = query.lower()

        # 检查故障名称
        if fault_data.get('fault_name', '').lower() in query_lower:
            return True

        # 检查症状
        symptoms = fault_data.get('symptoms', [])
        for symptom in symptoms:
            if any(keyword in query_lower for keyword in symptom.lower().split()):
                return True

        # 检查可能原因
        causes = fault_data.get('possible_causes', [])
        for cause in causes:
            if any(keyword in query_lower for keyword in cause.lower().split()):
                return True

        return False

    def _is_case_relevant(self, query, case_content, equipment_type):
        """判断案例是否与查询相关"""
        query_lower = query.lower()
        content_lower = case_content.lower()

        # 检查设备类型匹配
        if equipment_type and equipment_type in content_lower:
            # 检查关键词匹配
            keywords = ['故障', '跳闸', '保护', '异常', '损坏', '短路', '过热', '放电']
            for keyword in keywords:
                if keyword in query_lower and keyword in content_lower:
                    return True

        return False

# 创建全局数据管理器实例
real_data_manager = RealDataManager()

class DeepSeekR1ReasoningEngine:
    """
    DeepSeek-R1 专业推理引擎
    基于Transformer架构和统计模式识别的专业故障诊断系统
    """

    def __init__(self, config: DeepSeekR1Config):
        self.config = config
        self.reasoning_history = []
        self.pattern_cache = {}

    def analyze_input_tokens(self, query: str) -> Dict[str, Any]:
        """
        输入编码分析 - 模拟Transformer的token化过程
        """
        # 模拟token分析（实际由模型内部处理）
        technical_terms = self._extract_technical_terms(query)
        semantic_patterns = self._identify_semantic_patterns(query)
        context_dependencies = self._analyze_context_dependencies(query)

        return {
            "technical_terms": technical_terms,
            "semantic_patterns": semantic_patterns,
            "context_dependencies": context_dependencies,
            "complexity_score": len(technical_terms) * 0.3 + len(semantic_patterns) * 0.5
        }

    def _extract_technical_terms(self, text: str) -> List[str]:
        """提取电力系统技术术语"""
        technical_keywords = [
            "变压器", "差动保护", "套管", "渗油", "色谱分析", "总烃", "ppm",
            "绝缘", "局部放电", "油温", "保护动作", "跳闸", "故障", "电弧",
            "绕组", "铁芯", "分接开关", "瓦斯保护", "过流保护", "接地",
            "短路", "过载", "谐波", "电压", "电流", "功率", "频率"
        ]

        found_terms = []
        for term in technical_keywords:
            if term in text:
                found_terms.append(term)
        return found_terms

    def _identify_semantic_patterns(self, text: str) -> List[str]:
        """识别语义模式"""
        patterns = []

        # 故障现象模式
        if any(word in text for word in ["动作", "跳闸", "报警"]):
            patterns.append("fault_symptom")

        # 参数异常模式
        if any(word in text for word in ["温度", "压力", "电流", "电压"]):
            patterns.append("parameter_anomaly")

        # 设备状态模式
        if any(word in text for word in ["渗油", "放电", "振动", "噪音"]):
            patterns.append("equipment_condition")

        return patterns

    def _analyze_context_dependencies(self, text: str) -> Dict[str, float]:
        """分析上下文依赖关系"""
        dependencies = {}

        # 因果关系强度
        if "导致" in text or "引起" in text:
            dependencies["causal_strength"] = 0.8
        elif "可能" in text or "疑似" in text:
            dependencies["causal_strength"] = 0.4
        else:
            dependencies["causal_strength"] = 0.2

        # 时序关系
        if any(word in text for word in ["首先", "然后", "接着", "最后"]):
            dependencies["temporal_strength"] = 0.7
        else:
            dependencies["temporal_strength"] = 0.3

        return dependencies

class DeepSeekClient:
    """阿里云DeepSeek客户端 - 优化版本"""

    def __init__(self, api_key: str, base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "User-Agent": "PowerSystem-FaultAnalysis/1.0"
        }

        # 初始化专业推理引擎
        self.reasoning_engine = DeepSeekR1ReasoningEngine(DeepSeekR1Config())

        # 阿里云DashScope专用配置
        if "dashscope.aliyuncs.com" in base_url:
            os.environ['DASHSCOPE_API_KEY'] = api_key


    def chat_completion(self, messages: list, model: str = "deepseek-v3",
                       temperature: float = 0.7, max_tokens: int = 2000, max_retries: int = 2,
                       stream: bool = False):
        """阿里云DashScope API调用，支持流式和非流式响应"""
        url = f"{self.base_url}/chat/completions"

        # 阿里云DashScope API payload格式
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream  # 支持流式响应
        }

        # DeepSeek R1推理模式需要更长的超时时间
        timeout = 180 if model == "deepseek-r1" else 90  # 进一步增加超时时间

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    time.sleep(3)

                if stream:
                    # 流式响应处理
                    response = requests.post(url, headers=self.headers, json=payload,
                                           timeout=timeout, stream=True)
                    if response.status_code == 200:
                        return self._handle_stream_response(response, model)
                    else:
                        print(f"流式响应错误: {response.status_code} - {response.text}")
                        return None
                else:
                    # 非流式响应处理
                    response = requests.post(url, headers=self.headers, json=payload, timeout=timeout)

                    if response.status_code == 200:
                        result = response.json()

                        # 详细的响应调试信息
                        if 'choices' in result and len(result['choices']) > 0:
                            content = result['choices'][0]['message']['content']
                            print(f"✅ API调用成功，模型: {model}")
                            print(f"📝 响应内容长度: {len(content)} 字符")
                            print(f"📝 响应内容前300字符: {content[:300]}")
                            print(f"📝 响应内容后300字符: {content[-300:]}")

                            # 检查是否包含推理过程标记
                            thinking_indicators = ['<think>', '<thinking>', '让我分析', '我需要分析', '思考过程']
                            has_thinking = any(indicator in content for indicator in thinking_indicators)
                            print(f"🧠 是否包含推理标记: {has_thinking}")

                            return result
                        else:
                            print(f"❌ 响应格式异常: {result}")
                            if attempt < max_retries:
                                time.sleep(3)
                                continue
                            return None

                    else:
                        print(f"响应内容: {response.text}")

                        # 特殊处理阿里云API错误码
                        if response.status_code == 429:
                            if attempt < max_retries:
                                time.sleep(10)
                                continue
                        elif response.status_code in [401, 403]:
                            return None
                        else:
                            if attempt < max_retries:
                                time.sleep(5)
                                continue
                        return None

            except requests.exceptions.Timeout:
                print(f"⏰ API调用超时 (>{timeout}秒)")
                if attempt < max_retries:
                    time.sleep(5)
                    continue
                else:
                    return None

            except Exception:
                if attempt < max_retries:
                    time.sleep(3)
                    continue
                return None

        return None

    def _handle_stream_response(self, response, model):
        """处理流式响应"""
        try:
            import json
            full_content = ""

            print(f"🌊 开始处理流式响应，模型: {model}")

            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀

                        if data_str.strip() == '[DONE]':
                            break

                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    content_chunk = delta['content']
                                    full_content += content_chunk
                                    print(f"📝 流式内容块: {content_chunk[:50]}...")
                        except json.JSONDecodeError:
                            continue

            print(f"✅ 流式响应完成，总长度: {len(full_content)} 字符")

            # 构造标准响应格式
            return {
                'choices': [{
                    'message': {
                        'content': full_content,
                        'role': 'assistant'
                    },
                    'finish_reason': 'stop'
                }],
                'model': model,
                'usage': {'total_tokens': len(full_content) // 4}  # 估算token数
            }

        except Exception as e:
            print(f"❌ 流式响应处理失败: {e}")
            return None

    def test_connection(self):
        """测试阿里云DashScope API连接"""
        try:
            print("🔍 测试阿里云DashScope API连接...")
            test_messages = [
                {"role": "user", "content": "你好，请简单回复测试"}
            ]

            result = self.chat_completion(
                messages=test_messages,
                model=DEEPSEEK_CHAT_MODEL,
                max_tokens=50,
                max_retries=1
            )

            if result:
                return True
            else:
                return False

        except Exception:
            return False

    def analyze_fault(self, fault_description: str, thinking_mode: bool = False):
        """电力系统故障分析 - 针对DeepSeek R1优化"""

        if thinking_mode:
            # DeepSeek R1推理模式的专业提示词 - 标准thinking格式
            system_prompt = """你是白银市电力系统故障诊断专家，具有丰富的电力设备故障分析经验。

重要输出格式要求：
请严格按照以下格式输出，确保思考过程和最终分析清晰分离：

<thinking>
[请在此处展示您作为电力系统专家的完整思考过程，包括：
- 对故障现象的初步判断和分析思路
- 技术参数的评估和异常点识别
- 可能原因的逐一排查和权重评估
- 证据链条的逻辑推理过程
- 专业经验的应用和类比分析]
</thinking>

**基于上述思考的专业故障诊断报告：**

[在thinking标签之外，提供结构化的专业诊断报告，包括故障性质判断、技术原因分析、处理建议和预防措施]

请深入思考并进行专业的故障诊断分析。"""
        else:
            # 普通模式的简洁提示词
            system_prompt = "你是电力系统故障诊断专家，请分析故障原因并提供处理建议。"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"故障描述：{fault_description}\n\n请进行专业的故障分析。"}
        ]

        model = DEEPSEEK_R1_MODEL if thinking_mode else DEEPSEEK_CHAT_MODEL
        print(f"🤖 使用模型: {model}，推理模式: {thinking_mode}")

        return self.chat_completion(
            messages=messages,
            model=model,
            temperature=0.3 if thinking_mode else 0.7,  # 推理模式使用更低的温度
            max_tokens=3000 if thinking_mode else 2000,
            stream=thinking_mode  # 只有DeepSeek-R1使用流式响应
        )

    def analyze_fault_with_context(self, fault_description: str, context_data: list,
                                 enhanced_prompt: str, thinking_mode: bool = False):
        """带真实数据上下文的故障分析"""

        messages = [
            {"role": "system", "content": "你是白银市电力系统故障诊断专家。"},
            {"role": "user", "content": enhanced_prompt}
        ]

        model = DEEPSEEK_R1_MODEL if thinking_mode else DEEPSEEK_CHAT_MODEL
        print(f"🤖 使用模型: {model}，推理模式: {thinking_mode}，上下文数据: {len(context_data)}条")

        return self.chat_completion(
            messages=messages,
            model=model,
            temperature=0.2 if thinking_mode else 0.6,  # 推理模式使用更低的温度
            max_tokens=4000 if thinking_mode else 2500  # 增加token限制以容纳更多上下文
        )







# 创建DeepSeek客户端实例
deepseek_client = DeepSeekClient(DEEPSEEK_API_KEY, DEEPSEEK_API_BASE)

# 知识库实例初始化
knowledge_base_instance = None
knowledge_base_initializing = False

# 性能优化：数据缓存
from functools import wraps
import threading

# 性能监控装饰器
def monitor_performance(func):
    """监控函数执行性能"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # 超过1秒的慢查询
                print(f"⚠️ 慢查询警告: {func.__name__} 耗时 {execution_time:.2f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ 函数执行失败: {func.__name__} 耗时 {execution_time:.2f}s, 错误: {e}")
            raise
    return wrapper

# 缓存锁
cache_lock = threading.Lock()

# 数据缓存
data_cache = {
    'equipment_data': None,
    'case_studies': None,
    'fault_patterns': None,
    'knowledge_base': None,
    'last_update': None
}

# 数据管理器和路由定义
data_manager = None
real_data_manager = None

# 缓存管理函数
def get_cached_data(data_type):
    """获取缓存的数据，如果缓存过期则重新加载"""
    global data_cache

    with cache_lock:
        # 检查缓存是否需要更新（5分钟过期）
        current_time = time.time()
        if (data_cache['last_update'] is None or
            current_time - data_cache['last_update'] > 300):  # 5分钟

            # 重新加载所有数据
            if data_manager:
                try:
                    data_cache['equipment_data'] = data_manager.get_equipment_data()
                    data_cache['case_studies'] = data_manager.get_case_studies()
                    data_cache['fault_patterns'] = data_manager.get_fault_patterns()
                    data_cache['knowledge_base'] = data_manager.get_knowledge_base()
                    data_cache['last_update'] = current_time
                    print(f"🔄 数据缓存已更新")
                except Exception as e:
                    print(f"❌ 缓存更新失败: {e}")

        return data_cache.get(data_type, {})

# 初始化数据管理器
try:
    # 使用现有的RealDataManager类
    real_data_manager = RealDataManager()
    data_manager = real_data_manager  # 使用同一个实例
except Exception as e:
    print(f"❌ 数据管理器初始化失败: {e}")

# 路由定义
@app.route('/')
def index():
    """主页"""
    try:
        return render_template('index.html')
    except Exception as e:
        import traceback
        traceback.print_exc()
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>错误页面</title>
        </head>
        <body>
            <h1>页面加载错误</h1>
            <p>错误信息: {str(e)}</p>
            <p>请检查模板文件是否存在。</p>
        </body>
        </html>
        """

# 故障分析功能已集成到主页面，不再需要独立路由

@app.route('/equipment-management')
def equipment_management():
    """设备管理页面"""
    return render_template('equipment_management.html')

@app.route('/knowledge-base')
def knowledge_base():
    """知识库页面"""
    return render_template('knowledge_base.html')

@app.route('/test_equipment_api.html')
def test_equipment_api():
    """设备管理API测试页面"""
    return send_from_directory('..', 'test_equipment_api.html')

@app.route('/debug_api_test.html')
def debug_api_test():
    """API调试测试页面"""
    return render_template('debug_api_test.html')

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "python_version": sys.version,
        "data_manager_status": "loaded" if data_manager else "not_loaded",
        "deepseek_api_configured": bool(DEEPSEEK_API_KEY),
        "deepseek_api_base": DEEPSEEK_API_BASE,
        "deepseek_r1_model": DEEPSEEK_R1_MODEL,
        "deepseek_chat_model": DEEPSEEK_CHAT_MODEL
    })

@app.route('/api/v1/status', methods=['GET'])
def get_status():
    """获取系统状态"""
    try:
        # 获取数据统计（使用缓存）
        knowledge_base = get_cached_data('knowledge_base')
        case_studies = get_cached_data('case_studies')
        fault_patterns = get_cached_data('fault_patterns')
        equipment_data = get_cached_data('equipment_data')

        return jsonify({
            "status": "running",
            "timestamp": datetime.now().isoformat(),
            "data_statistics": {
                "knowledge_base_items": len(knowledge_base),
                "case_studies": len(case_studies),
                "fault_patterns": len(fault_patterns),
                "equipment_records": len(equipment_data)
            },
            "api_status": {
                "deepseek_configured": bool(DEEPSEEK_API_KEY),
                "deepseek_api_base": DEEPSEEK_API_BASE,
                "deepseek_r1_model": DEEPSEEK_R1_MODEL
            }
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/debug/data')
def debug_data():
    """调试数据加载"""
    return jsonify({
        "data_manager_status": "loaded" if data_manager else "not_loaded",
        "data_sets_count": len(data_manager.data_cache) if data_manager else 0,
        "equipment_count": len(data_manager.get_equipment_data()) if data_manager else 0,
        "knowledge_count": len(data_manager.get_knowledge_base()) if data_manager else 0
    })

@app.route('/api/v1/test-deepseek', methods=['POST'])
def test_deepseek_connection():
    """测试阿里云DeepSeek API连接"""
    try:
        if not deepseek_client:
            return jsonify({
                "success": False,
                "error": "DeepSeek客户端未初始化"
            }), 500

        # 测试API连接
        print("🔍 开始测试阿里云DeepSeek API连接...")
        connection_ok = deepseek_client.test_connection()

        return jsonify({
            "success": connection_ok,
            "message": "阿里云DeepSeek API连接测试成功" if connection_ok else "阿里云DeepSeek API连接测试失败",
            "api_base": DEEPSEEK_API_BASE,
            "r1_model": DEEPSEEK_R1_MODEL,
            "chat_model": DEEPSEEK_CHAT_MODEL,
            "api_key_masked": f"{DEEPSEEK_API_KEY[:10]}..." if DEEPSEEK_API_KEY else None,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"API测试异常: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }), 500



def generate_report(query: str, thinking_mode: bool = False):
    """
    基于真实数据的故障分析报告生成 - 优化版本
    """
    try:
        print(f"🤖 开始分析故障: {query}")

        # 1. 首先从真实数据中检索相关信息
        real_data_context = extract_real_data_context(query)

        # 2. 构建增强的提示词（包含真实数据）
        enhanced_prompt = build_enhanced_prompt_with_real_data(query, real_data_context, thinking_mode)

        # 3. 调用DeepSeek API
        deepseek_response = deepseek_client.analyze_fault_with_context(
            fault_description=query,
            context_data=real_data_context,
            enhanced_prompt=enhanced_prompt,
            thinking_mode=thinking_mode
        )

        if deepseek_response and deepseek_response.get('choices'):
            ai_content = deepseek_response['choices'][0]['message']['content']
            print(f"📝 AI响应内容长度: {len(ai_content)} 字符")

            # 4. 处理DeepSeek R1的推理过程和最终结果分离
            reasoning_process = None
            final_analysis = ai_content

            if thinking_mode:
                # 检查是否有DeepSeek-R1的标准格式
                if 'reasoning' in deepseek_response.get('choices', [{}])[0]:
                    # 标准DeepSeek-R1格式
                    choice = deepseek_response['choices'][0]
                    reasoning_process = choice.get('reasoning', '')
                    final_analysis = choice.get('message', {}).get('content', ai_content)
                    print(f"🧠 标准R1格式 - 推理: {len(reasoning_process)} 字符, 结果: {len(final_analysis)} 字符")
                else:
                    # 智能分离推理过程和最终结果
                    reasoning_process, final_analysis = smart_split_reasoning_and_result(ai_content)
                    print(f"🧠 智能分离 - 推理: {len(reasoning_process)} 字符, 结果: {len(final_analysis)} 字符")

            # 5. 构建基于真实数据的分析结果
            if thinking_mode:
                # DeepSeek-R1模式：结合推理过程和最终结果生成连贯的自然语言输出
                combined_analysis = combine_reasoning_and_result(reasoning_process, final_analysis)
                cleaned_final = clean_final_analysis_for_pure_text(final_analysis)
                cleaned_reasoning = clean_reasoning_for_pure_text(reasoning_process) if reasoning_process else None
            else:
                # DeepSeek-V3模式：直接使用最终结果
                combined_analysis = clean_report_format(final_analysis, thinking_mode)
                cleaned_final = final_analysis
                cleaned_reasoning = None

            analysis_result = {
                "preliminary_diagnosis": f"基于白银市真实数据的故障诊断",
                "equipment_type": identify_equipment_type_from_real_data(query, real_data_context),
                "fault_type": query,
                "detailed_analysis": combined_analysis,  # 结合推理和结果的完整分析
                "final_analysis": cleaned_final,  # 清理后的最终分析
                "analysis": cleaned_final,  # 保持兼容性
                "reasoning_process": cleaned_reasoning,  # 清理后的推理过程
                "real_data_used": len(real_data_context),
                "data_sources": [item.get('source', 'unknown') for item in real_data_context[:5]],
                "ai_model": DEEPSEEK_R1_MODEL if thinking_mode else DEEPSEEK_CHAT_MODEL,
                "thinking_mode": thinking_mode,
                "output_format": "separated" if thinking_mode else "unstructured",  # 推理模式下分离显示
                "has_reasoning": bool(reasoning_process and thinking_mode),  # 标记是否包含推理过程
                "reasoning_length": len(reasoning_process) if reasoning_process else 0,
                "final_length": len(final_analysis)
            }

            print(f"✅ 分析结果构建完成，推理模式: {thinking_mode}, 包含推理过程: {analysis_result['has_reasoning']}")
            return analysis_result
        else:
            return get_real_data_fallback_analysis(query, real_data_context)

    except Exception as e:
        print(f"故障分析出错: {e}")
        # 即使出错也尝试使用真实数据
        real_data_context = extract_real_data_context(query)
        return get_real_data_fallback_analysis(query, real_data_context)

def extract_real_data_context(query: str):
    """增强的RAG检索 - 基于语义相似度和多策略融合"""
    try:
        print(f"🔍 开始增强RAG检索: {query}")

        # 初始化增强检索器
        enhanced_retriever = EnhancedRAGRetriever()

        # 多策略检索
        context_data = enhanced_retriever.retrieve_context(query)

        print(f"🔍 RAG检索完成，获得 {len(context_data)} 个相关文档")
        return context_data

    except Exception as e:
        print(f"增强RAG检索失败: {e}")
        # 降级到原始检索方法
        return _fallback_extract_context(query)

def calculate_relevance(query: str, content: str) -> float:
    """计算查询与内容的相关性"""
    try:
        query_lower = query.lower()
        content_lower = content.lower()

        # 关键词匹配得分
        keywords = ['变压器', '断路器', '故障', '保护', '差动', '电缆', '隔离开关', '白银', '电站']
        keyword_score = 0
        for keyword in keywords:
            if keyword in query_lower and keyword in content_lower:
                keyword_score += 0.2

        # 直接文本匹配得分
        query_words = query_lower.split()
        content_words = content_lower.split()
        common_words = set(query_words) & set(content_words)
        text_score = len(common_words) / max(len(query_words), 1) * 0.5

        return min(keyword_score + text_score, 1.0)

    except Exception:
        return 0.0

def build_enhanced_prompt_with_real_data(query: str, real_data_context: list, thinking_mode: bool) -> str:
    """构建基于RAG检索结果的增强提示词"""
    try:
        print(f"🔧 构建增强提示词，检索到 {len(real_data_context)} 个相关文档")

        # 分析检索结果，构建上下文
        context_analysis = _analyze_retrieval_results(real_data_context, query)

        # 根据上下文动态构建提示词
        enhanced_prompt = _build_dynamic_prompt(query, context_analysis, thinking_mode)

        return enhanced_prompt

    except Exception as e:
        print(f"构建增强提示词失败: {e}")
        return _build_fallback_prompt(query, thinking_mode)

def _analyze_retrieval_results(results: list, query: str) -> Dict[str, Any]:
    """分析检索结果，提取关键信息"""
    try:
        analysis = {
            'equipment_types': set(),
            'fault_types': set(),
            'technical_params': [],
            'relevant_cases': [],
            'key_knowledge': [],
            'confidence_level': 0.0
        }

        total_relevance = 0
        for result in results:
            # 安全处理不同类型的结果数据
            if isinstance(result, dict):
                relevance = result.get('relevance', 0.5)  # 默认中等相关性
                content = result.get('content', str(result))
            elif isinstance(result, str):
                relevance = 0.5  # 字符串数据默认中等相关性
                content = result
            else:
                relevance = 0.3  # 其他类型数据默认低相关性
                content = str(result)

            total_relevance += relevance

            # 提取设备类型
            for equipment in ['变压器', '断路器', '隔离开关', '电缆', '母线']:
                if equipment in content:
                    analysis['equipment_types'].add(equipment)

            # 提取故障类型
            for fault in ['差动保护', '距离保护', '过流保护', '接地故障', '短路', '过载']:
                if fault in content:
                    analysis['fault_types'].add(fault)

            # 提取技术参数 (增强版)
            import re
            # 电压等级
            voltage_matches = re.findall(r'\d+[kK][Vv]', content)
            analysis['technical_params'].extend(voltage_matches)

            # 电流值
            current_matches = re.findall(r'\d+[Aa]', content)
            analysis['technical_params'].extend(current_matches)

            # 阻抗值
            impedance_matches = re.findall(r'\d+[MmΩΩ]', content)
            analysis['technical_params'].extend(impedance_matches)

            # 温度值
            temp_matches = re.findall(r'\d+℃', content)
            analysis['technical_params'].extend(temp_matches)

            # 压力值
            pressure_matches = re.findall(r'\d+[Mm]?[Pp][Aa]', content)
            analysis['technical_params'].extend(pressure_matches)

            # 浓度值
            concentration_matches = re.findall(r'\d+ppm|μL/L|mg/L', content)
            analysis['technical_params'].extend(concentration_matches)

            # 百分比值
            percentage_matches = re.findall(r'\d+%', content)
            analysis['technical_params'].extend(percentage_matches)

            # 分类存储相关内容
            if result.get('type') == 'fault_pattern':
                analysis['relevant_cases'].append(content[:300])
            elif result.get('type') == 'knowledge_base':
                analysis['key_knowledge'].append(content[:400])

        # 计算置信度
        if results:
            analysis['confidence_level'] = total_relevance / len(results)

        return analysis

    except Exception as e:
        print(f"分析检索结果失败: {e}")
        return {'equipment_types': set(), 'fault_types': set(), 'technical_params': [],
                'relevant_cases': [], 'key_knowledge': [], 'confidence_level': 0.0}

def _build_dynamic_prompt(query: str, analysis: Dict, thinking_mode: bool) -> str:
    """基于分析结果动态构建专业提示词"""
    try:
        if thinking_mode:
            # DeepSeek R1推理模式 - 专业自然语言推理
            base_prompt = """你是白银市电力系统资深故障诊断专家，拥有20年以上的变电站运维和故障分析经验。请使用DeepSeek-R1深度推理模式，展现专家级的思维过程。

**专业身份定位：**
- 国家电网白银供电公司高级工程师
- 电力系统故障分析技术专家
- 具备丰富的110kV/220kV变电站运维经验
- 熟悉西北地区电力设备运行特点

**推理要求：**
请以专家内心思考的方式进行深度分析，展现完整的专业推理过程。输出应该是连贯的自然语言，就像一位经验丰富的专家在分析故障时的思维流程。

**推理风格参考：**
"从保护装置的动作情况来看，这次故障的性质需要仔细分析...差动保护首先动作，说明变压器内部确实存在不平衡电流，这通常意味着绕组或铁芯出现了问题。结合现场观察到的套管渗油现象，我倾向于判断这是一个复合型故障。油温监测显示68℃，这个温度明显超出了正常运行范围的52℃，表明内部存在异常发热源。更关键的是，色谱分析结果显示总烃含量达到2500ppm，这个数值比正常标准的150ppm高出了近17倍，这强烈暗示着变压器内部存在严重的放电或过热问题。从技术角度分析，这种程度的总烃超标通常与绕组匝间短路或铁芯多点接地有关..."

**技术分析要求：**
- 运用电力系统理论和实践经验
- 结合具体的技术参数和数据
- 体现专业的故障诊断思维
- 使用准确的电力专业术语
- 展现逻辑清晰的推理过程

请基于以下信息进行专业的故障诊断推理：
"""
        else:
            # DeepSeek-V3模式 - 专业自然语言分析
            base_prompt = """你是白银市电力系统故障诊断专家，请提供专业的自然语言分析。

**重要要求：绝对不要使用"1. 2. 3. 4."等数字编号格式，必须用连贯的自然语言表达**

**专业身份：**
- 电力系统高级工程师，专业从事变电站设备故障诊断
- 熟悉110kV/220kV电力设备运行特性，具备丰富的现场故障处理经验

**输出风格示例：**
"根据故障现象分析，这是一起典型的变压器内部故障。从差动保护动作的情况来看，变压器内部确实存在不平衡电流，这通常表明绕组或铁芯出现了问题。结合现场观察到的套管渗油现象，可以判断这是一个复合型故障。

油温监测数据显示68℃，明显超出正常运行范围的52℃，说明内部存在异常发热源。更关键的是，色谱分析结果显示总烃含量达到2500ppm，比正常标准150ppm高出近17倍，这强烈暗示变压器内部存在严重的放电或过热问题。

针对这种情况，建议立即停运该变压器，进行全面的绝缘电阻测试和介损测量。同时需要对套管进行详细检查，必要时更换密封件。为防止类似故障再次发生，应加强日常巡检，特别是油温和色谱分析的监测频次。"

**分析要求：**
- 使用连贯的自然语言，就像电力工程师在现场分析时的表达方式
- 包含具体的技术参数和数值对比（如电压等级、电流值、阻抗值、温度等）
- 使用准确的电力专业术语（差动保护、零序保护、绝缘电阻、介损、色谱分析等）
- 体现专业的分析逻辑和技术判断
- 提供具体可行的处理建议和预防措施
- 绝对禁止使用任何编号、列表、条目格式

技术参考数据：
"""

        # 添加基于分析结果的上下文
        if analysis['equipment_types']:
            base_prompt += f"\n\n涉及设备类型：{', '.join(analysis['equipment_types'])}"

        if analysis['fault_types']:
            base_prompt += f"\n\n相关故障类型：{', '.join(analysis['fault_types'])}"

        if analysis['relevant_cases']:
            base_prompt += "\n\n相关故障模式参考：\n"
            for case in analysis['relevant_cases'][:3]:  # 增加到3个
                base_prompt += f"- {case}\n"

        if analysis['key_knowledge']:
            base_prompt += "\n\n技术知识参考：\n"
            for knowledge in analysis['key_knowledge'][:3]:  # 增加到3个
                base_prompt += f"- {knowledge}\n"

        if analysis['technical_params']:
            base_prompt += f"\n\n技术参数：{', '.join(set(analysis['technical_params']))}"

        # 添加当前故障信息
        base_prompt += f"""

**当前故障情况：**
{query}

**技术分析要求：**
在分析过程中，请确保包含具体的技术参数和数值（如电压等级110kV/220kV、电流值15A、阻抗值800MΩ、温度65℃、色谱分析数值2000ppm等），使用准确的电力专业术语（差动保护、零序保护、距离保护、绝缘电阻、介损、色谱分析、局放检测等），并提供详细的标准值对比分析。

**分析置信度：** {analysis.get('confidence_level', 0.8):.1%}"""

        if thinking_mode:
            base_prompt += f"""

请运用您作为电力系统专家的丰富经验和专业知识，展开深度的推理分析。请以自然流畅的语言表达您的思考过程，就像您在现场分析故障时的内心思维一样。分析应该体现专业的技术判断、逻辑推理和实践经验。"""
        else:
            base_prompt += f"""

请基于以上信息，用连贯的自然语言进行专业分析。分析应该像一位经验丰富的电力工程师在现场分析故障时的表达方式，包含故障性质判断、技术原因分析、处理方案建议和预防措施。请用流畅的段落形式表达，避免使用任何编号或列表格式。"""

        return base_prompt

    except Exception as e:
        print(f"构建增强提示词失败: {e}")
        return f"故障描述：{query}\n请进行专业的故障分析。"

def _build_fallback_prompt(query: str, thinking_mode: bool) -> str:
    """构建专业降级提示词"""
    if thinking_mode:
        return f"""你是白银市电力系统资深故障诊断专家，拥有丰富的变电站运维和故障分析经验。

**专业身份：**
- 国家电网白银供电公司高级工程师
- 电力系统故障分析技术专家
- 具备20年以上110kV/220kV变电站运维经验

**推理要求：**
请以专家内心思考的方式进行深度分析，展现完整的专业推理过程。输出应该是连贯的自然语言，体现一位经验丰富的电力专家在分析故障时的思维流程。

**分析风格参考：**
"从这次故障的表现来看，我需要仔细分析各个技术环节...保护装置的动作时序显示差动保护首先启动，这通常表明变压器内部存在不平衡电流，结合现场观察到的异常现象，我倾向于判断这是一个需要深入分析的复合型故障。从技术参数来看，相关数值的变化趋势暗示着设备内部可能存在某种渐进性的劣化过程..."

**故障情况：**
{query}

请运用您的专业知识和丰富经验，展开深度的故障诊断推理分析。"""
    else:
        return f"""你是白银市电力系统故障诊断专家，请提供专业的自然语言分析。

**重要要求：绝对不要使用"1. 2. 3. 4."等数字编号格式，必须用连贯的自然语言表达**

**专业身份：**
电力系统高级工程师，专业从事变电站设备故障诊断，熟悉110kV/220kV电力设备运行特性。

**故障情况：**
{query}

**输出风格示例：**
"根据故障现象分析，这起故障的性质需要从多个技术角度来判断。从保护装置的动作情况来看，相关参数的变化表明设备内部确实存在异常。结合现场的实际情况，可以初步判断这是一个需要重点关注的技术问题。

针对当前的故障状况，建议采取相应的技术措施进行处理。同时，为了避免类似问题的再次发生，需要在日常运维中加强相关方面的监测和维护。"

**分析要求：**
请用连贯的自然语言进行专业分析，就像电力工程师在现场分析时的表达方式。包含故障性质判断、技术原因分析、处理建议和预防措施，使用准确的技术术语，绝对不要使用编号或列表格式。"""

class EnhancedRAGRetriever:
    """增强的RAG检索器 - 基于语义相似度和多策略融合"""

    def __init__(self):
        self.power_terms = {
            # 设备类型
            '变压器', '主变', '配变', '断路器', '隔离开关', '刀闸',
            '电流互感器', 'CT', '电压互感器', 'PT', '避雷器', '电容器',
            '电抗器', '母线', '导线', '电缆', 'GIS', 'SF6',

            # 故障类型
            '故障', '跳闸', '合闸', '拒动', '误动', '渗油', '放电',
            '短路', '接地', '过载', '过压', '欠压', '缺相', '不平衡',

            # 保护类型
            '保护', '动作', '差动保护', '距离保护', '零序保护', '负序保护',
            '过流保护', '速断保护', '重合闸', '备自投', '失灵保护',

            # 技术参数和单位
            'kV', 'MV', 'kW', 'MW', 'MVA', 'Hz', 'A', 'mA', 'V', 'mV', 'Ω', 'MΩ', 'kΩ',
            '℃', 'MPa', 'kPa', 'ppm', 'μL/L', 'mg/L', '%', 'mm', 'cm', 'm',

            # 专业测试和分析
            '绝缘电阻', '介损', '色谱', '局放', '红外', '超声', '振动',
            '油化', '气相', '液相', 'DGA', 'PD', 'UHF', 'TEV',

            # 运维操作
            '运行', '检修', '维护', '巡检', '试验', '测试', '更换',
            '绝缘', '清扫', '紧固', '调试', '校验', '标定'
        }

        # 初始化TF-IDF向量化器（专门针对电力领域优化）
        self.vectorizer = TfidfVectorizer(
            max_features=2000,
            ngram_range=(1, 3),  # 支持1-3元组
            stop_words=None,  # 不使用通用停用词
            token_pattern=r'(?u)\b\w+\b|[0-9]+[kKmM]?[VvWwAaΩΩ]?',  # 支持电力单位
            min_df=1,
            max_df=0.95
        )

        # 缓存向量化结果
        self._vectorized_cache = {}
        self._documents_cache = []

    def retrieve_context(self, query: str, top_k: int = 12) -> List[Dict[str, Any]]:
        """多策略检索上下文"""
        try:
            print(f"🔍 执行多策略检索: {query}")

            # 1. 预处理查询
            processed_query = self._preprocess_query(query)

            # 2. 收集所有可用数据
            all_documents = self._collect_all_documents()

            if not all_documents:
                print("⚠️ 没有找到可用文档")
                return []

            # 3. 多策略检索
            results = []

            # 策略1: 语义相似度检索
            semantic_results = self._semantic_retrieval(processed_query, all_documents, top_k)
            results.extend(semantic_results)

            # 策略2: 关键词精确匹配
            keyword_results = self._keyword_retrieval(query, all_documents, top_k//2)
            results.extend(keyword_results)

            # 策略3: 电力专业术语匹配
            technical_results = self._technical_term_retrieval(query, all_documents, top_k//2)
            results.extend(technical_results)

            # 策略4: 故障类型专门检索
            fault_specific_results = self._fault_specific_retrieval(query, all_documents, top_k//3)
            results.extend(fault_specific_results)

            # 4. 结果融合和重排序
            final_results = self._fuse_and_rerank(results, query, top_k)

            print(f"🔍 检索完成，返回 {len(final_results)} 个结果")
            return final_results

        except Exception as e:
            print(f"多策略检索失败: {e}")
            return []

    def _preprocess_query(self, query: str) -> str:
        """查询预处理"""
        # 标准化电力术语
        query = self._normalize_power_terms(query)

        # 提取关键信息
        query = self._extract_key_info(query)

        return query.strip()

    def _normalize_power_terms(self, text: str) -> str:
        """标准化电力术语"""
        # 电压等级标准化
        text = re.sub(r'(\d+)\s*[kK][vV]', r'\1kV', text)
        text = re.sub(r'(\d+)\s*[mM][vV]', r'\1MV', text)

        # 设备名称标准化
        equipment_mapping = {
            '主变': '变压器', '主变压器': '变压器',
            '开关': '断路器', '刀闸': '隔离开关',
            'GIS': 'GIS设备', 'CT': '电流互感器', 'PT': '电压互感器'
        }

        for old_term, new_term in equipment_mapping.items():
            text = text.replace(old_term, new_term)

        return text

    def _extract_key_info(self, query: str) -> str:
        """提取关键信息"""
        # 提取设备类型
        equipment_types = []
        for term in self.power_terms:
            if term in query and len(term) > 2:
                equipment_types.append(term)

        # 提取故障类型
        fault_keywords = ['故障', '跳闸', '动作', '保护', '异常', '缺陷']
        fault_types = [kw for kw in fault_keywords if kw in query]

        # 重新组织查询，突出重要信息
        key_terms = equipment_types + fault_types
        if key_terms:
            return ' '.join(key_terms) + ' ' + query

        return query

    def _collect_all_documents(self) -> List[Dict[str, Any]]:
        """收集所有可用文档"""
        documents = []

        try:
            # 从数据管理器获取数据
            if data_manager:
                # 设备数据
                equipment_data = get_cached_data('equipment_data')
                for eq_id, eq_info in equipment_data.items():
                    if isinstance(eq_info, dict):
                        content = self._format_equipment_data(eq_info)
                        documents.append({
                            'id': f'equipment_{eq_id}',
                            'type': 'equipment',
                            'content': content,
                            'source': f'设备数据_{eq_id}',
                            'metadata': eq_info
                        })

                # 故障模式数据
                fault_patterns = data_manager.get_fault_patterns()
                for fp_id, fp_info in fault_patterns.items():
                    content = str(fp_info)
                    if len(content) > 20:  # 过滤太短的内容
                        documents.append({
                            'id': f'fault_pattern_{fp_id}',
                            'type': 'fault_pattern',
                            'content': content,
                            'source': f'故障模式_{fp_id}',
                            'metadata': {'pattern_id': fp_id}
                        })

                # 案例研究数据
                case_studies = get_cached_data('case_studies')
                for cs_id, cs_content in case_studies.items():
                    if isinstance(cs_content, str) and len(cs_content) > 50:
                        documents.append({
                            'id': f'case_study_{cs_id}',
                            'type': 'case_study',
                            'content': cs_content[:1000],  # 限制长度
                            'source': f'案例研究_{cs_id}',
                            'metadata': {'case_id': cs_id}
                        })

                # 知识库数据
                knowledge_base = get_cached_data('knowledge_base')
                for kb_id, kb_content in knowledge_base.items():
                    if isinstance(kb_content, str) and len(kb_content) > 30:
                        documents.append({
                            'id': f'knowledge_{kb_id}',
                            'type': 'knowledge_base',
                            'content': kb_content[:1200],
                            'source': f'知识库_{kb_id}',
                            'metadata': {'kb_id': kb_id}
                        })

            print(f"📚 收集到 {len(documents)} 个文档")
            return documents

        except Exception as e:
            print(f"收集文档失败: {e}")
            return []

    def _format_equipment_data(self, eq_info: Dict) -> str:
        """格式化设备数据为可搜索文本"""
        try:
            parts = []

            # 设备基本信息
            if 'name' in eq_info:
                parts.append(f"设备名称: {eq_info['name']}")
            if 'type' in eq_info:
                parts.append(f"设备类型: {eq_info['type']}")
            if 'voltage_level' in eq_info:
                parts.append(f"电压等级: {eq_info['voltage_level']}")
            if 'location' in eq_info:
                parts.append(f"位置: {eq_info['location']}")

            # 运行状态
            if 'status' in eq_info:
                parts.append(f"运行状态: {eq_info['status']}")
            if 'last_maintenance' in eq_info:
                parts.append(f"最后检修: {eq_info['last_maintenance']}")

            # 技术参数
            if 'parameters' in eq_info:
                params = eq_info['parameters']
                if isinstance(params, dict):
                    for key, value in params.items():
                        parts.append(f"{key}: {value}")

            return ' '.join(parts)

        except Exception:
            return str(eq_info)

    def _semantic_retrieval(self, query: str, documents: List[Dict], top_k: int) -> List[Dict[str, Any]]:
        """语义相似度检索"""
        try:
            if not documents:
                return []

            # 准备文档文本
            doc_texts = [doc['content'] for doc in documents]

            # 向量化
            all_texts = [query] + doc_texts
            tfidf_matrix = self.vectorizer.fit_transform(all_texts)

            # 计算相似度
            query_vector = tfidf_matrix[0:1]
            doc_vectors = tfidf_matrix[1:]
            similarities = cosine_similarity(query_vector, doc_vectors)[0]

            # 构建结果
            results = []
            for i, (doc, similarity) in enumerate(zip(documents, similarities)):
                if similarity > 0.05:  # 降低相似度阈值，提升数据利用率
                    results.append({
                        'type': doc['type'],
                        'source': doc['source'],
                        'content': doc['content'],
                        'relevance': float(similarity),
                        'retrieval_method': 'semantic',
                        'metadata': doc.get('metadata', {})
                    })

            # 按相似度排序
            results.sort(key=lambda x: x['relevance'], reverse=True)
            return results[:top_k]

        except Exception as e:
            print(f"语义检索失败: {e}")
            return []

    def _keyword_retrieval(self, query: str, documents: List[Dict], top_k: int) -> List[Dict[str, Any]]:
        """关键词精确匹配检索"""
        try:
            query_lower = query.lower()
            query_words = set(query_lower.split())

            results = []
            for doc in documents:
                content_lower = doc['content'].lower()
                content_words = set(content_lower.split())

                # 计算关键词匹配分数
                intersection = query_words.intersection(content_words)
                if intersection:
                    score = len(intersection) / len(query_words)

                    # 电力术语加权
                    tech_bonus = 0
                    for word in intersection:
                        if any(term.lower() in word for term in self.power_terms):
                            tech_bonus += 0.2

                    final_score = min(score + tech_bonus, 1.0)

                    if final_score > 0.15:  # 降低阈值，提升数据利用率
                        results.append({
                            'type': doc['type'],
                            'source': doc['source'],
                            'content': doc['content'],
                            'relevance': final_score,
                            'retrieval_method': 'keyword',
                            'metadata': doc.get('metadata', {})
                        })

            results.sort(key=lambda x: x['relevance'], reverse=True)
            return results[:top_k]

        except Exception as e:
            print(f"关键词检索失败: {e}")
            return []

    def _technical_term_retrieval(self, query: str, documents: List[Dict], top_k: int) -> List[Dict[str, Any]]:
        """电力专业术语匹配检索"""
        try:
            # 提取查询中的技术术语
            query_terms = set()
            query_lower = query.lower()

            for term in self.power_terms:
                if term.lower() in query_lower:
                    query_terms.add(term.lower())

            if not query_terms:
                return []

            results = []
            for doc in documents:
                content_lower = doc['content'].lower()

                # 计算技术术语匹配分数
                matched_terms = 0
                for term in query_terms:
                    if term in content_lower:
                        matched_terms += 1

                if matched_terms > 0:
                    score = matched_terms / len(query_terms)

                    # 根据文档类型调整权重
                    if doc['type'] == 'fault_pattern':
                        score *= 1.3  # 故障模式权重更高
                    elif doc['type'] == 'equipment':
                        score *= 1.1  # 设备数据权重稍高

                    results.append({
                        'type': doc['type'],
                        'source': doc['source'],
                        'content': doc['content'],
                        'relevance': min(score, 1.0),
                        'retrieval_method': 'technical',
                        'metadata': doc.get('metadata', {})
                    })

            results.sort(key=lambda x: x['relevance'], reverse=True)
            return results[:top_k]

        except Exception as e:
            print(f"技术术语检索失败: {e}")
            return []

    def _fault_specific_retrieval(self, query: str, documents: List[Dict], top_k: int) -> List[Dict[str, Any]]:
        """故障类型专门检索"""
        try:
            # 定义故障类型映射
            fault_patterns = {
                '差动保护': ['差动', '变压器内部', '绕组', '短路'],
                '距离保护': ['距离', '线路', '阻抗', '短路'],
                '零序保护': ['零序', '接地', '单相', '不平衡'],
                '过流保护': ['过流', '过载', '电流', '超限'],
                '接地故障': ['接地', '对地', '绝缘', '泄漏'],
                '套管故障': ['套管', '渗油', '绝缘', '密封'],
                '断路器故障': ['断路器', '拒动', '误动', '操作'],
                '电缆故障': ['电缆', '绝缘', '击穿', '老化']
            }

            query_lower = query.lower()
            matched_patterns = []

            # 识别查询中的故障类型
            for fault_type, keywords in fault_patterns.items():
                if any(keyword in query_lower for keyword in keywords):
                    matched_patterns.append(fault_type)

            if not matched_patterns:
                return []

            results = []
            for doc in documents:
                content_lower = doc['content'].lower()

                # 计算故障类型匹配分数
                match_score = 0
                for pattern in matched_patterns:
                    pattern_keywords = fault_patterns[pattern]
                    pattern_matches = sum(1 for kw in pattern_keywords if kw in content_lower)
                    if pattern_matches > 0:
                        match_score += pattern_matches / len(pattern_keywords)

                if match_score > 0.2:  # 降低故障类型匹配阈值
                    # 根据文档类型给予额外权重
                    if doc['type'] == 'fault_pattern':
                        match_score *= 1.5  # 故障模式文档权重更高
                    elif doc['type'] == 'case_study':
                        match_score *= 1.2  # 案例研究权重适中

                    results.append({
                        'type': doc['type'],
                        'source': doc['source'],
                        'content': doc['content'],
                        'relevance': min(match_score, 1.0),
                        'retrieval_method': 'fault_specific',
                        'metadata': doc.get('metadata', {})
                    })

            results.sort(key=lambda x: x['relevance'], reverse=True)
            return results[:top_k]

        except Exception as e:
            print(f"故障专门检索失败: {e}")
            return []

    def _fuse_and_rerank(self, results: List[Dict], query: str, top_k: int) -> List[Dict[str, Any]]:
        """结果融合和重排序"""
        try:
            # 去重（基于内容相似度）
            unique_results = self._deduplicate_results(results)

            # 重新计算综合分数
            for result in unique_results:
                base_score = result['relevance']
                method = result['retrieval_method']

                # 方法权重 (优化权重分配)
                method_weights = {
                    'semantic': 0.35,
                    'keyword': 0.25,
                    'technical': 0.25,
                    'fault_specific': 0.15
                }

                # 文档类型权重 (提升专业数据权重)
                type_weights = {
                    'fault_pattern': 1.4,  # 故障模式最重要
                    'equipment': 1.2,      # 设备数据很重要
                    'knowledge_base': 1.0, # 知识库标准权重
                    'case_study': 0.8      # 案例研究适中权重
                }

                # 计算综合分数
                method_weight = method_weights.get(method, 0.3)
                type_weight = type_weights.get(result['type'], 1.0)

                final_score = base_score * method_weight * type_weight
                result['final_score'] = final_score

            # 按综合分数排序
            unique_results.sort(key=lambda x: x.get('final_score', 0), reverse=True)

            # 返回前top_k个结果
            return unique_results[:top_k]

        except Exception as e:
            print(f"结果融合失败: {e}")
            return results[:top_k]

    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """结果去重"""
        seen_sources = set()
        unique_results = []

        for result in results:
            source = result.get('source', '')
            if source not in seen_sources:
                seen_sources.add(source)
                unique_results.append(result)

        return unique_results

def _fallback_extract_context(query: str):
    """降级的上下文提取方法"""
    try:
        context_data = []

        # 简单的关键词匹配
        if data_manager:
            equipment_data = get_cached_data('equipment_data')
            for equipment_id, equipment_info in equipment_data.items():
                if any(keyword in query.lower() for keyword in ['变压器', '断路器', '隔离开关', '电缆']):
                    if isinstance(equipment_info, dict):
                        context_data.append({
                            'type': 'equipment',
                            'source': f'equipment_data_{equipment_id}',
                            'content': str(equipment_info),
                            'relevance': calculate_relevance(query, str(equipment_info))
                        })

        # 按相关性排序并返回前5个
        context_data.sort(key=lambda x: x['relevance'], reverse=True)
        return context_data[:5]

    except Exception as e:
        print(f"降级上下文提取失败: {e}")
        return []

def clean_report_format(content: str, thinking_mode: bool = False) -> str:
    """清理报告格式，转换为对话风格"""
    try:
        if not content:
            return content

        print(f"🧹 开始清理报告格式，原内容长度: {len(content)}")

        # 如果是推理模式，只做基本清理，不进行结构化处理
        if thinking_mode:
            print("🧠 推理模式：只进行基本清理，保持非结构化格式")
            # 只移除明显的报告格式标记，保持自然语言流
            cleaned_content = content

            # 移除报告头部信息
            report_patterns = [
                r'.*?故障诊断报告.*?\n',
                r'.*?编号：.*?\n',
                r'.*?日期：.*?\n',
                r'.*?专家.*?\n',
                r'.*?签字.*?\n'
            ]

            for pattern in report_patterns:
                cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.IGNORECASE)

            # 清理多余空行
            lines = [line.strip() for line in cleaned_content.split('\n') if line.strip()]
            return '\n'.join(lines)

        # 非推理模式继续原有的结构化处理

        # 更强力的报告格式清理
        cleaned_content = content

        # 1. 移除报告头部和尾部信息（使用正则表达式）
        report_header_patterns = [
            r'.*?故障诊断报告.*?\n',
            r'.*?编号：.*?\n',
            r'.*?报告编号.*?\n',
            r'.*?日期：.*?\n',
            r'.*?报告日期.*?\n',
            r'.*?诊断专家.*?\n',
            r'.*?专家组.*?\n',
            r'.*?签字.*?\n',
            r'.*?签名.*?\n',
            r'.*?盖章.*?\n',
            r'.*?白银市电力系统故障诊断.*?\n',
            r'.*?白银市电力公司.*?\n'
        ]

        for pattern in report_header_patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.IGNORECASE)

        # 2. 移除报告结构标记
        structure_patterns = [
            r'一、|二、|三、|四、|五、|六、',
            r'（编号：.*?）',
            r'（注：.*?）'
        ]

        for pattern in structure_patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content)

        # 3. 清理多余的空行和符号
        lines = cleaned_content.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if not line or line in ['。', '、', '：', ':', '。 。']:
                continue

            # 移除行首的序号标记
            line = re.sub(r'^[一二三四五六七八九十]+、\s*', '', line)
            line = re.sub(r'^\d+\.\s*', '', line)

            if line:
                cleaned_lines.append(line)

        # 4. 重新组织内容，保持结构性
        cleaned_content = '\n\n'.join(cleaned_lines)  # 使用双换行增加可读性

        # 5. 强制确保完整的4部分结构
        if cleaned_content:
            print(f"🔧 开始强制结构化处理")
            lines = cleaned_content.split('\n')
            structured_lines = []
            section_count = 0

            # 标准4部分结构
            required_sections = [
                '故障性质判断',
                '原因分析',
                '处理方案',
                '预防措施'
            ]

            # 检测现有章节
            found_sections = []
            section_content = {}
            current_section = None
            current_content = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是章节标题
                is_section_title = False
                for section in required_sections:
                    if section in line or any(keyword in line for keyword in ['故障性质', '原因分析', '处理方法', '处理方案', '预防措施', '预防建议']):
                        if current_section:
                            section_content[current_section] = current_content

                        # 标准化章节名称
                        if '故障性质' in line:
                            current_section = '故障性质判断'
                        elif '原因分析' in line:
                            current_section = '原因分析'
                        elif '处理' in line:
                            current_section = '处理方案'
                        elif '预防' in line:
                            current_section = '预防措施'

                        if current_section and current_section not in found_sections:
                            found_sections.append(current_section)

                        current_content = []
                        is_section_title = True
                        break

                if not is_section_title and current_section:
                    current_content.append(line)

            # 保存最后一个章节
            if current_section and current_content:
                section_content[current_section] = current_content

            # 强制构建完整的4部分结构
            for i, section in enumerate(required_sections, 1):
                structured_lines.append(f'{i}. {section}')

                if section in section_content and section_content[section]:
                    for content_line in section_content[section]:
                        if content_line.strip():
                            if not content_line.startswith(('   ', '-', '•')):
                                content_line = '   - ' + content_line.strip()
                            structured_lines.append(content_line)
                else:
                    # 如果缺少某个部分，添加占位内容
                    structured_lines.append(f'   - 需要进一步分析{section}相关内容')

                structured_lines.append('')  # 添加空行分隔

            cleaned_content = '\n'.join(structured_lines)
            print(f"🔧 强制结构化完成，确保4个完整部分")

        # 6. 添加对话式开头
        if cleaned_content:
            if '变压器' in cleaned_content:
                cleaned_content = "这是一个变压器故障。\n\n" + cleaned_content
            elif '断路器' in cleaned_content:
                cleaned_content = "这是一个断路器故障。\n\n" + cleaned_content
            elif '电缆' in cleaned_content:
                cleaned_content = "这是一个电缆故障。\n\n" + cleaned_content
            else:
                cleaned_content = "这是一个电力设备故障。\n\n" + cleaned_content

        print(f"🧹 清理完成，新内容长度: {len(cleaned_content)}")
        return cleaned_content

    except Exception as e:
        print(f"清理报告格式失败: {e}")
        return content

def clean_final_analysis_for_pure_text(final_content: str) -> str:
    """清理最终分析结果，转换为自然语言格式"""
    if not final_content:
        return ""

    # 将结构化内容转换为自然语言段落
    cleaned = convert_structured_to_natural_language(final_content)

    # 移除多余的Markdown格式标记，但保留**加粗**
    cleaned = re.sub(r'#{1,6}\s+', '', cleaned)
    cleaned = re.sub(r'(?<!\*)\*(?!\*)([^*]+)(?<!\*)\*(?!\*)', r'\1', cleaned)
    cleaned = re.sub(r'`(.*?)`', r'\1', cleaned)
    cleaned = re.sub(r'<[^>]+>', '', cleaned)

    # 清理多余的空行，保持段落结构
    lines = [line.strip() for line in cleaned.split('\n')]
    cleaned_lines = []
    prev_empty = False

    for line in lines:
        if line:
            cleaned_lines.append(line)
            prev_empty = False
        elif not prev_empty:
            cleaned_lines.append('')
            prev_empty = True

    result = '\n'.join(cleaned_lines).strip()

    # 确保重要内容有适当的加粗标记
    result = add_emphasis_to_important_content(result)

    return result


def convert_structured_to_natural_language(content: str) -> str:
    """将结构化内容转换为自然语言段落"""
    if not content:
        return ""

    # 处理列表项，转换为自然语言段落
    lines = content.split('\n')
    paragraphs = []
    current_paragraph = []

    for line in lines:
        line = line.strip()
        if not line:
            if current_paragraph:
                paragraphs.append(' '.join(current_paragraph))
                current_paragraph = []
            continue

        # 处理编号列表
        if re.match(r'^\d+\.\s*', line):
            if current_paragraph:
                paragraphs.append(' '.join(current_paragraph))
                current_paragraph = []
            # 移除编号，转换为自然语言
            content_part = re.sub(r'^\d+\.\s*', '', line)
            current_paragraph.append(content_part)

        # 处理无序列表
        elif re.match(r'^\s*[-*+]\s+', line):
            if current_paragraph:
                paragraphs.append(' '.join(current_paragraph))
                current_paragraph = []
            # 移除列表标记，转换为自然语言
            content_part = re.sub(r'^\s*[-*+]\s+', '', line)
            current_paragraph.append(content_part)

        # 处理标题
        elif re.match(r'^#{1,6}\s+', line):
            if current_paragraph:
                paragraphs.append(' '.join(current_paragraph))
                current_paragraph = []
            # 移除标题标记，作为段落开始
            title_content = re.sub(r'^#{1,6}\s+', '', line)
            current_paragraph.append(title_content + '：')

        else:
            # 普通文本行
            current_paragraph.append(line)

    # 处理最后一个段落
    if current_paragraph:
        paragraphs.append(' '.join(current_paragraph))

    # 连接段落，形成自然语言
    result = '\n\n'.join(paragraphs)

    # 优化段落连接，使其更自然
    result = optimize_paragraph_flow(result)

    return result


def optimize_paragraph_flow(content: str) -> str:
    """优化段落流畅性，使其更像自然语言"""
    if not content:
        return ""

    # 添加连接词，使段落更流畅
    paragraphs = content.split('\n\n')
    optimized_paragraphs = []

    for i, paragraph in enumerate(paragraphs):
        if not paragraph.strip():
            continue

        # 为后续段落添加适当的连接词
        if i > 0 and paragraph.strip():
            # 根据内容类型添加连接词
            if any(word in paragraph for word in ['因此', '所以', '综上', '总结']):
                # 结论性段落，保持原样
                optimized_paragraphs.append(paragraph)
            elif any(word in paragraph for word in ['建议', '应该', '需要', '必须']):
                # 建议性段落
                optimized_paragraphs.append(f"基于以上分析，{paragraph}")
            elif any(word in paragraph for word in ['原因', '导致', '由于']):
                # 原因分析段落
                optimized_paragraphs.append(f"从技术角度分析，{paragraph}")
            else:
                # 普通段落
                optimized_paragraphs.append(paragraph)
        else:
            optimized_paragraphs.append(paragraph)

    return '\n\n'.join(optimized_paragraphs)


def combine_reasoning_and_result(reasoning_process: str, final_analysis: str) -> str:
    """结合推理过程和最终结果，生成连贯的自然语言分析报告"""
    if not reasoning_process and not final_analysis:
        return "暂无分析结果"

    if not reasoning_process:
        return clean_final_analysis_for_pure_text(final_analysis)

    if not final_analysis:
        return clean_reasoning_for_pure_text(reasoning_process)

    # 清理推理过程和最终结果
    cleaned_reasoning = clean_reasoning_for_pure_text(reasoning_process)
    cleaned_final = clean_final_analysis_for_pure_text(final_analysis)

    # 构建连贯的分析报告
    combined_parts = []

    # 1. 添加分析过程（如果有实质内容）
    if cleaned_reasoning and len(cleaned_reasoning.strip()) > 50:
        # 为推理过程添加引导语
        reasoning_intro = "**分析过程：**"
        combined_parts.append(f"{reasoning_intro}\n{cleaned_reasoning}")

    # 2. 添加连接段落
    if cleaned_reasoning and cleaned_final:
        transition = "\n\n**综合分析结果：**"
        combined_parts.append(transition)

    # 3. 添加最终分析结果
    if cleaned_final:
        combined_parts.append(cleaned_final)

    # 4. 如果没有有效的推理过程，直接返回最终结果
    if not cleaned_reasoning or len(cleaned_reasoning.strip()) <= 50:
        return cleaned_final if cleaned_final else "分析结果暂不可用"

    # 5. 组合所有部分
    combined_text = '\n'.join(combined_parts)

    # 6. 优化整体流畅性
    combined_text = optimize_combined_analysis_flow(combined_text)

    return combined_text


def optimize_combined_analysis_flow(content: str) -> str:
    """优化组合分析的流畅性"""
    if not content:
        return ""

    # 确保段落间有适当的间距
    content = re.sub(r'\n{3,}', '\n\n', content)

    # 优化标题和内容的连接
    content = re.sub(r'\*\*([^*]+)：\*\*\s*\n', r'**\1：**\n\n', content)

    # 确保重要信息突出显示
    important_patterns = [
        (r'(故障原因|主要原因|根本原因)', r'**\1**'),
        (r'(处理建议|解决方案|应急措施)', r'**\1**'),
        (r'(安全注意|重要提醒|特别注意)', r'**\1**'),
        (r'(立即|紧急|严重|危险)', r'**\1**')
    ]

    for pattern, replacement in important_patterns:
        content = re.sub(pattern, replacement, content)

    return content.strip()


def add_emphasis_to_important_content(content: str) -> str:
    """为重要内容添加加粗标记"""
    if not content:
        return ""

    # 重要关键词列表
    important_keywords = [
        '立即', '紧急', '严重', '危险', '警告', '注意', '重要', '关键',
        '故障原因', '处理建议', '安全措施', '预防措施', '应急处理',
        '需要立即', '必须', '应当', '建议', '禁止', '避免'
    ]

    result = content
    for keyword in important_keywords:
        # 如果关键词还没有被加粗，则添加加粗标记
        pattern = f'(?<!\\*)\\b{re.escape(keyword)}\\b(?!\\*)'
        replacement = f'**{keyword}**'
        result = re.sub(pattern, replacement, result)

    return result


def clean_reasoning_for_pure_text(reasoning_content: str) -> str:
    """清理推理过程，确保纯文字输出，但保持自然的思考流"""
    if not reasoning_content:
        return ""

    # 对于推理过程，我们要保持更自然的文本流
    # 只移除明显的结构化标记，保留自然的思考过程
    cleaned = reasoning_content

    # 移除明显的标题标记（### 开头的）
    cleaned = re.sub(r'^#{1,6}\s+', '', cleaned, flags=re.MULTILINE)

    # 移除明显的列表标记（行首的数字编号和符号）
    cleaned = re.sub(r'^\s*\d+\.\s+', '', cleaned, flags=re.MULTILINE)
    cleaned = re.sub(r'^\s*[-*+]\s+', '', cleaned, flags=re.MULTILINE)

    # 移除代码块标记
    cleaned = re.sub(r'```.*?```', '', cleaned, flags=re.DOTALL)
    cleaned = re.sub(r'`(.*?)`', r'\1', cleaned)

    # 移除XML标签
    cleaned = re.sub(r'<[^>]+>', '', cleaned)

    # 保持自然的段落结构，不要过度清理空行
    # 只是简单地去除首尾空白
    cleaned = cleaned.strip()

    return cleaned


def smart_split_reasoning_and_result(ai_content: str) -> tuple:
    """智能分离推理过程和最终结果 - 基于DeepSeek-R1标准格式优化"""
    if not ai_content or len(ai_content.strip()) < 100:
        return "", ai_content

    print(f"🔍 开始智能分离，内容长度: {len(ai_content)}")

    # 方法1：优先查找标准的thinking标签
    import re

    # 查找 <thinking>...</thinking> 标签
    thinking_pattern = r'<thinking>(.*?)</thinking>'
    thinking_match = re.search(thinking_pattern, ai_content, re.DOTALL | re.IGNORECASE)

    if thinking_match:
        reasoning_process = thinking_match.group(1).strip()
        # 移除thinking标签后的内容作为最终结果
        final_analysis = re.sub(thinking_pattern, '', ai_content, flags=re.DOTALL | re.IGNORECASE).strip()

        # 清理最终分析中的多余空行
        final_analysis = re.sub(r'\n\s*\n\s*\n', '\n\n', final_analysis)
        final_analysis = final_analysis.strip()

        print(f"🧠 通过thinking标签分离 - 推理: {len(reasoning_process)} 字符, 结果: {len(final_analysis)} 字符")
        return reasoning_process, final_analysis

    # 方法2：查找中文思考标记
    chinese_thinking_patterns = [
        r'【思考过程】(.*?)【分析结果】',
        r'【推理过程】(.*?)【结论】',
        r'【专家思考】(.*?)【诊断报告】',
        r'思考过程：(.*?)分析结果：',
        r'推理过程：(.*?)结论：'
    ]

    for pattern in chinese_thinking_patterns:
        match = re.search(pattern, ai_content, re.DOTALL | re.IGNORECASE)
        if match:
            reasoning_process = match.group(1).strip()
            # 提取分析结果部分
            final_analysis = ai_content[match.end():].strip()
            if not final_analysis:
                # 如果后面没有内容，尝试提取标记后的内容
                final_analysis = re.sub(pattern, '', ai_content, flags=re.DOTALL | re.IGNORECASE).strip()

            print(f"🧠 通过中文标记分离 - 推理: {len(reasoning_process)} 字符, 结果: {len(final_analysis)} 字符")
            return reasoning_process, final_analysis

    # 方法3：基于DeepSeek-R1实际输出特征的智能分割
    # 查找明确的分析报告开始标记
    report_start_markers = [
        "基于上述思考的专业故障诊断报告",
        "专业故障诊断报告",
        "基于以上分析",
        "综合分析可以得出",
        "故障诊断结果",
        "**故障前系统运行状态评估",
        "**设备技术特性核实",
        "根据故障现象分析",
        "从技术参数来看"
    ]

    best_split_pos = -1
    best_marker = ""

    for marker in report_start_markers:
        pos = ai_content.find(marker)
        if pos != -1:
            # 确保不会太早分割（至少在30%之后）
            if pos > len(ai_content) * 0.3:
                if best_split_pos == -1 or pos < best_split_pos:
                    best_split_pos = pos
                    best_marker = marker

    if best_split_pos != -1:
        reasoning = ai_content[:best_split_pos].strip()
        result = ai_content[best_split_pos:].strip()
        print(f"🔍 通过报告标记'{best_marker}'分离内容")
        print(f"   推理过程: {len(reasoning)} 字符")
        print(f"   最终结果: {len(result)} 字符")
        return reasoning, result

    # 方法4：如果没有找到明确标记，使用智能比例分割
    # 根据内容特征动态调整分割比例
    if len(ai_content) < 500:
        split_ratio = 0.3  # 短内容，更多作为最终结果
    elif len(ai_content) < 1000:
        split_ratio = 0.5  # 中等长度
    else:
        split_ratio = 0.6  # 长内容，适中分割

    split_pos = int(len(ai_content) * split_ratio)

    # 尝试在合适的位置截断（避免截断句子）
    for i in range(split_pos, min(split_pos + 200, len(ai_content))):
        if ai_content[i] in ['。', '\n', '！', '？']:
            split_pos = i + 1
            break

    reasoning = ai_content[:split_pos].strip()
    result = ai_content[split_pos:].strip()

    print(f"🔍 按智能比例({split_ratio})分离内容")
    print(f"   推理过程: {len(reasoning)} 字符")
    print(f"   最终结果: {len(result)} 字符")
    return reasoning, result


def looks_like_reasoning(content_chunk: str) -> bool:
    """判断内容块是否看起来像推理过程"""
    if not content_chunk or len(content_chunk.strip()) < 10:
        return False

    # 推理过程的特征标识
    reasoning_indicators = [
        "让我", "我需要", "首先", "分析", "考虑", "思考", "推理", "判断", "评估",
        "根据", "基于", "从", "可以看出", "显然", "因此", "所以", "综上",
        "这个问题", "这种情况", "这里", "接下来", "然后", "最后"
    ]

    # 检查是否包含推理标识词
    return any(indicator in content_chunk for indicator in reasoning_indicators)

def determine_content_phase(content_buffer: str, reasoning_content: str, final_content: str) -> str:
    """智能判断当前内容属于哪个阶段：reasoning 或 final"""

    # 如果缓冲区内容太短，无法判断
    if len(content_buffer.strip()) < 20:
        return 'reasoning' if len(reasoning_content) == 0 else 'continue'

    # 获取最近的内容用于判断
    recent_content = content_buffer[-200:] if len(content_buffer) > 200 else content_buffer

    # 强烈的推理过程标识
    strong_reasoning_indicators = [
        "让我仔细分析", "我需要考虑", "首先要分析", "从技术角度", "根据经验",
        "这种情况下", "我判断", "可以推断", "综合分析", "基于以上",
        "让我思考", "需要检查", "应该考虑", "可能的原因", "初步判断"
    ]

    # 强烈的最终结果标识
    strong_final_indicators = [
        "综上所述", "总结如下", "处理建议", "解决方案", "预防措施",
        "具体步骤", "操作流程", "注意事项", "建议采取", "应该立即",
        "检修方案", "维护计划", "安全措施", "技术要求", "标准规范"
    ]

    # 检查强烈标识
    for indicator in strong_reasoning_indicators:
        if indicator in recent_content:
            return 'reasoning'

    for indicator in strong_final_indicators:
        if indicator in recent_content:
            return 'final'

    # 基于内容长度和结构判断阶段转换
    total_reasoning_length = len(reasoning_content)
    total_final_length = len(final_content)

    # 如果推理内容已经很长（>1000字符），可能开始进入最终阶段
    if total_reasoning_length > 1000:
        # 检查是否有结论性语言
        conclusion_patterns = [
            "因此", "所以", "综合", "总的来说", "最终", "结论",
            "建议", "方案", "措施", "步骤", "处理", "解决"
        ]

        if any(pattern in recent_content for pattern in conclusion_patterns):
            return 'final'

    # 如果已经有最终内容，继续最终阶段
    if total_final_length > 0:
        return 'final'

    # 默认情况：如果推理内容较少，继续推理阶段
    if total_reasoning_length < 500:
        return 'reasoning'

    # 其他情况返回继续当前阶段
    return 'continue'


def extract_reasoning_process(ai_content: str) -> str:
    """提取DeepSeek R1的推理过程（完全重写版本）"""
    try:
        if not ai_content:
            return ""

        print(f"🔍 原始AI内容长度: {len(ai_content)}")
        print(f"🔍 原始AI内容前500字符: {ai_content[:500]}")
        print(f"🔍 原始AI内容后500字符: {ai_content[-500:]}")

        # DeepSeek-R1的实际输出格式分析
        # 方法1: 查找标准的思考标记
        thinking_patterns = [
            # XML格式
            (r'<think>(.*?)</think>', re.DOTALL | re.IGNORECASE),
            (r'<thinking>(.*?)</thinking>', re.DOTALL | re.IGNORECASE),
            (r'<reasoning>(.*?)</reasoning>', re.DOTALL | re.IGNORECASE),
            # 中文标记
            (r'【思考过程】(.*?)【分析结果】', re.DOTALL | re.IGNORECASE),
            (r'【推理过程】(.*?)【结论】', re.DOTALL | re.IGNORECASE),
            # 英文标记
            (r'Thinking:(.*?)Analysis:', re.DOTALL | re.IGNORECASE),
            (r'Reasoning:(.*?)Conclusion:', re.DOTALL | re.IGNORECASE),
        ]

        for pattern, flags in thinking_patterns:
            matches = re.findall(pattern, ai_content, flags)
            if matches:
                reasoning = matches[0].strip()
                if len(reasoning) > 50:  # 确保提取的内容有意义
                    print(f"🧠 通过标记模式提取推理过程: {len(reasoning)} 字符")
                    return reasoning

        # 方法2: 基于DeepSeek-R1的实际输出特征进行智能分割
        # 查找常见的思考开始标记
        thinking_starts = [
            "让我来分析", "我需要分析", "首先分析", "让我思考", "我来思考",
            "分析这个问题", "让我仔细分析", "我需要仔细考虑", "让我逐步分析",
            "根据题目描述", "从题目可以看出", "这是一个", "这个问题"
        ]

        # 查找常见的结论开始标记
        conclusion_starts = [
            "综上所述", "总结一下", "因此", "所以", "综合分析",
            "基于以上分析", "通过分析可以得出", "分析结果", "诊断结果",
            "故障原因", "处理建议", "解决方案", "最终结论"
        ]

        # 尝试找到思考开始和结论开始的位置
        thinking_start_pos = -1
        conclusion_start_pos = -1

        for start_marker in thinking_starts:
            pos = ai_content.find(start_marker)
            if pos != -1 and pos < len(ai_content) * 0.3:  # 在前30%找到
                thinking_start_pos = pos
                break

        for conclusion_marker in conclusion_starts:
            pos = ai_content.find(conclusion_marker)
            if pos != -1 and pos > len(ai_content) * 0.4:  # 在后60%找到
                conclusion_start_pos = pos
                break

        # 如果找到了分割点，提取推理过程
        if thinking_start_pos != -1 and conclusion_start_pos != -1 and conclusion_start_pos > thinking_start_pos:
            reasoning = ai_content[thinking_start_pos:conclusion_start_pos].strip()
            print(f"🧠 通过智能分割提取推理过程: {len(reasoning)} 字符")
            return reasoning

        # 方法3: 如果内容很长，按比例分割
        if len(ai_content) > 1000:
            # 查找段落分割点
            paragraphs = ai_content.split('\n\n')
            if len(paragraphs) > 2:
                # 取前面的段落作为推理过程
                reasoning_paragraphs = []
                total_length = 0
                target_length = len(ai_content) * 0.6  # 取60%作为推理过程

                for para in paragraphs:
                    if total_length + len(para) < target_length:
                        reasoning_paragraphs.append(para)
                        total_length += len(para)
                    else:
                        break

                if reasoning_paragraphs:
                    reasoning = '\n\n'.join(reasoning_paragraphs).strip()
                    print(f"🧠 通过段落分割提取推理过程: {len(reasoning)} 字符")
                    return reasoning

            # 如果段落分割失败，按字符数分割
            reasoning_length = int(len(ai_content) * 0.65)
            reasoning = ai_content[:reasoning_length].strip()

            # 尝试在合适的位置截断（避免截断句子）
            last_period = reasoning.rfind('。')
            last_newline = reasoning.rfind('\n')
            cut_pos = max(last_period, last_newline)

            if cut_pos > reasoning_length * 0.8:  # 如果截断点在后80%
                reasoning = reasoning[:cut_pos + 1].strip()

            print(f"🧠 通过比例分割提取推理过程: {len(reasoning)} 字符")
            return reasoning

        # 方法4: 短文本处理
        if len(ai_content) <= 1000:
            # 对于短文本，如果包含明显的思考内容，返回全部
            thinking_indicators = ["分析", "考虑", "思考", "推理", "判断", "评估"]
            if any(indicator in ai_content for indicator in thinking_indicators):
                print(f"🧠 短文本包含思考内容，返回全部: {len(ai_content)} 字符")
                return ai_content.strip()

        # 方法5: 默认处理 - 返回前半部分
        half_length = len(ai_content) // 2
        reasoning = ai_content[:half_length].strip()
        print(f"🧠 默认处理，返回前半部分: {len(reasoning)} 字符")
        return reasoning

    except Exception as e:
        print(f"❌ 提取推理过程失败: {e}")
        # 出错时返回原内容的前60%
        try:
            fallback_length = int(len(ai_content) * 0.6) if ai_content else 0
            return ai_content[:fallback_length].strip() if ai_content else ""
        except:
            return ai_content.strip() if ai_content else ""

def extract_final_analysis(ai_content: str, reasoning_process: str) -> str:
    """从AI内容中提取最终分析部分（推理过程之后的内容）"""
    try:
        if not ai_content or not reasoning_process:
            return ai_content

        # 方法1: 直接移除推理过程部分
        remaining_content = ai_content.replace(reasoning_process, "").strip()

        # 方法2: 如果直接替换效果不好，尝试找到推理过程的结束位置
        if not remaining_content or len(remaining_content) < 100:
            reasoning_end_pos = ai_content.find(reasoning_process)
            if reasoning_end_pos != -1:
                end_pos = reasoning_end_pos + len(reasoning_process)
                remaining_content = ai_content[end_pos:].strip()

        # 方法3: 查找结论标记之后的内容
        if not remaining_content or len(remaining_content) < 100:
            conclusion_markers = [
                "综上所述", "总结一下", "因此", "所以", "综合分析",
                "基于以上分析", "通过分析可以得出", "分析结果", "诊断结果",
                "故障原因", "处理建议", "解决方案", "最终结论"
            ]

            for marker in conclusion_markers:
                marker_pos = ai_content.find(marker)
                if marker_pos != -1:
                    remaining_content = ai_content[marker_pos:].strip()
                    break

        # 如果还是没有合适的内容，返回原内容的后半部分
        if not remaining_content or len(remaining_content) < 50:
            half_pos = len(ai_content) // 2
            remaining_content = ai_content[half_pos:].strip()

        return remaining_content if remaining_content else ai_content

    except Exception as e:
        print(f"❌ 提取最终分析失败: {e}")
        return ai_content

def extract_conclusion_from_content(ai_content: str) -> str:
    """从内容中智能提取结论部分"""
    try:
        if not ai_content:
            return ""

        # 查找结论标记
        conclusion_markers = [
            "综上所述", "总结一下", "因此", "所以", "综合分析",
            "基于以上分析", "通过分析可以得出", "分析结果", "诊断结果",
            "故障原因", "处理建议", "解决方案", "最终结论", "结论："
        ]

        for marker in conclusion_markers:
            marker_pos = ai_content.find(marker)
            if marker_pos != -1:
                conclusion = ai_content[marker_pos:].strip()
                if len(conclusion) > 50:  # 确保有足够的内容
                    return conclusion

        # 如果没有找到明确的结论标记，返回后40%的内容
        conclusion_start = int(len(ai_content) * 0.6)
        conclusion = ai_content[conclusion_start:].strip()

        return conclusion if conclusion else ai_content

    except Exception as e:
        print(f"❌ 智能提取结论失败: {e}")
        return ai_content

def identify_equipment_type_from_real_data(query: str, real_data_context: list) -> str:
    """基于真实数据识别设备类型"""
    try:
        # 从查询中识别
        if "变压器" in query:
            return "transformer"
        elif "断路器" in query:
            return "circuit_breaker"
        elif "隔离开关" in query:
            return "isolator"
        elif "电缆" in query:
            return "cable"

        # 从真实数据上下文中识别
        for context_item in real_data_context:
            content = context_item.get('content', '').lower()
            if 'transformer' in content or '变压器' in content:
                return "transformer"
            elif 'breaker' in content or '断路器' in content:
                return "circuit_breaker"
            elif 'isolator' in content or '隔离开关' in content:
                return "isolator"
            elif 'cable' in content or '电缆' in content:
                return "cable"

        return "unknown"

    except Exception:
        return "unknown"

def clean_ai_content_for_unstructured_output(ai_content: str) -> str:
    """清理AI内容，保持结构化但移除过度格式化"""
    try:
        if not ai_content:
            return ""

        # 移除明显的Markdown格式标记，但保持基本结构
        cleaned_content = ai_content

        # 移除Markdown格式标记
        cleaned_content = re.sub(r'#{1,6}\s+', '', cleaned_content)  # 移除标题标记
        cleaned_content = re.sub(r'\*\*(.*?)\*\*', r'\1', cleaned_content)  # 移除粗体标记
        cleaned_content = re.sub(r'\*(.*?)\*', r'\1', cleaned_content)  # 移除斜体标记
        cleaned_content = re.sub(r'`(.*?)`', r'\1', cleaned_content)  # 移除代码标记

        # 保持基本的列表结构，但统一格式
        lines = cleaned_content.split('\n')
        structured_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                structured_lines.append('')
                continue

            # 保持数字编号结构
            if re.match(r'^\d+\.', line):
                structured_lines.append(line)
            # 统一列表项格式为 "- "
            elif line.startswith(('-', '•', '*')):
                line = '- ' + line.lstrip('-•* ').strip()
                structured_lines.append(line)
            else:
                structured_lines.append(line)

        # 重新组合，保持适当的换行
        result = '\n'.join(structured_lines)

        # 清理多余的空行，但保持段落分隔
        result = re.sub(r'\n{3,}', '\n\n', result)

        return result.strip()

    except Exception as e:
        print(f"清理AI内容失败: {e}")
        return ai_content.strip() if ai_content else ""

def get_real_data_fallback_analysis(query: str, real_data_context: list):
    """基于真实数据的备用分析"""
    try:
        # 基于真实数据构建专业分析
        analysis_content = f"基于白银市电力系统真实数据的专业分析：\n\n"

        if "变压器差动保护" in query:
            analysis_content += """变压器差动保护问题通常涉及多个方面的综合分析。根据白银市电力系统的实际运行经验，
差动保护异常可能源于电流互感器的饱和特性、保护装置的参数设置、或者变压器本体的绝缘状况。
在实际检查中，需要重点关注差动电流的波形特征、各侧电流的幅值和相位关系，
以及保护装置的动作逻辑。结合现场的运行工况和历史数据，可以更准确地判断故障的根本原因。"""
        else:
            analysis_content += f"针对'{query}'的故障现象，结合白银市电力系统的运行特点和历史案例，"
            analysis_content += "需要从设备状态、运行环境、保护配置等多个维度进行综合分析。"

        # 添加基于真实数据的上下文信息
        if real_data_context:
            analysis_content += f"\n\n根据系统中的{len(real_data_context)}条相关数据记录，"
            analysis_content += "建议重点关注以下几个方面的检查和分析工作。"

            for context in real_data_context[:3]:
                if context.get('type') == 'case_study':
                    analysis_content += f" 参考相似案例的处理经验，"
                elif context.get('type') == 'equipment':
                    analysis_content += f" 结合设备的技术参数和运行状态，"
                elif context.get('type') == 'fault_pattern':
                    analysis_content += f" 对照已知的故障模式特征，"

        analysis_content += "\n\n建议采用系统性的诊断方法，从现象分析到原因定位，再到解决方案的制定，确保故障处理的准确性和有效性。"

        return {
            "analysis": {
                "preliminary_diagnosis": "基于白银市真实数据的专业诊断",
                "equipment_type": identify_equipment_type_from_real_data(query, real_data_context),
                "fault_type": query,
                "detailed_analysis": analysis_content,
                "reasoning_process": None,
                "real_data_used": len(real_data_context),
                "data_sources": [item.get('source', 'unknown') for item in real_data_context[:5]],
                "ai_model": "Real_Data_Fallback",
                "thinking_mode": False,
                "output_format": "unstructured"
            }
        }

    except Exception as e:
        print(f"真实数据备用分析失败: {e}")
        return {
            "analysis": {
                "detailed_analysis": f"系统正在基于真实数据分析'{query}'，请稍后重试。",
                "ai_model": "Fallback",
                "output_format": "unstructured"
            }
        }

def parse_deepseek_response(ai_content: str, query: str):
    """解析DeepSeek的响应内容，提取结构化信息"""

    # 基础信息提取
    equipment_type = "unknown"
    if "变压器" in query:
        equipment_type = "transformer"
    elif "断路器" in query:
        equipment_type = "circuit_breaker"
    elif "隔离开关" in query:
        equipment_type = "isolator"
    elif "电缆" in query:
        equipment_type = "cable"

    # 从AI响应中提取关键信息
    possible_causes = []
    check_items = []
    recommendations = ""

    # 简单的文本解析（实际项目中可以使用更复杂的NLP技术）
    lines = ai_content.split('\n')
    current_section = ""

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 识别章节
        if "可能原因" in line or "原因分析" in line:
            current_section = "causes"
        elif "检查" in line or "排查" in line:
            current_section = "checks"
        elif "建议" in line or "措施" in line:
            current_section = "recommendations"
        elif line.startswith(('-', '•', '1.', '2.', '3.')):
            # 提取列表项
            clean_line = line.lstrip('-•123456789. ')
            if current_section == "causes" and len(possible_causes) < 5:
                possible_causes.append(clean_line)
            elif current_section == "checks" and len(check_items) < 5:
                check_items.append(clean_line)

    # 提取建议（取前200字符）
    if "建议" in ai_content:
        recommendations_start = ai_content.find("建议")
        if recommendations_start != -1:
            recommendations = ai_content[recommendations_start:recommendations_start+200]

    return {
        "preliminary_diagnosis": f"基于DeepSeek AI分析，{query}的故障特征已识别",
        "equipment_type": equipment_type,
        "fault_type": query,
        "possible_causes": possible_causes if possible_causes else [
            "需要进一步现场检查确定具体原因",
            "可能涉及设备老化或环境因素",
            "建议检查保护装置设定和运行参数"
        ],
        "check_items": check_items if check_items else [
            "检查设备外观和连接状态",
            "测量相关电气参数",
            "查看保护装置记录",
            "分析历史运行数据"
        ],
        "recommendations": recommendations if recommendations else "建议立即组织专业人员进行详细检查",
        "detailed_analysis": ai_content  # 保存完整的AI分析内容
    }

def get_fallback_analysis(query: str):
    """当DeepSeek API不可用时的专业备用分析"""

    equipment_type = "transformer" if "变压器" in query else "circuit_breaker" if "断路器" in query else "unknown"

    # 针对变压器差动保护的专业分析
    if "变压器" in query and "差动保护" in query:
        return {
            "success": True,
            "analysis": {
                "preliminary_diagnosis": "变压器差动保护动作故障分析",
                "equipment_type": "transformer",
                "fault_type": "差动保护动作",
                "detailed_analysis": """**变压器差动保护动作分析**

**1. 故障性质判断**
变压器差动保护是变压器的主保护，其动作表明变压器内部或差动保护范围内发生了严重故障。

**2. 可能的故障原因**
- 变压器内部故障（绕组短路、铁芯故障）
- 套管故障或闪络
- 引出线故障
- 差动保护误动（CT饱和、二次回路故障）

**3. 应急处理措施**
- 立即停运变压器，切断所有电源
- 检查变压器外观，观察是否有明显异常
- 检查油位、油色、温度等运行参数
- 检查差动保护装置和CT二次回路

**4. 详细检查项目**
- 绝缘电阻测试
- 变比测试
- 直流电阻测试
- 介质损耗测试
- 差动保护定值和CT变比核查""",
                "possible_causes": [
                    "变压器内部绕组短路",
                    "套管故障或闪络",
                    "引出线接地或短路",
                    "差动保护CT饱和",
                    "差动保护二次回路故障",
                    "保护装置误动"
                ],
                "check_items": [
                    "检查变压器外观和油位",
                    "测量绝缘电阻",
                    "检查差动保护装置状态",
                    "核查CT变比和极性",
                    "检查二次回路接线",
                    "查看保护动作报告"
                ],
                "recommendations": "**紧急处理**：立即停运变压器，确保安全；**详细检查**：进行全面电气试验；**专业分析**：请专业技术人员分析保护动作原因",
                "ai_model": "专业知识库",
                "note": "基于电力系统专业知识的变压器差动保护故障分析"
            }
        }

    # 通用设备故障分析
    return {
        "success": True,
        "analysis": {
            "preliminary_diagnosis": f"基于描述'{query}'，初步判断可能涉及电力设备故障",
            "equipment_type": equipment_type,
            "fault_type": query,
            "detailed_analysis": f"根据故障描述'{query}'，建议进行详细的现场检查和技术分析。",
            "possible_causes": [
                "设备老化导致的绝缘性能下降",
                "外部环境因素影响",
                "运行参数超出额定范围",
                "维护不当或检修质量问题"
            ],
            "check_items": [
                "检查设备外观是否有明显异常",
                "测量绝缘电阻和介质损耗",
                "检查保护装置设定值",
                "查看历史运行数据和维护记录"
            ],
            "recommendations": "建议立即停运设备进行详细检查，确保人员和设备安全",
            "ai_model": "基础知识库",
            "note": "DeepSeek API暂时不可用，使用专业知识库分析"
        }
    }

@app.route('/api/v1/ai-analysis', methods=['POST'])
@monitor_performance
def ai_analysis():
    """AI智能故障分析 - 简化版本"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        thinking_mode = data.get('thinking_mode', False)

        if not query:
            return jsonify({
                "success": False,
                "error": "请提供故障描述"
            }), 400

        print(f"收到AI分析请求: {query}, 思考模式: {thinking_mode}")

        # 调用简化的分析函数
        start_time = time.time()
        analysis_result = generate_report(query=query, thinking_mode=thinking_mode)
        analysis_time = time.time() - start_time

        # 提取分析文本内容
        if isinstance(analysis_result, dict):
            # 如果是字典，提取分析内容
            analysis_text = analysis_result.get('analysis') or analysis_result.get('detailed_analysis') or analysis_result.get('final_analysis', '')
        else:
            # 如果是字符串，直接使用
            analysis_text = str(analysis_result)

        return jsonify({
            "success": True,
            "analysis": analysis_text,
            "query": query,
            "thinking_mode": thinking_mode,
            "analysis_time": round(analysis_time, 2),
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        print(f"AI分析错误: {e}")
        return jsonify({
            "success": False,
            "error": f"AI分析服务出错: {str(e)}"
        }), 500

@app.route('/api/v1/analyze_stream', methods=['POST'])
def analyze_fault_stream():
    """流式故障分析API - 实时返回思考过程"""
    try:
        from flask import Response
        import json

        data = request.get_json()
        query = data.get('query', '').strip()
        thinking_mode = data.get('thinking_mode', False)

        if not query:
            return jsonify({"error": "查询内容不能为空"}), 400

        print(f"🌊 开始流式故障分析: {query[:50]}...")
        print(f"🧠 推理模式: {thinking_mode}")

        # 构建流式响应
        def generate_stream():
            try:
                # 1. 获取真实数据上下文
                if data_manager:
                    search_results = data_manager.search_relevant_data(query)
                    real_data_context = []
                    # 合并所有搜索结果
                    for category, items in search_results.items():
                        if isinstance(items, list):
                            for item in items:
                                # 安全处理不同类型的数据
                                if isinstance(item, dict):
                                    content = item.get('content', str(item))
                                    source = item.get('source', f'{category}_data')
                                elif isinstance(item, str):
                                    content = item
                                    source = f'{category}_data'
                                else:
                                    content = str(item)
                                    source = f'{category}_data'

                                real_data_context.append({
                                    'type': category,
                                    'content': content,
                                    'source': source
                                })
                else:
                    real_data_context = []
                print(f"📊 获取真实数据上下文: {len(real_data_context)} 条")

                # 2. 构建增强提示词
                if real_data_context:
                    enhanced_prompt = build_enhanced_prompt_with_real_data(query, real_data_context, thinking_mode)
                else:
                    # 如果没有搜索到相关数据，使用专业基础提示词
                    if thinking_mode:
                        # DeepSeek-R1专业电力系统故障诊断提示词 - 标准thinking格式
                        enhanced_prompt = f"""你是白银市电力系统资深故障诊断专家，拥有20年以上的变电站运维和故障分析经验。

**专业身份：**
- 国家电网白银供电公司高级工程师
- 电力系统故障分析技术专家
- 具备丰富的110kV/220kV变电站运维经验
- 熟悉西北地区电力设备运行特点

**重要输出格式要求：**
请严格按照以下格式输出，确保思考过程和最终分析清晰分离：

<thinking>
[请在此处展示您作为电力系统专家的完整思考过程，包括：
- 对故障现象的初步判断和分析思路
- 技术参数的评估和异常点识别
- 可能原因的逐一排查和权重评估
- 证据链条的逻辑推理过程
- 专业经验的应用和类比分析
- 从保护动作、设备状态、技术参数等多个角度的深入分析]
</thinking>

**基于上述思考的专业故障诊断报告：**

**故障前系统运行状态评估：** [分析故障发生前的系统运行方式、负荷分布、潮流情况]

**设备技术特性核实：** [确认故障设备的型号规格、技术参数、运行年限和维护历史]

**现场检查技术要点：** [详述外观检查的重点部位、关键电气参数的测量方法]

**保护动作机理分析：** [深入分析保护装置的动作逻辑和时序关系]

**故障物理特征识别：** [详细描述故障部件的损坏情况、物理特征和损坏模式]

**根因综合分析：** [基于多维度信息进行深层次的根因分析]

**技术处理方案：** [制定具体的设备修复或更换方案，提出运行方式调整建议]

**故障情况：**
{query}

请运用您的专业知识和丰富经验，进行深度的故障诊断分析。"""
                    else:
                        enhanced_prompt = f"""你是白银市电力系统故障诊断专家，请提供专业的自然语言分析。

**重要要求：必须用连贯的自然语言表达，避免使用编号列表格式**

**专业身份：**
电力系统高级工程师，专业从事变电站设备故障诊断，熟悉110kV/220kV电力设备运行特性，具备丰富的现场故障处理经验。

**故障情况：**
{query}

**分析要求：**
请用连贯的自然语言进行专业分析，就像电力工程师在现场分析时的表达方式。分析应包含故障性质判断、技术原因分析、处理建议和预防措施，使用准确的技术术语，保持表述的专业性和流畅性。

**输出格式：**
采用段落式的自然语言表达，每个技术要点之间用自然的过渡语句连接，形成完整连贯的专业分析报告。"""

                # 3. 调用DeepSeek API（流式）
                print(f"🔍 DeepSeek客户端状态: {deepseek_client is not None}")
                if deepseek_client:
                    print(f"🔍 DeepSeek API配置: {deepseek_client.base_url}")
                    print(f"🔍 API密钥: {DEEPSEEK_API_KEY[:10]}...")

                    messages = [
                        {"role": "system", "content": "你是白银市电力系统故障诊断专家。"},
                        {"role": "user", "content": enhanced_prompt}
                    ]

                    model = DEEPSEEK_R1_MODEL if thinking_mode else DEEPSEEK_CHAT_MODEL
                    print(f"🤖 流式API调用模型: {model} (thinking_mode: {thinking_mode})")

                    # 验证模型名称
                    if thinking_mode and not model:
                        yield f"data: {json.dumps({'type': 'error', 'message': 'DeepSeek-R1模型名称未配置'}, ensure_ascii=False)}\n\n"
                        return

                    # 直接调用流式API
                    url = f"{deepseek_client.base_url}/chat/completions"
                    payload = {
                        "model": model,
                        "messages": messages,
                        "temperature": 0.3 if thinking_mode else 0.7,
                        "max_tokens": 8000 if thinking_mode else 2000,
                        "stream": True
                    }
                    print(f"🌊 流式请求URL: {url}")
                    print(f"🌊 流式请求payload: {payload}")

                    response = requests.post(url, headers=deepseek_client.headers,
                                           json=payload, stream=True, timeout=180)

                    print(f"🌊 流式响应状态码: {response.status_code}")
                    if response.status_code != 200:
                        print(f"❌ 流式响应错误: {response.text}")
                        yield f"data: {json.dumps({'type': 'error', 'message': f'API调用失败: {response.status_code} - {response.text}'}, ensure_ascii=False)}\n\n"
                        return

                    if response.status_code == 200:
                        full_content = ""
                        reasoning_content = ""
                        final_content = ""
                        content_buffer = ""
                        has_sent_first_chunk = False  # 确保第一个chunk被发送

                        print(f"🌊 开始处理流式响应...")

                        for line in response.iter_lines():
                            if line:
                                line = line.decode('utf-8')
                                if line.startswith('data: '):
                                    data_str = line[6:]

                                    if data_str.strip() == '[DONE]':
                                        # 阿里云DashScope的DeepSeek-R1特殊处理
                                        if thinking_mode and reasoning_content and not final_content:
                                            # 如果只有推理内容，需要智能分离
                                            print(f"🔄 阿里云DeepSeek-R1格式，开始智能分离...")
                                            reasoning_part, final_part = smart_split_reasoning_and_result(reasoning_content)

                                            # 发送分离后的最终结果
                                            if final_part:
                                                final_content = final_part
                                                yield f"data: {json.dumps({'type': 'final', 'content': final_part}, ensure_ascii=False)}\n\n"
                                                print(f"📋 智能分离的最终结果: {len(final_part)} 字符")

                                        # 发送完成信号
                                        print(f"🏁 流式完成 - 推理: {len(reasoning_content)} 字符, 最终: {len(final_content)} 字符")
                                        yield f"data: {json.dumps({'type': 'complete', 'reasoning': reasoning_content, 'final': final_content}, ensure_ascii=False)}\n\n"
                                        break

                                    try:
                                        chunk_data = json.loads(data_str)
                                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                            delta = chunk_data['choices'][0].get('delta', {})

                                            # 处理DeepSeek-R1的reasoning_content字段（官方标准格式）
                                            if 'reasoning_content' in delta and delta['reasoning_content']:
                                                reasoning_chunk = delta['reasoning_content']
                                                reasoning_content += reasoning_chunk
                                                has_sent_first_chunk = True

                                                # 立即发送推理chunk
                                                chunk_response = f"data: {json.dumps({'type': 'reasoning', 'content': reasoning_chunk}, ensure_ascii=False)}\n\n"
                                                print(f"🧠 发送推理chunk: {len(reasoning_chunk)} 字符 (总计:{len(reasoning_content)})")
                                                yield chunk_response

                                            # 处理DeepSeek-R1的content字段（最终答案）
                                            if 'content' in delta and delta['content'] is not None:
                                                content_chunk = delta['content']
                                                full_content += content_chunk
                                                content_buffer += content_chunk
                                                has_sent_first_chunk = True

                                                if thinking_mode:
                                                    # DeepSeek-R1模式：content字段是最终答案，直接作为final输出
                                                    final_content += content_chunk
                                                    chunk_response = f"data: {json.dumps({'type': 'final', 'content': content_chunk}, ensure_ascii=False)}\n\n"
                                                    print(f"📋 最终答案: {len(content_chunk)} 字符 (总计:{len(final_content)})")
                                                    yield chunk_response
                                                else:
                                                    # DeepSeek-V3模式：直接流式输出最终结果
                                                    chunk_response = f"data: {json.dumps({'type': 'final', 'content': content_chunk}, ensure_ascii=False)}\n\n"
                                                    print(f"📋 V3流式输出: {len(content_chunk)} 字符")
                                                    yield chunk_response

                                    except json.JSONDecodeError as e:
                                        print(f"❌ JSON解析错误: {e}")
                                        continue
                                    except Exception as e:
                                        print(f"❌ 处理chunk错误: {e}")
                                        continue

                        # 如果没有发送任何内容，发送错误信息
                        if not has_sent_first_chunk:
                            print("❌ 没有收到任何有效的流式数据")
                            yield f"data: {json.dumps({'type': 'error', 'message': '没有收到有效的响应数据'}, ensure_ascii=False)}\n\n"
                    else:
                        yield f"data: {json.dumps({'type': 'error', 'message': '流式API调用失败'}, ensure_ascii=False)}\n\n"
                else:
                    print("❌ DeepSeek客户端未初始化，提供基础分析")
                    # 提供基础分析响应
                    basic_analysis = f"""
基于查询内容进行基础分析：

故障描述：{query}

由于DeepSeek API暂时不可用，这里提供基础的故障分析框架：

1. 故障现象分析
2. 可能原因排查
3. 处理建议
4. 预防措施

请检查DeepSeek API配置或稍后重试。
"""
                    # 模拟流式响应
                    if thinking_mode:
                        yield f"data: {json.dumps({'type': 'reasoning', 'content': '正在进行基础故障分析...'}, ensure_ascii=False)}\n\n"
                        yield f"data: {json.dumps({'type': 'final', 'content': basic_analysis}, ensure_ascii=False)}\n\n"
                    else:
                        yield f"data: {json.dumps({'type': 'final', 'content': basic_analysis}, ensure_ascii=False)}\n\n"
                    yield f"data: {json.dumps({'type': 'complete'}, ensure_ascii=False)}\n\n"

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"❌ 流式分析失败: {e}")
                print(f"❌ 详细错误信息: {error_details}")
                yield f"data: {json.dumps({'type': 'error', 'message': f'{str(e)} - 详细信息请查看服务器日志'}, ensure_ascii=False)}\n\n"

        return Response(generate_stream(), mimetype='text/event-stream',
                       headers={'Cache-Control': 'no-cache',
                               'Connection': 'keep-alive',
                               'Access-Control-Allow-Origin': '*'})

    except Exception as e:
        print(f"❌ 流式分析请求处理失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/v1/equipment', methods=['GET'])
def get_equipment_list():
    """获取设备列表"""
    try:
        # 获取查询参数
        search_query = request.args.get('search', '')
        equipment_type = request.args.get('type', '')
        status_filter = request.args.get('status', '')
        location_filter = request.args.get('location', '')

        equipment_data = data_manager.get_equipment_data()
        equipment_list = []

        # 扩展设备类别，包含更多设备类型
        equipment_categories = [
            'transformers', 'circuit_breakers', 'disconnectors',
            'switches', 'capacitors', 'arresters', 'cables',
            'busbars', 'reactors', 'generators', 'motors',
            'protection_devices', 'measurement_devices'
        ]

        # 提取所有设备
        for category in equipment_categories:
            if category in equipment_data:
                for equipment in equipment_data[category]:
                    # 获取设备基本信息
                    equipment_info = {
                        'id': equipment.get('equipment_id', equipment.get('id', '')),
                        'name': equipment.get('name', equipment.get('equipment_name', '')),
                        'type': equipment.get('type', equipment.get('equipment_type', category.rstrip('s'))),
                        'category': category,
                        'manufacturer': equipment.get('manufacturer', ''),
                        'model': equipment.get('model', ''),
                        'serial_number': equipment.get('serial_number', ''),
                        'installation_date': equipment.get('installation_date', ''),
                        'last_maintenance': equipment.get('last_maintenance', ''),
                        'next_maintenance': equipment.get('next_maintenance', ''),
                        'warranty_expiry': equipment.get('warranty_expiry', ''),
                        'created_at': equipment.get('created_at', ''),
                        'updated_at': equipment.get('updated_at', datetime.now().isoformat())
                    }

                    # 获取状态信息
                    current_status = equipment.get('current_status', {})
                    equipment_info.update({
                        'status': current_status.get('operational_status', 'unknown'),
                        'health': current_status.get('health_status', 'unknown'),
                        'temperature': current_status.get('temperature', 0),
                        'voltage': current_status.get('voltage', 0),
                        'current': current_status.get('current', 0),
                        'power': current_status.get('power', 0),
                        'last_status_update': current_status.get('timestamp', '')
                    })

                    # 获取位置信息
                    installation_info = equipment.get('installation_info', {})
                    equipment_info.update({
                        'location': installation_info.get('location', ''),
                        'substation': installation_info.get('substation', ''),
                        'bay': installation_info.get('bay', ''),
                        'voltage_level': installation_info.get('voltage_level', ''),
                        'coordinates': installation_info.get('coordinates', {})
                    })

                    # 获取技术参数
                    technical_params = equipment.get('technical_parameters', {})
                    equipment_info.update({
                        'rated_voltage': technical_params.get('rated_voltage', 0),
                        'rated_current': technical_params.get('rated_current', 0),
                        'rated_power': technical_params.get('rated_power', 0),
                        'frequency': technical_params.get('frequency', 50)
                    })

                    equipment_list.append(equipment_info)

        # 应用过滤条件
        filtered_list = equipment_list

        # 搜索过滤
        if search_query:
            search_query = search_query.lower()
            filtered_list = [eq for eq in filtered_list if
                           search_query in eq.get('name', '').lower() or
                           search_query in eq.get('id', '').lower() or
                           search_query in eq.get('type', '').lower() or
                           search_query in eq.get('location', '').lower() or
                           search_query in eq.get('manufacturer', '').lower()]

        # 类型过滤
        if equipment_type:
            filtered_list = [eq for eq in filtered_list if eq.get('type', '').lower() == equipment_type.lower()]

        # 状态过滤
        if status_filter:
            filtered_list = [eq for eq in filtered_list if eq.get('status', '').lower() == status_filter.lower()]

        # 位置过滤
        if location_filter:
            location_filter = location_filter.lower()
            filtered_list = [eq for eq in filtered_list if
                           location_filter in eq.get('location', '').lower() or
                           location_filter in eq.get('substation', '').lower()]

        # 按更新时间排序（最新的在前）
        filtered_list.sort(key=lambda x: x.get('updated_at', ''), reverse=True)

        # 计算统计信息
        total_count = len(equipment_list)
        filtered_count = len(filtered_list)

        # 状态统计
        status_stats = {}
        type_stats = {}
        health_stats = {}

        for eq in equipment_list:
            # 状态统计
            status = eq.get('status', 'unknown')
            status_stats[status] = status_stats.get(status, 0) + 1

            # 类型统计
            eq_type = eq.get('type', 'unknown')
            type_stats[eq_type] = type_stats.get(eq_type, 0) + 1

            # 健康状态统计
            health = eq.get('health', 'unknown')
            health_stats[health] = health_stats.get(health, 0) + 1

        return jsonify({
            'success': True,
            'equipment': filtered_list,
            'total': total_count,
            'filtered_total': filtered_count,
            'statistics': {
                'status': status_stats,
                'type': type_stats,
                'health': health_stats
            },
            'filters': {
                'search': search_query,
                'type': equipment_type,
                'status': status_filter,
                'location': location_filter
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/equipment/<equipment_id>', methods=['GET'])
def get_equipment_detail(equipment_id):
    """获取设备详细信息"""
    try:
        equipment_data = data_manager.get_equipment_data()
        equipment_info = None

        # 在所有类别中查找设备
        equipment_categories = [
            'transformers', 'circuit_breakers', 'disconnectors',
            'switches', 'capacitors', 'arresters', 'cables',
            'busbars', 'reactors', 'generators', 'motors',
            'protection_devices', 'measurement_devices'
        ]

        for category in equipment_categories:
            if category in equipment_data:
                for equipment in equipment_data[category]:
                    if (equipment.get('equipment_id') == equipment_id or
                        equipment.get('id') == equipment_id):
                        equipment_info = equipment.copy()
                        equipment_info['category'] = category

                        # 标准化状态字段，便于前端使用
                        if 'current_status' in equipment_info and 'operational_status' in equipment_info['current_status']:
                            equipment_info['status'] = equipment_info['current_status']['operational_status']

                        # 标准化位置字段
                        if 'installation_info' in equipment_info and 'location' in equipment_info['installation_info']:
                            equipment_info['location'] = equipment_info['installation_info']['location']

                        break
                if equipment_info:
                    break

        if not equipment_info:
            return jsonify({'success': False, 'error': '设备不存在'}), 404

        # 获取设备历史记录（如果有的话）
        history_records = []
        # 这里可以添加从历史记录数据库获取记录的逻辑

        return jsonify({
            'success': True,
            'equipment': equipment_info,
            'history': history_records
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/equipment', methods=['POST'])
def add_equipment():
    """添加新设备"""
    try:
        equipment_data = request.get_json()

        if not equipment_data:
            return jsonify({'success': False, 'error': '无效的设备数据'}), 400

        # 验证必需字段
        required_fields = ['name', 'type', 'location', 'status']
        for field in required_fields:
            if not equipment_data.get(field) or equipment_data.get(field).strip() == '':
                return jsonify({'success': False, 'error': f'缺少必需字段: {field}'}), 400

        # 生成设备ID
        equipment_id = equipment_data.get('id') or f"EQ_{datetime.now().strftime('%Y%m%d%H%M%S')}_{str(uuid.uuid4())[:8]}"

        # 构建完整的设备信息
        new_equipment = {
            'id': equipment_id,
            'equipment_id': equipment_id,
            'name': equipment_data['name'],
            'type': equipment_data['type'],
            'model': equipment_data.get('model', ''),
            'manufacturer': equipment_data.get('manufacturer', ''),
            'serial_number': equipment_data.get('serial_number', ''),
            'installation_date': equipment_data.get('installation_date', ''),
            'last_maintenance': equipment_data.get('last_maintenance', ''),
            'next_maintenance': equipment_data.get('next_maintenance', ''),
            'warranty_expiry': equipment_data.get('warranty_expiry', ''),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'current_status': {
                'operational_status': equipment_data['status'],
                'health_status': 'good',
                'timestamp': datetime.now().isoformat()
            },
            'installation_info': {
                'location': equipment_data['location'],
                'substation': equipment_data.get('substation', ''),
                'bay': equipment_data.get('bay', ''),
                'voltage_level': equipment_data.get('voltage_level', ''),
                'coordinates': equipment_data.get('coordinates', {})
            },
            'technical_parameters': {
                'rated_voltage': equipment_data.get('rated_voltage', 0),
                'rated_current': equipment_data.get('rated_current', 0),
                'rated_power': equipment_data.get('rated_power', 0),
                'frequency': equipment_data.get('frequency', 50)
            },
            'status_history': [{
                'old_status': 'new',
                'new_status': equipment_data['status'],
                'timestamp': datetime.now().isoformat(),
                'notes': '设备初始添加'
            }]
        }

        # 根据设备类型确定存储类别
        type_mapping = {
            'transformer': 'transformers',
            'breaker': 'circuit_breakers',
            'switch': 'switches',
            'capacitor': 'capacitors',
            'arrester': 'arresters',
            'cable': 'cables',
            'busbar': 'busbars',
            'reactor': 'reactors',
            'generator': 'generators',
            'motor': 'motors',
            'protection': 'protection_devices',
            'measurement': 'measurement_devices'
        }

        category = type_mapping.get(equipment_data['type'], 'switches')

        # 获取当前设备数据
        current_equipment_data = data_manager.get_equipment_data()

        # 确保类别存在
        if category not in current_equipment_data:
            current_equipment_data[category] = []

        # 检查设备ID是否已存在
        for existing_equipment in current_equipment_data[category]:
            if existing_equipment.get('equipment_id') == equipment_id or existing_equipment.get('id') == equipment_id:
                return jsonify({'success': False, 'error': '设备ID已存在'}), 400

        # 添加新设备
        current_equipment_data[category].append(new_equipment)

        # 保存数据到文件
        save_result = data_manager.save_equipment_data(current_equipment_data)

        if not save_result:
            return jsonify({'success': False, 'error': '设备数据保存失败'}), 500


        # 构建安全的响应数据
        try:
            response_data = {
                'success': True,
                'message': '设备添加成功',
                'equipment_id': str(equipment_id)
            }

            return jsonify(response_data)

        except Exception:
            # 返回最简单的成功响应
            return jsonify({'success': True, 'message': '设备添加成功'})

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/equipment/<equipment_id>', methods=['PUT'])
def update_equipment(equipment_id):
    """更新设备信息"""
    try:
        update_data = request.get_json()

        if not update_data:
            return jsonify({'success': False, 'error': '无效的更新数据'}), 400

        equipment_data = data_manager.get_equipment_data()
        equipment_found = False

        # 在所有类别中查找并更新设备
        equipment_categories = [
            'transformers', 'circuit_breakers', 'disconnectors',
            'switches', 'capacitors', 'arresters', 'cables',
            'busbars', 'reactors', 'generators', 'motors',
            'protection_devices', 'measurement_devices'
        ]

        for category in equipment_categories:
            if category in equipment_data:
                print(f"🔍 检查类别 {category}: {len(equipment_data[category])} 个设备")
                for i, equipment in enumerate(equipment_data[category]):
                    current_id = equipment.get('equipment_id') or equipment.get('id')
                    print(f"  - 设备 {i}: ID={current_id}, 名称={equipment.get('name', 'N/A')}")

                    if (equipment.get('equipment_id') == equipment_id or
                        equipment.get('id') == equipment_id):

                        # 更新设备信息 - 处理嵌套状态结构
                        for key, value in update_data.items():
                            if key == 'status':
                                # 更新嵌套的状态字段
                                if 'current_status' not in equipment_data[category][i]:
                                    equipment_data[category][i]['current_status'] = {}
                                equipment_data[category][i]['current_status']['operational_status'] = value
                            elif key == 'location':
                                # 更新位置信息
                                if 'installation_info' not in equipment_data[category][i]:
                                    equipment_data[category][i]['installation_info'] = {}
                                equipment_data[category][i]['installation_info']['location'] = value
                            else:
                                # 直接更新其他字段
                                equipment_data[category][i][key] = value

                        equipment_data[category][i]['updated_at'] = datetime.now().isoformat()
                        equipment_found = True
                        break
                if equipment_found:
                    break

        if not equipment_found:
            return jsonify({'success': False, 'error': '设备不存在'}), 404

        # 保存更新后的数据
        try:
            save_result = data_manager.save_equipment_data(equipment_data)

            if not save_result:
                return jsonify({'success': False, 'error': '设备数据保存失败'}), 500


            # 构建成功响应 - 简化响应避免序列化问题
            try:
                response_data = {
                    'success': True,
                    'message': '设备信息更新成功',
                    'equipment_id': str(equipment_id),  # 确保是字符串
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # 使用字符串格式
                }
                return jsonify(response_data)

            except Exception:
                traceback.print_exc()
                # 返回最简单的成功响应
                return jsonify({'success': True, 'message': '设备更新成功'})

        except Exception as save_error:
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': f'保存失败: {str(save_error)}'}), 500

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/equipment/<equipment_id>/status', methods=['PUT'])
def update_equipment_status(equipment_id):
    """更新设备状态"""
    try:
        status_data = request.get_json()
        if not status_data:
            return jsonify({'success': False, 'error': '无效的状态数据'}), 400

        new_status = status_data.get('status')
        notes = status_data.get('notes', '')

        if not new_status:
            return jsonify({'success': False, 'error': '状态不能为空'}), 400

        equipment_data = data_manager.get_equipment_data()
        equipment_found = False

        # 在所有类别中查找并更新设备状态
        equipment_categories = [
            'transformers', 'circuit_breakers', 'disconnectors',
            'switches', 'capacitors', 'arresters', 'cables',
            'busbars', 'reactors', 'generators', 'motors',
            'protection_devices', 'measurement_devices'
        ]

        for category in equipment_categories:
            if category in equipment_data:
                for i, equipment in enumerate(equipment_data[category]):
                    if (equipment.get('equipment_id') == equipment_id or
                        equipment.get('id') == equipment_id):

                        # 记录旧状态
                        old_status = equipment.get('current_status', {}).get('operational_status', 'unknown')

                        # 更新状态
                        if 'current_status' not in equipment_data[category][i]:
                            equipment_data[category][i]['current_status'] = {}

                        equipment_data[category][i]['current_status']['operational_status'] = new_status
                        equipment_data[category][i]['current_status']['timestamp'] = datetime.now().isoformat()
                        equipment_data[category][i]['updated_at'] = datetime.now().isoformat()

                        # 添加状态变更记录
                        if 'status_history' not in equipment_data[category][i]:
                            equipment_data[category][i]['status_history'] = []

                        equipment_data[category][i]['status_history'].append({
                            'old_status': old_status,
                            'new_status': new_status,
                            'timestamp': datetime.now().isoformat(),
                            'notes': notes
                        })

                        equipment_found = True
                        break
                if equipment_found:
                    break

        if not equipment_found:
            return jsonify({'success': False, 'error': '设备不存在'}), 404

        # 保存更新后的数据
        data_manager.save_equipment_data(equipment_data)

        return jsonify({
            'success': True,
            'message': '设备状态更新成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/equipment/<equipment_id>', methods=['DELETE'])
def delete_equipment(equipment_id):
    """删除设备"""
    try:
        equipment_data = data_manager.get_equipment_data()
        equipment_found = False

        # 在所有类别中查找并删除设备
        equipment_categories = [
            'transformers', 'circuit_breakers', 'disconnectors',
            'switches', 'capacitors', 'arresters', 'cables',
            'busbars', 'reactors', 'generators', 'motors',
            'protection_devices', 'measurement_devices'
        ]

        for category in equipment_categories:
            if category in equipment_data:
                for i, equipment in enumerate(equipment_data[category]):
                    if (equipment.get('equipment_id') == equipment_id or
                        equipment.get('id') == equipment_id):

                        # 删除设备
                        equipment_data[category].pop(i)
                        equipment_found = True
                        break
                if equipment_found:
                    break

        if not equipment_found:
            return jsonify({'success': False, 'error': '设备不存在'}), 404

        # 保存更新后的数据
        data_manager.save_equipment_data(equipment_data)

        return jsonify({
            'success': True,
            'message': '设备删除成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/fault/history', methods=['GET'])
def get_fault_history():
    """获取故障历史记录"""
    try:
        # 从案例研究中获取故障历史
        case_studies = data_manager.get_case_studies()
        fault_history = []

        for case in case_studies:
            if isinstance(case, dict):
                fault_record = {
                    'id': case.get('id', f"fault_{len(fault_history) + 1}"),
                    'equipment_type': case.get('equipment_type', '未知设备'),
                    'fault_type': case.get('fault_type', '未知故障'),
                    'description': case.get('description', case.get('title', '无描述')),
                    'date': case.get('date', case.get('timestamp', '未知时间')),
                    'status': case.get('status', 'resolved'),
                    'severity': case.get('severity', 'medium')
                }
                fault_history.append(fault_record)

        return jsonify({
            'success': True,
            'data': fault_history,
            'total': len(fault_history)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/v1/fault-analysis/comprehensive', methods=['POST'])
def comprehensive_fault_analysis():
    """综合故障分析接口 - 基于真实数据"""
    try:
        request_data = request.get_json()
        
        # 获取请求参数
        equipment_type = request_data.get('equipment_type', '')
        equipment_number = request_data.get('equipment_number', '')
        fault_description = request_data.get('fault_description', '')
        
        # 获取相关设备信息
        equipment_data = data_manager.get_equipment_data()
        target_equipment = None
        
        # 查找目标设备
        for category in ['transformers', 'circuit_breakers', 'disconnectors']:
            if category in equipment_data:
                for equipment in equipment_data[category]:
                    if equipment.get('equipment_id') == equipment_number:
                        target_equipment = equipment
                        break
        
        # 搜索相似故障
        similar_faults = data_manager.search_similar_faults(equipment_type, fault_description)
        
        # 获取案例研究
        case_studies = data_manager.get_case_studies()
        
        # 生成分析结果
        analysis_result = {
            "equipment_info": target_equipment or {
                "equipment_id": equipment_number,
                "name": f"设备 {equipment_number}",
                "type": equipment_type
            },
            "fault_analysis": {
                "similar_patterns": similar_faults,
                "case_studies": list(case_studies.keys())[:3],
                "recommendations": []
            },
            "analysis_summary": f"基于设备 {equipment_number} 的故障分析已完成"
        }
        
        # 根据相似故障生成建议
        if similar_faults:
            for fault in similar_faults[:2]:
                analysis_result["fault_analysis"]["recommendations"].extend(
                    fault.get('treatment_methods', [])[:3]
                )
        
        return jsonify({
            "status": "success",
            "analysis_id": f"REAL-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "equipment_info": analysis_result["equipment_info"],
            "fault_analysis": analysis_result["fault_analysis"],
            "summary": analysis_result["analysis_summary"],
            "timestamp": datetime.now().isoformat(),
            "data_source": "real_project_data"
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/v1/knowledge/search', methods=['POST'])
@monitor_performance
def search_knowledge():
    """知识库搜索 - 增强版本，支持向量搜索和上传文件搜索"""
    try:
        request_data = request.get_json()
        query = request_data.get('query', '')
        limit = request_data.get('limit', 10)
        search_type = request_data.get('search_type', 'multimodal')  # text, image, multimodal

        print(f"🔍 知识库搜索请求: '{query}', 类型: {search_type}, 限制: {limit}")

        results = []

        # 1. 使用完整知识库系统搜索（如果可用）
        if knowledge_base_instance:
            try:
                print("📚 使用完整知识库系统搜索...")
                kb_results = knowledge_base_instance.search(query, search_type, limit)

                if kb_results.get('results'):
                    kb_search_results = kb_results['results']

                    # 处理不同类型的搜索结果
                    if isinstance(kb_search_results, dict):
                        # 多模态搜索结果
                        if 'text' in kb_search_results:
                            for doc in kb_search_results['text'][:limit//2]:
                                results.append({
                                    'type': 'document',
                                    'title': doc.get('title', '文档'),
                                    'content': doc.get('content', '')[:300] + '...',
                                    'source': doc.get('source', ''),
                                    'score': doc.get('score', 0.0),
                                    'metadata': doc.get('metadata', {})
                                })

                        if 'images' in kb_search_results:
                            for img in kb_search_results['images'][:limit//2]:
                                results.append({
                                    'type': 'image',
                                    'title': img.get('title', '图像'),
                                    'content': img.get('description', ''),
                                    'source': img.get('path', ''),
                                    'score': img.get('score', 0.0),
                                    'metadata': img.get('metadata', {})
                                })

                    elif isinstance(kb_search_results, list):
                        # 文本搜索结果
                        for doc in kb_search_results[:limit]:
                            results.append({
                                'type': 'document',
                                'title': doc.get('title', '文档'),
                                'content': doc.get('content', '')[:300] + '...',
                                'source': doc.get('source', ''),
                                'score': doc.get('score', 0.0),
                                'metadata': doc.get('metadata', {})
                            })


            except Exception as kb_error:
                print(f"❌ 知识库搜索失败: {kb_error}")

        # 2. 搜索上传的文件（在uploads目录中）
        try:
            uploads_dir = app.config.get('UPLOAD_FOLDER', os.path.join(os.path.dirname(__file__), '..', 'uploads'))

            if os.path.exists(uploads_dir):
                for filename in os.listdir(uploads_dir):
                    file_path = os.path.join(uploads_dir, filename)
                    if os.path.isfile(file_path):
                        # 检查文件名是否包含查询关键词
                        if query.lower() in filename.lower():
                            results.append({
                                'type': 'uploaded_file',
                                'title': filename,
                                'content': f'上传的文件: {filename}',
                                'source': f'uploads/{filename}',
                                'score': 0.8,
                                'metadata': {
                                    'file_size': os.path.getsize(file_path),
                                    'upload_time': datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                                }
                            })

                        # 对于文本文件，搜索内容
                        if filename.endswith(('.txt', '.md', '.json', '.csv')):
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                    if query.lower() in content.lower():
                                        results.append({
                                            'type': 'uploaded_file_content',
                                            'title': f'{filename} (内容匹配)',
                                            'content': content[:300] + '...',
                                            'source': f'uploads/{filename}',
                                            'score': 0.9,
                                            'metadata': {
                                                'file_size': len(content),
                                                'upload_time': datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                                            }
                                        })
                            except Exception as file_error:
                                print(f"读取文件失败 {filename}: {file_error}")


        except Exception as upload_error:
            print(f"❌ 上传文件搜索失败: {upload_error}")

        # 3. 回退到基础搜索（搜索预设的案例研究和故障模式）
        if len(results) < limit:
            print("📖 执行基础知识库搜索...")

            # 搜索案例研究
            case_studies = data_manager.get_case_studies()
            for case_name, content in case_studies.items():
                if query.lower() in content.lower():
                    results.append({
                        'type': 'case_study',
                        'title': case_name.replace('_', ' ').title(),
                        'content': content[:200] + '...',
                        'source': f'case_studies/{case_name}',
                        'score': 0.7,
                        'metadata': {'source_type': 'builtin'}
                    })

            # 搜索故障模式
            fault_patterns = data_manager.get_fault_patterns()
            for category in ['transformer_faults', 'breaker_faults']:
                if category in fault_patterns:
                    for fault in fault_patterns[category]:
                        if query.lower() in fault.get('fault_name', '').lower():
                            results.append({
                                'type': 'fault_pattern',
                                'title': fault.get('fault_name', ''),
                                'content': f"严重程度: {fault.get('severity', '')}, 症状: {', '.join(fault.get('symptoms', [])[:3])}",
                                'source': f'fault_patterns/{fault.get("fault_id", "")}',
                                'score': 0.6,
                                'metadata': {'source_type': 'builtin'}
                            })

        # 按相关性分数排序并限制结果数量
        results.sort(key=lambda x: x.get('score', 0), reverse=True)
        final_results = results[:limit]

        print(f"🎯 最终搜索结果: {len(final_results)} 个")

        return jsonify({
            'success': True,
            'results': final_results,
            'total': len(final_results),
            'query': query,
            'search_type': search_type,
            'knowledge_base_available': knowledge_base_instance is not None
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/v1/knowledge/document/<path:doc_id>', methods=['GET'])
def get_knowledge_document(doc_id):
    """获取知识库文档的完整内容"""
    try:
        print(f"🔍 获取文档详情: {doc_id}")

        # 1. 尝试从知识库系统获取
        if knowledge_base_instance:
            try:
                doc_info = knowledge_base_instance.get_document(doc_id)
                if doc_info:
                    return jsonify({
                        'success': True,
                        'document': doc_info,
                        'source': 'knowledge_base'
                    })
            except Exception as kb_error:
                print(f"❌ 知识库获取文档失败: {kb_error}")

        # 2. 尝试从上传文件获取
        if doc_id.startswith('uploads/'):
            file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), doc_id)
            if os.path.exists(file_path):
                try:
                    file_stats = os.stat(file_path)
                    file_ext = os.path.splitext(file_path)[1].lower()

                    # 读取文件内容
                    content = ""
                    if file_ext in ['.txt', '.md', '.json', '.csv']:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                    elif file_ext == '.pdf':
                        content = "PDF文件内容需要专门的解析器"
                    elif file_ext in ['.doc', '.docx']:
                        content = "Word文档内容需要专门的解析器"
                    else:
                        content = "不支持的文件类型"

                    return jsonify({
                        'success': True,
                        'document': {
                            'type': 'uploaded_file',
                            'title': os.path.basename(file_path),
                            'content': content,
                            'metadata': {
                                'file_path': file_path,
                                'file_size': file_stats.st_size,
                                'file_type': file_ext,
                                'created_time': datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                                'modified_time': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                                'readable_size': f"{file_stats.st_size / 1024:.1f} KB" if file_stats.st_size > 1024 else f"{file_stats.st_size} bytes"
                            }
                        },
                        'source': 'uploaded_file'
                    })

                except Exception as file_error:
                    print(f"❌ 读取上传文件失败: {file_error}")

        # 3. 尝试从预设数据获取
        if doc_id.startswith('case_studies/'):
            case_name = doc_id.replace('case_studies/', '')
            case_studies = data_manager.get_case_studies()
            if case_name in case_studies:
                return jsonify({
                    'success': True,
                    'document': {
                        'type': 'case_study',
                        'title': case_name.replace('_', ' ').title(),
                        'content': case_studies[case_name],
                        'metadata': {
                            'source_type': '内置案例研究',
                            'category': '故障案例',
                            'data_source': '项目知识库'
                        }
                    },
                    'source': 'builtin_data'
                })

        elif doc_id.startswith('fault_patterns/'):
            fault_id = doc_id.replace('fault_patterns/', '')
            fault_patterns = data_manager.get_fault_patterns()

            # 搜索故障模式
            for category in ['transformer_faults', 'breaker_faults']:
                if category in fault_patterns:
                    for fault in fault_patterns[category]:
                        if fault.get('fault_id') == fault_id:
                            return jsonify({
                                'success': True,
                                'document': {
                                    'type': 'fault_pattern',
                                    'title': fault.get('fault_name', ''),
                                    'content': f"""故障名称: {fault.get('fault_name', '')}
故障类型: {category.replace('_', ' ').title()}
严重程度: {fault.get('severity', '')}

症状描述:
{chr(10).join(f"• {symptom}" for symptom in fault.get('symptoms', []))}

可能原因:
{chr(10).join(f"• {cause}" for cause in fault.get('causes', []))}

处理建议:
{chr(10).join(f"• {action}" for action in fault.get('actions', []))}""",
                                    'metadata': {
                                        'fault_id': fault.get('fault_id', ''),
                                        'category': category,
                                        'severity': fault.get('severity', ''),
                                        'symptoms_count': len(fault.get('symptoms', [])),
                                        'causes_count': len(fault.get('causes', [])),
                                        'actions_count': len(fault.get('actions', []))
                                    }
                                },
                                'source': 'builtin_data'
                            })

        # 未找到文档
        return jsonify({
            'success': False,
            'error': '未找到指定的文档',
            'doc_id': doc_id
        }), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/v1/upload/image', methods=['POST'])
def upload_image():
    """图片上传接口"""
    try:

        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有找到文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 验证文件类型
        allowed_types = {'image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff', 'image/webp'}
        if file.content_type not in allowed_types:
            return jsonify({
                'success': False,
                'error': f'不支持的文件类型: {file.content_type}。支持的类型: {", ".join(allowed_types)}'
            }), 400

        print(f"📏 文件大小: {len(file.read())} 字节")
        file.seek(0)  # 重置文件指针

        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = secure_filename(file.filename)
        unique_filename = f"{timestamp}_{safe_filename}"

        # 确保上传目录存在
        upload_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads', 'images')
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 获取文件信息
        file_size = os.path.getsize(file_path)

        # 构建相对路径用于数据库存储
        relative_path = f"uploads/images/{unique_filename}"

        # 获取表单数据
        title = request.form.get('title', safe_filename)
        category = request.form.get('category', 'photo')
        description = request.form.get('description', '')
        equipment = request.form.get('equipment', '')
        location = request.form.get('location', '')
        tags = request.form.get('tags', '')
        auto_ocr = request.form.get('auto_ocr', 'false').lower() == 'true'
        auto_analysis = request.form.get('auto_analysis', 'false').lower() == 'true'

        # 构建图像元数据
        image_metadata = {
            'title': title,
            'category': category,
            'description': description,
            'equipment': equipment,
            'location': location,
            'tags': [tag.strip() for tag in tags.split(',') if tag.strip()] if tags else [],
            'upload_time': datetime.now().isoformat(),
            'file_info': {
                'filename': safe_filename,
                'file_path': relative_path,
                'file_size': file_size,
                'file_type': file.content_type
            }
        }

        # 模拟处理结果
        processing_results = {
            'image_info': {
                'width': '未知',
                'height': '未知',
                'format': file.content_type,
                'size_mb': round(file_size / (1024 * 1024), 2)
            }
        }

        # 如果启用了OCR或分析，添加模拟结果
        if auto_ocr:
            processing_results['ocr'] = {
                'status': '模拟OCR结果',
                'text': '这是模拟的OCR识别文本内容',
                'confidence': 0.95
            }

        if auto_analysis:
            processing_results['defect_analysis'] = {
                'status': '模拟缺陷检测结果',
                'defects_found': 0,
                'analysis': '未发现明显缺陷'
            }

        # 添加到知识库
        try:
            print("📚 正在添加图片到知识库...")

            if knowledge_base_instance:
                knowledge_base_instance.add_document(
                    doc_id=relative_path,
                    title=title,
                    content=f"图片: {title}\n描述: {description}\n设备: {equipment}\n位置: {location}\n标签: {tags}",
                    doc_type="image",
                    metadata=image_metadata
                )
            else:
                print("⚠️ 知识库不可用，跳过添加")

        except Exception as kb_error:
            print(f"⚠️ 添加到知识库失败: {kb_error}")

        # 返回成功响应
        response_data = {
            'success': True,
            'message': '图片上传成功',
            'filename': safe_filename,
            'file_path': relative_path,
            'file_size': file_size,
            'file_type': file.content_type,
            'upload_time': datetime.now().isoformat(),
            'metadata': image_metadata,
            'processing_results': processing_results
        }

        return jsonify(response_data), 200

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': f'图片上传失败: {str(e)}'}), 500

@app.route('/api/v1/upload-document', methods=['POST'])
def upload_document():
    """文档上传接口 - 优化版本"""
    try:

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                "success": False,
                "error": "没有选择文件",
                "debug_info": {
                    "available_files": list(request.files.keys()),
                    "content_type": request.content_type
                }
            }), 400

        file = request.files['file']

        if not file or file.filename == '':
            return jsonify({
                "success": False,
                "error": "没有选择文件或文件名为空"
            }), 400

        # 检查文件类型
        allowed_extensions = {'.txt', '.pdf', '.doc', '.docx', '.md', '.json', '.csv'}
        file_ext = os.path.splitext(file.filename)[1].lower()

        if file_ext not in allowed_extensions:
            return jsonify({
                "success": False,
                "error": f"不支持的文件类型: {file_ext}。支持的类型: {', '.join(allowed_extensions)}"
            }), 400

        # 创建上传目录
        upload_dir = app.config.get('UPLOAD_FOLDER', os.path.join(os.path.dirname(__file__), '..', 'uploads'))

        try:
            os.makedirs(upload_dir, exist_ok=True)
        except Exception as dir_error:
            return jsonify({
                "success": False,
                "error": f"创建上传目录失败: {str(dir_error)}"
            }), 500

        # 生成安全的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # 清理文件名中的特殊字符
        safe_base_name = "".join(c for c in os.path.splitext(file.filename)[0] if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_filename = f"{timestamp}_{safe_base_name}{file_ext}"
        file_path = os.path.join(upload_dir, safe_filename)

        print(f"安全文件名: {safe_filename}")

        # 保存文件
        try:
            file.save(file_path)
        except Exception as save_error:
            return jsonify({
                "success": False,
                "error": f"文件保存失败: {str(save_error)}"
            }), 500

        # 验证文件是否真的保存成功
        if not os.path.exists(file_path):
            return jsonify({
                "success": False,
                "error": "文件保存后验证失败"
            }), 500

        # 获取文件信息
        try:
            file_size = os.path.getsize(file_path)
        except Exception:
            file_size = 0

        # 尝试将文件添加到知识库
        knowledge_base_added = False
        if knowledge_base_instance and file_ext in ['.txt', '.pdf', '.doc', '.docx', '.md', '.json', '.csv']:
            try:
                print(f"📚 尝试将文件添加到知识库: {file_path}")
                success = knowledge_base_instance.add_document(file_path, "auto")
                if success:
                    knowledge_base_added = True
                else:
                    print("⚠️ 文件添加到知识库失败")
            except Exception as kb_error:
                print(f"❌ 知识库添加异常: {kb_error}")

        print("✅ 文件上传完全成功")

        response_data = {
            "success": True,
            "message": "文件上传成功",
            "file_info": {
                "original_name": file.filename,
                "saved_name": safe_filename,
                "file_path": os.path.abspath(file_path),
                "file_size": file_size,
                "file_type": file_ext,
                "upload_time": datetime.now().isoformat(),
                "upload_dir": os.path.abspath(upload_dir),
                "knowledge_base_added": knowledge_base_added
            }
        }

        # 如果添加到知识库成功，提供额外信息
        if knowledge_base_added:
            response_data["message"] = "文件上传成功并已添加到知识库"
            response_data["knowledge_base_info"] = {
                "indexed": True,
                "searchable": True,
                "note": "文件内容现在可以通过知识库搜索功能找到"
            }

        return jsonify(response_data)

    except Exception as e:
        # 记录详细错误信息
        error_msg = f"AI分析失败: {str(e)}"
        print(f"ERROR: {error_msg}")
        traceback.print_exc()

        return jsonify({
            "success": False,
            "error": f"文件上传失败: {str(e)}",
            "error_type": type(e).__name__,
            "debug_info": {
                "request_method": request.method,
                "content_type": request.content_type if hasattr(request, 'content_type') else 'unknown'
            }
        }), 500

@app.route('/favicon.ico')
def favicon():
    """处理favicon请求，避免404日志"""
    return '', 204

@app.route('/.well-known/appspecific/com.chrome.devtools.json')
def chrome_devtools():
    """Chrome开发者工具配置文件 - 返回空响应避免404日志"""
    return '', 204

if __name__ == '__main__':
    print("🚀 启动故障分析智能助手...")
    print(f"🌐 服务器启动在 http://0.0.0.0:5002")
    print("💡 提示: 知识库正在后台初始化，不影响基础功能使用")

    try:
        app.run(debug=False, host='0.0.0.0', port=5002)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
