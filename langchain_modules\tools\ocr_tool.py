"""
OCR工具

为LangChain提供OCR识别功能
"""

from typing import Dict, Any, Optional
from langchain.tools import BaseTool
from pydantic import Field
from loguru import logger

from data_processing.ocr_processor import OCRProcessor


class OCRTool(BaseTool):
    """OCR识别工具"""
    
    name: str = "ocr_tool"
    description: str = """
    OCR文字识别工具。用于从图像中提取文字信息。
    输入: 图像文件路径
    输出: 识别出的文字内容和位置信息
    """
    
    ocr_processor: OCRProcessor = Field(exclude=True)
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        ocr_processor = OCRProcessor(config.get("ocr", {}))
        super().__init__(ocr_processor=ocr_processor, **kwargs)
    
    def _run(self, image_path: str) -> str:
        """
        执行OCR识别
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            识别结果的JSON字符串
        """
        try:
            result = self.ocr_processor.extract_text(image_path)
            
            if not result or not result.get("text_blocks"):
                return "未能从图像中识别出文字内容"
            
            # 格式化输出
            output = {
                "full_text": result.get("full_text", ""),
                "confidence": result.get("confidence", 0.0),
                "total_blocks": result.get("total_blocks", 0),
                "text_blocks": []
            }
            
            # 只保留重要信息
            for block in result.get("text_blocks", [])[:10]:  # 最多返回10个文本块
                output["text_blocks"].append({
                    "text": block.get("text", ""),
                    "confidence": block.get("confidence", 0.0),
                    "bbox": block.get("bbox", [])
                })
            
            return f"OCR识别结果:\n全文: {output['full_text']}\n置信度: {output['confidence']:.2f}\n文本块数量: {output['total_blocks']}"
            
        except Exception as e:
            logger.error(f"OCR工具执行失败: {str(e)}")
            return f"OCR识别失败: {str(e)}"
    
    async def _arun(self, image_path: str) -> str:
        """异步执行OCR识别"""
        return self._run(image_path)


class TableExtractionTool(BaseTool):
    """表格提取工具"""
    
    name: str = "table_extraction_tool"
    description: str = """
    表格提取工具。用于从图像中提取表格数据。
    输入: 图像文件路径
    输出: 表格数据的结构化信息
    """
    
    ocr_processor: OCRProcessor = Field(exclude=True)
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        ocr_processor = OCRProcessor(config.get("ocr", {}))
        super().__init__(ocr_processor=ocr_processor, **kwargs)
    
    def _run(self, image_path: str) -> str:
        """
        执行表格提取
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            表格数据的字符串表示
        """
        try:
            result = self.ocr_processor.extract_table(image_path)
            
            if not result or not result.get("table_data"):
                return "未能从图像中提取到表格数据"
            
            table_data = result.get("table_data", [])
            
            # 格式化表格输出
            output_lines = ["提取的表格数据:"]
            for i, row in enumerate(table_data):
                row_str = " | ".join(row)
                output_lines.append(f"第{i+1}行: {row_str}")
            
            output_lines.append(f"\n表格行数: {len(table_data)}")
            output_lines.append(f"OCR置信度: {result.get('ocr_confidence', 0.0):.2f}")
            
            return "\n".join(output_lines)
            
        except Exception as e:
            logger.error(f"表格提取工具执行失败: {str(e)}")
            return f"表格提取失败: {str(e)}"
    
    async def _arun(self, image_path: str) -> str:
        """异步执行表格提取"""
        return self._run(image_path)
