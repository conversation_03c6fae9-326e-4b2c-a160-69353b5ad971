"""
故障分析核心功能模块

提供故障分析的核心业务功能和基础工具
"""

# 基础工具模块 - 无外部依赖，可以安全导入
from .config_manager import ConfigManager, get_config
from .security_utils import FileSecurityValidator, SecureFileUploader, get_file_uploader, validate_and_save_file

# 业务模块 - 延迟导入以避免依赖问题
def get_fault_analyzer():
    """延迟导入故障分析器"""
    from .fault_analyzer import FaultAnalyzer
    return FaultAnalyzer

def get_equipment_manager():
    """延迟导入设备管理器"""
    from .equipment_manager import EquipmentManager
    return EquipmentManager

def get_inspection_parser():
    """延迟导入检查解析器"""
    from .inspection_parser import InspectionParser
    return InspectionParser

def get_operation_analyzer():
    """延迟导入操作分析器"""
    from .operation_analyzer import OperationAnalyzer
    return OperationAnalyzer

__all__ = [
    # 基础工具
    "ConfigManager",
    "get_config",
    "FileSecurityValidator",
    "SecureFileUploader",
    "get_file_uploader",
    "validate_and_save_file",
    # 业务模块工厂函数
    "get_fault_analyzer",
    "get_equipment_manager",
    "get_inspection_parser",
    "get_operation_analyzer"
]
