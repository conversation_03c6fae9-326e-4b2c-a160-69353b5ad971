"""
故障诊断智能代理

整合多种工具和链，提供智能故障诊断能力
"""

from typing import Dict, List, Any, Optional
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain_core.prompts import PromptTemplate
from langchain_community.llms import DeepSeek
from loguru import logger

from ..tools.ocr_tool import OCRTool
from ..tools.defect_tool import DefectDetectionTool
from ..tools.equipment_tool import EquipmentLocationTool
from ..tools.waveform_tool import WaveformAnalysisTool
from ..chains.fault_analysis_chain import FaultAnalysisChain
from ..chains.document_qa_chain import DocumentQAChain
from ..chains.operation_analysis_chain import OperationAnalysisChain


class FaultDiagnosisAgent:
    """故障诊断智能代理"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化故障诊断代理
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.llm = self._init_llm()
        self.tools = self._init_tools()
        self.agent = self._init_agent()
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            max_iterations=10,
            handle_parsing_errors=True
        )
        
        logger.info("故障诊断智能代理初始化完成")
    
    def _init_llm(self):
        """初始化大语言模型"""
        llm_config = self.config.get("llm", {}).get("deepseek", {})
        
        return DeepSeek(
            model=llm_config.get("model_name", "deepseek-chat"),
            api_key=llm_config.get("api_key"),
            base_url=llm_config.get("base_url"),
            max_tokens=llm_config.get("max_tokens", 4096),
            temperature=llm_config.get("temperature", 0.7)
        )
    
    def _init_tools(self) -> List[Tool]:
        """初始化工具集"""
        tools = []
        
        # OCR工具
        ocr_tool = OCRTool(self.config)
        tools.append(Tool(
            name="OCR识别",
            description="从图像中提取文字信息，用于分析设备铭牌、报告等",
            func=ocr_tool.extract_text
        ))
        
        # 缺陷检测工具
        defect_tool = DefectDetectionTool(self.config)
        tools.append(Tool(
            name="缺陷检测",
            description="检测设备图像中的缺陷和异常，如锈蚀、裂纹、变形等",
            func=defect_tool.detect_defects
        ))
        
        # 设备定位工具
        equipment_tool = EquipmentLocationTool(self.config)
        tools.append(Tool(
            name="设备定位",
            description="在图像中定位和识别电力设备",
            func=equipment_tool.locate_equipment
        ))
        
        # 波形分析工具
        waveform_tool = WaveformAnalysisTool(self.config)
        tools.append(Tool(
            name="波形分析",
            description="分析电力系统波形数据，检测异常",
            func=waveform_tool.analyze_waveform
        ))
        
        # 故障分析链
        fault_chain = FaultAnalysisChain(self.config)
        tools.append(Tool(
            name="故障分析",
            description="基于设备信息和故障现象进行综合故障分析",
            func=fault_chain.run
        ))
        
        # 文档问答链
        qa_chain = DocumentQAChain(self.config)
        tools.append(Tool(
            name="文档问答",
            description="基于知识库文档回答技术问题",
            func=qa_chain.run
        ))
        
        # 运行方式分析链
        operation_chain = OperationAnalysisChain(self.config)
        tools.append(Tool(
            name="运行分析",
            description="分析电力系统运行方式和操作序列",
            func=operation_chain.run
        ))
        
        return tools
    
    def _init_agent(self):
        """初始化ReAct代理"""
        prompt_template = """
你是一个专业的电力系统故障诊断专家。你有以下工具可以使用：

{tools}

使用以下格式进行推理：

Question: 需要解决的问题
Thought: 我需要思考如何解决这个问题
Action: 选择使用的工具名称
Action Input: 工具的输入参数
Observation: 工具返回的结果
... (这个思考/行动/观察的过程可以重复多次)
Thought: 我现在知道最终答案了
Final Answer: 最终的诊断结果和建议

开始！

Question: {input}
Thought: {agent_scratchpad}
"""
        
        prompt = PromptTemplate(
            template=prompt_template,
            input_variables=["input", "agent_scratchpad", "tools"]
        )
        
        return create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
    
    def diagnose(self, 
                equipment_info: str,
                fault_symptoms: str,
                images: Optional[List[str]] = None,
                inspection_data: Optional[str] = None,
                historical_data: Optional[str] = None) -> Dict[str, Any]:
        """
        执行故障诊断
        
        Args:
            equipment_info: 设备信息
            fault_symptoms: 故障现象
            images: 相关图像路径列表
            inspection_data: 检查数据
            historical_data: 历史数据
            
        Returns:
            诊断结果
        """
        try:
            # 构建诊断问题
            question = self._build_diagnosis_question(
                equipment_info, fault_symptoms, images, 
                inspection_data, historical_data
            )
            
            # 执行诊断
            result = self.agent_executor.invoke({"input": question})
            
            return {
                "success": True,
                "diagnosis": result.get("output", ""),
                "reasoning_steps": self._extract_reasoning_steps(result),
                "tools_used": self._extract_tools_used(result)
            }
            
        except Exception as e:
            logger.error(f"故障诊断失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "diagnosis": "诊断过程中发生错误，请检查输入数据"
            }
    
    def _build_diagnosis_question(self, 
                                equipment_info: str,
                                fault_symptoms: str,
                                images: Optional[List[str]] = None,
                                inspection_data: Optional[str] = None,
                                historical_data: Optional[str] = None) -> str:
        """构建诊断问题"""
        question_parts = [
            f"设备信息：{equipment_info}",
            f"故障现象：{fault_symptoms}"
        ]
        
        if images:
            question_parts.append(f"相关图像：{', '.join(images)}")
        
        if inspection_data:
            question_parts.append(f"检查数据：{inspection_data}")
        
        if historical_data:
            question_parts.append(f"历史数据：{historical_data}")
        
        question_parts.append("请进行全面的故障诊断分析，包括可能的故障原因、诊断逻辑和处理建议。")
        
        return "\n".join(question_parts)
    
    def _extract_reasoning_steps(self, result: Dict[str, Any]) -> List[str]:
        """提取推理步骤"""
        # 从代理执行结果中提取推理步骤
        # 这里可以根据实际的结果格式进行解析
        return []
    
    def _extract_tools_used(self, result: Dict[str, Any]) -> List[str]:
        """提取使用的工具"""
        # 从代理执行结果中提取使用的工具
        # 这里可以根据实际的结果格式进行解析
        return []
    
    def ask_question(self, question: str) -> Dict[str, Any]:
        """
        回答技术问题
        
        Args:
            question: 技术问题
            
        Returns:
            回答结果
        """
        try:
            result = self.agent_executor.invoke({"input": question})
            
            return {
                "success": True,
                "answer": result.get("output", ""),
                "tools_used": self._extract_tools_used(result)
            }
            
        except Exception as e:
            logger.error(f"问答失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "answer": "回答问题时发生错误"
            }
    
    def analyze_operation(self, operation_data: str) -> Dict[str, Any]:
        """
        分析运行方式
        
        Args:
            operation_data: 运行数据
            
        Returns:
            分析结果
        """
        try:
            question = f"请分析以下电力系统运行数据：\n{operation_data}\n请提供运行状态评估和建议。"
            
            result = self.agent_executor.invoke({"input": question})
            
            return {
                "success": True,
                "analysis": result.get("output", ""),
                "tools_used": self._extract_tools_used(result)
            }
            
        except Exception as e:
            logger.error(f"运行分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis": "运行分析时发生错误"
            }
