# FAISS向量存储目录

此目录用于存储FAISS向量数据库文件，支持高效的相似度搜索。

## 目录结构

```
embeddings/faiss_store/
├── index.faiss           # 主向量索引文件
├── index.pkl             # 索引元数据文件
├── documents.json        # 文档映射文件
├── metadata.json         # 向量元数据
├── backup/               # 备份文件
│   ├── index_backup_*.faiss
│   └── metadata_backup_*.json
└── temp/                 # 临时文件
    └── building_index/   # 索引构建临时文件
```

## FAISS索引类型

系统支持以下FAISS索引类型：

### 1. IndexFlatIP (内积索引)
- **用途**：精确相似度搜索
- **特点**：准确度高，适合小规模数据
- **文件**：`index_flat.faiss`

### 2. IndexIVFFlat (倒排索引)
- **用途**：大规模数据快速搜索
- **特点**：速度快，内存占用少
- **文件**：`index_ivf.faiss`

### 3. IndexHNSW (分层导航小世界)
- **用途**：高维数据近似搜索
- **特点**：查询速度极快
- **文件**：`index_hnsw.faiss`

## 向量维度配置

```python
# 支持的向量维度
EMBEDDING_DIMENSIONS = {
    "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2": 384,
    "sentence-transformers/all-MiniLM-L6-v2": 384,
    "text-embedding-ada-002": 1536,
    "custom-model": 768
}
```

## 索引构建流程

1. **数据预处理**
   - 文本清洗和标准化
   - 向量化处理
   - 数据验证

2. **索引构建**
   - 选择合适的索引类型
   - 设置索引参数
   - 批量添加向量

3. **索引优化**
   - 训练索引（如需要）
   - 参数调优
   - 性能测试

4. **索引保存**
   - 保存索引文件
   - 保存元数据
   - 创建备份

## 使用示例

### 构建索引
```python
import faiss
import numpy as np

# 创建索引
dimension = 384
index = faiss.IndexFlatIP(dimension)

# 添加向量
vectors = np.random.random((1000, dimension)).astype('float32')
index.add(vectors)

# 保存索引
faiss.write_index(index, 'embeddings/faiss_store/index.faiss')
```

### 搜索向量
```python
# 加载索引
index = faiss.read_index('embeddings/faiss_store/index.faiss')

# 搜索相似向量
query_vector = np.random.random((1, dimension)).astype('float32')
scores, indices = index.search(query_vector, k=10)
```

## 性能优化

### 索引参数调优
```python
# IVF索引参数
nlist = 100  # 聚类中心数量
index = faiss.IndexIVFFlat(quantizer, dimension, nlist)

# HNSW索引参数
M = 16       # 连接数
ef = 200     # 搜索参数
index = faiss.IndexHNSWFlat(dimension, M)
index.hnsw.efConstruction = ef
```

### 内存优化
- 使用适当的数据类型（float32）
- 批量处理大数据集
- 定期清理临时文件
- 使用内存映射文件

## 备份和恢复

### 自动备份
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
cp embeddings/faiss_store/index.faiss embeddings/faiss_store/backup/index_backup_$DATE.faiss
cp embeddings/faiss_store/metadata.json embeddings/faiss_store/backup/metadata_backup_$DATE.json
```

### 恢复索引
```python
# 从备份恢复
backup_file = 'embeddings/faiss_store/backup/index_backup_20240101.faiss'
index = faiss.read_index(backup_file)
faiss.write_index(index, 'embeddings/faiss_store/index.faiss')
```

## 监控和维护

- 定期检查索引文件完整性
- 监控搜索性能指标
- 清理过期的临时文件
- 更新索引以包含新数据

## 故障排除

### 常见问题
1. **索引文件损坏**：从备份恢复
2. **内存不足**：调整批处理大小
3. **搜索速度慢**：优化索引参数
4. **精度不足**：检查向量质量

## 示例数据文件

本目录包含以下示例文件：
- `sample_index.faiss` - 示例向量索引
- `sample_metadata.json` - 示例元数据
- `sample_documents.json` - 示例文档映射
- 更多示例文件请参考目录内容
