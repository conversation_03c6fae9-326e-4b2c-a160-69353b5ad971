"""
设备定位工具

为LangChain提供设备信息查询和定位功能
"""

import json
from typing import Dict, Any, List, Optional
from langchain.tools import BaseTool
from pydantic import Field
from loguru import logger


class EquipmentLocatorTool(BaseTool):
    """设备定位工具"""
    
    name: str = "equipment_locator_tool"
    description: str = """
    设备定位工具。用于查询设备的基本信息、位置和状态。
    输入: 设备名称或设备编号
    输出: 设备的详细信息，包括型号、参数、位置、状态等
    """
    
    equipment_db_path: str = Field(default="./data/structured/equipment_info.json")
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        equipment_db_path = config.get("equipment_db_path", "./data/structured/equipment_info.json")
        super().__init__(equipment_db_path=equipment_db_path, **kwargs)
    
    def _load_equipment_data(self) -> Dict[str, Any]:
        """加载设备数据"""
        try:
            with open(self.equipment_db_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"设备数据文件不存在: {self.equipment_db_path}")
            return {}
        except Exception as e:
            logger.error(f"加载设备数据失败: {str(e)}")
            return {}
    
    def _run(self, equipment_query: str) -> str:
        """
        查询设备信息
        
        Args:
            equipment_query: 设备名称或编号
            
        Returns:
            设备信息的字符串描述
        """
        try:
            equipment_data = self._load_equipment_data()
            
            if not equipment_data:
                return "设备数据库为空或无法访问"
            
            # 搜索匹配的设备
            matched_equipment = []
            query_lower = equipment_query.lower()
            
            for eq_id, eq_info in equipment_data.items():
                # 检查设备ID、名称、型号是否匹配
                if (query_lower in eq_id.lower() or 
                    query_lower in eq_info.get("name", "").lower() or
                    query_lower in eq_info.get("model", "").lower()):
                    matched_equipment.append((eq_id, eq_info))
            
            if not matched_equipment:
                return f"未找到匹配的设备: {equipment_query}"
            
            # 格式化输出
            if len(matched_equipment) == 1:
                eq_id, eq_info = matched_equipment[0]
                return self._format_equipment_info(eq_id, eq_info)
            else:
                # 多个匹配结果
                output_lines = [f"找到 {len(matched_equipment)} 个匹配的设备:"]
                for i, (eq_id, eq_info) in enumerate(matched_equipment[:5]):  # 最多显示5个
                    output_lines.append(f"\n{i+1}. {eq_info.get('name', eq_id)}")
                    output_lines.append(f"   设备编号: {eq_id}")
                    output_lines.append(f"   型号: {eq_info.get('model', 'unknown')}")
                    output_lines.append(f"   状态: {eq_info.get('status', 'unknown')}")
                
                if len(matched_equipment) > 5:
                    output_lines.append(f"\n... 还有 {len(matched_equipment) - 5} 个设备")
                
                return "\n".join(output_lines)
            
        except Exception as e:
            logger.error(f"设备定位工具执行失败: {str(e)}")
            return f"设备查询失败: {str(e)}"
    
    def _format_equipment_info(self, eq_id: str, eq_info: Dict[str, Any]) -> str:
        """格式化设备信息"""
        output_lines = [f"设备信息: {eq_info.get('name', eq_id)}"]
        output_lines.append(f"设备编号: {eq_id}")
        
        # 基本信息
        if eq_info.get("model"):
            output_lines.append(f"型号: {eq_info['model']}")
        if eq_info.get("manufacturer"):
            output_lines.append(f"制造商: {eq_info['manufacturer']}")
        if eq_info.get("installation_date"):
            output_lines.append(f"投运日期: {eq_info['installation_date']}")
        
        # 技术参数
        parameters = eq_info.get("parameters", {})
        if parameters:
            output_lines.append("\n技术参数:")
            for param, value in parameters.items():
                output_lines.append(f"  {param}: {value}")
        
        # 位置信息
        location = eq_info.get("location", {})
        if location:
            output_lines.append("\n位置信息:")
            if location.get("substation"):
                output_lines.append(f"  变电站: {location['substation']}")
            if location.get("bay"):
                output_lines.append(f"  间隔: {location['bay']}")
            if location.get("coordinates"):
                coords = location["coordinates"]
                output_lines.append(f"  坐标: ({coords.get('x', 0)}, {coords.get('y', 0)})")
        
        # 运行状态
        status = eq_info.get("status", {})
        if status:
            output_lines.append("\n运行状态:")
            if status.get("operational_status"):
                output_lines.append(f"  运行状态: {status['operational_status']}")
            if status.get("last_maintenance"):
                output_lines.append(f"  上次检修: {status['last_maintenance']}")
            if status.get("next_maintenance"):
                output_lines.append(f"  下次检修: {status['next_maintenance']}")
        
        # 历史记录
        history = eq_info.get("maintenance_history", [])
        if history:
            output_lines.append(f"\n检修历史 (最近{min(3, len(history))}次):")
            for i, record in enumerate(history[:3]):
                output_lines.append(f"  {i+1}. {record.get('date', 'unknown')}: {record.get('type', 'unknown')} - {record.get('description', '')}")
        
        return "\n".join(output_lines)
    
    async def _arun(self, equipment_query: str) -> str:
        """异步执行设备查询"""
        return self._run(equipment_query)


class EquipmentStatusTool(BaseTool):
    """设备状态工具"""
    
    name: str = "equipment_status_tool"
    description: str = """
    设备状态查询工具。用于查询设备的实时运行状态和参数。
    输入: 设备名称或编号
    输出: 设备的实时状态信息
    """
    
    status_db_path: str = Field(default="./data/structured/equipment_status.json")
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        status_db_path = config.get("status_db_path", "./data/structured/equipment_status.json")
        super().__init__(status_db_path=status_db_path, **kwargs)
    
    def _load_status_data(self) -> Dict[str, Any]:
        """加载设备状态数据"""
        try:
            with open(self.status_db_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"设备状态文件不存在: {self.status_db_path}")
            return {}
        except Exception as e:
            logger.error(f"加载设备状态失败: {str(e)}")
            return {}
    
    def _run(self, equipment_query: str) -> str:
        """
        查询设备状态
        
        Args:
            equipment_query: 设备名称或编号
            
        Returns:
            设备状态信息的字符串描述
        """
        try:
            status_data = self._load_status_data()
            
            if not status_data:
                return "设备状态数据库为空或无法访问"
            
            # 搜索匹配的设备状态
            matched_status = None
            query_lower = equipment_query.lower()
            
            for eq_id, status_info in status_data.items():
                if query_lower in eq_id.lower():
                    matched_status = (eq_id, status_info)
                    break
            
            if not matched_status:
                return f"未找到设备状态信息: {equipment_query}"
            
            eq_id, status_info = matched_status
            
            # 格式化状态输出
            output_lines = [f"设备状态: {eq_id}"]
            output_lines.append(f"更新时间: {status_info.get('timestamp', 'unknown')}")
            
            # 运行参数
            parameters = status_info.get("parameters", {})
            if parameters:
                output_lines.append("\n运行参数:")
                for param, value in parameters.items():
                    unit = value.get("unit", "") if isinstance(value, dict) else ""
                    val = value.get("value", value) if isinstance(value, dict) else value
                    output_lines.append(f"  {param}: {val} {unit}")
            
            # 告警信息
            alarms = status_info.get("alarms", [])
            if alarms:
                output_lines.append(f"\n告警信息 ({len(alarms)} 个):")
                for i, alarm in enumerate(alarms[:5]):  # 最多显示5个告警
                    level = alarm.get("level", "unknown")
                    message = alarm.get("message", "")
                    time = alarm.get("time", "")
                    output_lines.append(f"  {i+1}. [{level}] {message} ({time})")
            
            # 开关状态
            switches = status_info.get("switches", {})
            if switches:
                output_lines.append("\n开关状态:")
                for switch, state in switches.items():
                    output_lines.append(f"  {switch}: {state}")
            
            return "\n".join(output_lines)
            
        except Exception as e:
            logger.error(f"设备状态工具执行失败: {str(e)}")
            return f"设备状态查询失败: {str(e)}"
    
    async def _arun(self, equipment_query: str) -> str:
        """异步执行设备状态查询"""
        return self._run(equipment_query)
