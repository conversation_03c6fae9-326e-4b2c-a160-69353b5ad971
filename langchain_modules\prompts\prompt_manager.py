"""
提示词管理器

管理各种场景的提示词模板
"""

import os
import yaml
from typing import Dict, Any, Optional
from langchain.prompts import PromptTemplate
from loguru import logger


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.prompts_config = config.get("prompts", {})
        self.templates = {}
        
        # 加载提示词模板
        self._load_prompt_templates()
    
    def _load_prompt_templates(self):
        """加载提示词模板"""
        try:
            # 从配置文件加载
            templates_config = self.prompts_config.get("templates", {})
            
            for template_name, template_config in templates_config.items():
                self.templates[template_name] = PromptTemplate(
                    template=template_config.get("template", ""),
                    input_variables=template_config.get("input_variables", [])
                )
            
            # 加载默认模板
            self._load_default_templates()
            
            logger.info(f"加载了 {len(self.templates)} 个提示词模板")
            
        except Exception as e:
            logger.error(f"加载提示词模板失败: {str(e)}")
    
    def _load_default_templates(self):
        """加载默认提示词模板"""
        
        # 标准RAG故障分析模板 - 符合提示词工程规范
        self.templates["fault_analysis"] = PromptTemplate(
            template="""# 指令（Instructions）
你是白银市电力系统资深故障诊断专家，拥有20年以上变电站运维经验。请根据下面提供的上下文信息来回答用户问题。

## 回答要求：
- 如果上下文中没有足够信息，请明确说明需要补充哪些技术数据
- 使用专业术语但保持表述清晰易懂
- 重要技术参数和结论用**粗体**标注
- 回答应包含具体的数值分析和技术建议
- 确保分析的专业性和可操作性

# 上下文信息（Context）
## 故障基本信息
{fault_description}

## 设备技术参数
{equipment_info}

## 运行监测数据
{operation_data}

## 历史故障记录
{history_data}

# 用户问题（Question）
{question}

# 专业分析（请基于上述上下文信息回答）：""",
            input_variables=["fault_description", "equipment_info", "operation_data", "history_data", "question"]
        )

        # DeepSeek R1 专业故障分析模板 - 优化版，确保正确的思考过程格式
        self.templates["deepseek_fault_analysis"] = PromptTemplate(
            template="""# 指令（Instructions）
你是白银市电力系统资深故障诊断专家，拥有20年以上的变电站运维和故障分析经验。

## 重要输出格式要求：
请严格按照以下格式输出，确保思考过程和最终分析清晰分离：

**第一部分：专家思考过程**
请在 <thinking> 和 </thinking> 标签内展示您的完整推理过程，包括：
- 对故障现象的初步判断
- 技术参数的分析思路
- 可能原因的逐一排查
- 证据的权重评估
- 推理链条的逻辑展开

**第二部分：专业诊断报告**
在思考过程之后，请用连贯的自然语言提供专业诊断报告，避免使用编号列表格式。

## 专业身份定位：
- 国家电网白银供电公司高级工程师
- 电力系统故障分析技术专家
- 具备丰富的110kV/220kV变电站运维经验
- 熟悉西北地区电力设备运行特点和环境影响

# 上下文信息（Context）
## 故障基本描述
{fault_description}

## 设备技术信息
{equipment_info}

## 运行监测数据
{operation_data}

## 历史故障记录
{history_data}

## 图像分析结果
{image_analysis}

# 用户问题（Question）
{question}

# 请开始您的专业分析：

<thinking>
[请在此处展示您作为电力系统专家的完整思考过程，包括对故障现象的分析、技术参数的评估、可能原因的推理等]
</thinking>

**基于上述思考的专业故障诊断报告：**

根据故障现象和技术参数的综合分析，可以对此次故障的性质和原因进行专业判断。从系统运行状态来看，故障发生前的运行方式和负荷分布情况需要详细评估。结合设备的技术特性和运行历史，能够更准确地定位故障根源。

现场检查应重点关注关键部位的外观状况和电气参数变化。保护装置的动作机理反映了故障的发展过程和严重程度。通过对故障物理特征的识别，可以确定损坏模式和影响范围。

综合多维度信息进行根因分析，能够揭示故障的深层次原因。基于分析结果，制定针对性的技术处理方案，包括设备修复措施和运行方式调整建议，确保系统安全稳定运行。

**技术要求：**
- 必须包含具体的技术参数和数值（电压等级、电流值、阻抗值、温度、压力等）
- 使用准确的电力专业术语（差动保护、零序保护、绝缘电阻、介损、色谱分析等）
- 重要结论用**粗体**标注
- 提供量化的风险评估和具体的处理时限要求
- 最终诊断报告必须使用连贯的自然语言表达，避免编号列表格式""",
            input_variables=["fault_description", "equipment_info", "operation_data", "history_data", "image_analysis", "question"]
        )
        
        # 设备检查模板
        self.templates["equipment_inspection"] = PromptTemplate(
            template="""你是一个专业的电力设备检查专家。

请根据以下检查信息进行分析：

检查对象：{equipment_name}
检查类型：{inspection_type}
检查数据：{inspection_data}
图像分析：{image_analysis}
测试结果：{test_results}

请提供：
1. 设备状态评估
2. 发现的问题
3. 风险等级评定
4. 处理建议
5. 下次检查建议

评估标准：
- 按照电力行业标准
- 考虑设备使用年限
- 评估安全风险
- 提供量化指标

检查结论：""",
            input_variables=["equipment_name", "inspection_type", "inspection_data", "image_analysis", "test_results"]
        )
        
        # 运行方式分析模板
        self.templates["operation_analysis"] = PromptTemplate(
            template="""你是一个电力系统运行方式分析专家。

请分析以下运行方式：

系统状态：{system_status}
负荷情况：{load_condition}
设备状态：{equipment_status}
天气条件：{weather_condition}
特殊要求：{special_requirements}

分析内容：
1. 当前运行方式评估
2. 潜在风险识别
3. 优化建议
4. 应急预案
5. 操作注意事项

分析原则：
- 确保系统安全稳定
- 优化经济运行
- 满足供电质量要求
- 考虑设备健康状况

分析结果：""",
            input_variables=["system_status", "load_condition", "equipment_status", "weather_condition", "special_requirements"]
        )
        
        # 保护动作分析模板
        self.templates["protection_analysis"] = PromptTemplate(
            template="""你是一个继电保护专家，请分析以下保护动作：

保护装置：{protection_device}
动作时间：{action_time}
动作类型：{action_type}
故障录波：{fault_recording}
系统状态：{system_state}
相关设备：{related_equipment}

分析要点：
1. 保护动作正确性
2. 动作时间分析
3. 选择性分析
4. 故障性质判断
5. 系统影响评估

技术要求：
- 基于保护原理分析
- 结合现场实际情况
- 考虑保护配合关系
- 评估保护性能

分析结论：""",
            input_variables=["protection_device", "action_time", "action_type", "fault_recording", "system_state", "related_equipment"]
        )
        
        # 文档问答模板
        self.templates["document_qa"] = PromptTemplate(
            template="""基于以下文档内容回答问题：

文档内容：
{document_content}

用户问题：{question}

请根据文档内容准确回答问题，如果文档中没有相关信息，请明确说明。

回答要求：
- 基于文档内容回答
- 保持准确性和完整性
- 如有必要，引用具体段落
- 避免推测和臆断

回答：""",
            input_variables=["document_content", "question"]
        )
        
        # OCR结果分析模板
        self.templates["ocr_analysis"] = PromptTemplate(
            template="""请分析以下OCR识别结果：

识别文本：{ocr_text}
置信度：{confidence}
图像类型：{image_type}
识别场景：{scene_context}

分析任务：
1. 文本内容理解
2. 关键信息提取
3. 数据有效性验证
4. 可能的错误识别
5. 改进建议

分析重点：
- 识别准确性评估
- 关键数据提取
- 格式化输出
- 异常情况标注

分析结果：""",
            input_variables=["ocr_text", "confidence", "image_type", "scene_context"]
        )
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """
        获取提示词模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            提示词模板
        """
        return self.templates.get(template_name)
    
    def format_prompt(self, template_name: str, **kwargs) -> Optional[str]:
        """
        格式化提示词
        
        Args:
            template_name: 模板名称
            **kwargs: 模板变量
            
        Returns:
            格式化后的提示词
        """
        try:
            template = self.get_template(template_name)
            if not template:
                logger.error(f"未找到模板: {template_name}")
                return None
            
            return template.format(**kwargs)
            
        except Exception as e:
            logger.error(f"格式化提示词失败: {str(e)}")
            return None

    def get_deepseek_fault_analysis_prompt(self, fault_description: str, equipment_info: str = "",
                                         operation_data: str = "", history_data: str = "",
                                         image_analysis: str = "", question: str = "") -> str:
        """
        获取符合RAG标准的DeepSeek故障分析提示词

        Args:
            fault_description: 故障描述
            equipment_info: 设备信息
            operation_data: 运行数据
            history_data: 历史记录
            image_analysis: 图像分析结果
            question: 用户问题

        Returns:
            符合RAG标准的格式化提示词
        """
        try:
            return self.format_prompt(
                "deepseek_fault_analysis",
                fault_description=fault_description,
                equipment_info=equipment_info or "暂无详细设备信息",
                operation_data=operation_data or "暂无运行数据",
                history_data=history_data or "暂无历史记录",
                image_analysis=image_analysis or "暂无图像分析",
                question=question or "请进行故障诊断分析"
            )
        except Exception as e:
            logger.error(f"获取DeepSeek故障分析提示词失败: {str(e)}")
            return self.format_prompt(
                "fault_analysis",
                fault_description=fault_description,
                equipment_info=equipment_info,
                operation_data=operation_data,
                history_data=history_data,
                question=question or "请进行故障分析"
            )

    def get_enhanced_context_prompt(self, query: str, context_data: dict) -> str:
        """
        获取符合RAG标准的增强上下文提示词

        遵循提示词工程三要素：指令 + 上下文占位符 + 问题占位符

        Args:
            query: 用户查询（问题占位符）
            context_data: 上下文数据字典（上下文占位符）

        Returns:
            符合RAG标准的提示词
        """
        # 构建结构化上下文
        structured_context = self._build_structured_context(context_data)

        # 标准RAG提示词模板
        enhanced_prompt = f"""# 指令（Instructions）
你是白银市电力系统故障诊断专家。请根据下面提供的上下文信息来回答用户问题。

## 回答规则：
- 严格基于提供的上下文信息进行回答
- 如果上下文中没有答案，请说"根据当前信息无法确定，需要补充以下技术数据：..."
- 使用专业术语但保持表述清晰
- 重要信息用**粗体**标注
- 提供具体的数据和建议
- 回答应简洁专业，避免冗余

# 上下文信息（Context）
{structured_context}

# 用户问题（Question）
{query}

# 基于上下文的专业回答："""

        return enhanced_prompt

    def _build_structured_context(self, context_data: dict) -> str:
        """构建符合RAG标准的结构化上下文"""
        context_parts = []

        # 设备信息
        if context_data.get("equipment_info"):
            context_parts.append(f"## 设备信息\n{context_data['equipment_info']}")

        # 故障记录
        if context_data.get("fault_records"):
            context_parts.append(f"## 故障记录\n{context_data['fault_records']}")

        # 运行数据
        if context_data.get("operation_data"):
            context_parts.append(f"## 运行数据\n{context_data['operation_data']}")

        # 检索结果（RAG核心）
        if context_data.get("search_results"):
            results_text = "\n".join([
                f"- {result.get('content', '')[:200]}..."
                for result in context_data['search_results'][:3]
            ])
            context_parts.append(f"## 相关技术文档\n{results_text}")

        # 图像分析
        if context_data.get("image_analysis"):
            context_parts.append(f"## 图像分析\n{context_data['image_analysis']}")

        # 如果没有上下文信息，返回提示
        if not context_parts:
            return "## 上下文信息\n暂无相关技术信息，请提供更多故障详情。"

        return "\n\n".join(context_parts)

    def _build_professional_context(self, context_data: dict) -> str:
        """构建专业化技术上下文（保持向后兼容）"""
        return self._build_structured_context(context_data)

    def add_template(self, template_name: str, template_str: str, input_variables: list):
        """
        添加新的提示词模板
        
        Args:
            template_name: 模板名称
            template_str: 模板字符串
            input_variables: 输入变量列表
        """
        try:
            self.templates[template_name] = PromptTemplate(
                template=template_str,
                input_variables=input_variables
            )
            logger.info(f"添加提示词模板: {template_name}")
            
        except Exception as e:
            logger.error(f"添加提示词模板失败: {str(e)}")
    
    def list_templates(self) -> list:
        """
        列出所有模板名称
        
        Returns:
            模板名称列表
        """
        return list(self.templates.keys())
    
    def save_templates_to_file(self, file_path: str):
        """
        保存模板到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            templates_data = {}
            
            for name, template in self.templates.items():
                templates_data[name] = {
                    "template": template.template,
                    "input_variables": template.input_variables
                }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(templates_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"模板已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存模板失败: {str(e)}")
    
    def load_templates_from_file(self, file_path: str):
        """
        从文件加载模板
        
        Args:
            file_path: 文件路径
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"模板文件不存在: {file_path}")
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                templates_data = yaml.safe_load(f)
            
            for name, template_config in templates_data.items():
                self.templates[name] = PromptTemplate(
                    template=template_config.get("template", ""),
                    input_variables=template_config.get("input_variables", [])
                )
            
            logger.info(f"从文件加载了 {len(templates_data)} 个模板")
            
        except Exception as e:
            logger.error(f"从文件加载模板失败: {str(e)}")
    
    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板信息
        """
        template = self.get_template(template_name)
        if not template:
            return None
        
        return {
            "name": template_name,
            "template": template.template,
            "input_variables": template.input_variables,
            "variable_count": len(template.input_variables)
        }
