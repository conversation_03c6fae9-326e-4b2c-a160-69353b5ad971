"""
安全工具模块

提供文件上传安全验证、API认证等安全功能
"""

import os
import hashlib
import uuid
from typing import List, Optional, Tuple
from pathlib import Path
from fastapi import HTTPException, UploadFile
from loguru import logger

# 尝试导入python-magic，如果不可用则使用备用方案
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
    logger.warning("python-magic 模块未安装，将使用基础文件类型检测")

from .config_manager import get_config


class FileSecurityValidator:
    """文件安全验证器"""
    
    # 危险文件扩展名
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', 
        '.jar', '.sh', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb'
    }
    
    # 允许的MIME类型映射
    ALLOWED_MIME_TYPES = {
        'image': {
            'image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 
            'image/tiff', 'image/webp', 'image/gif'
        },
        'document': {
            'application/pdf', 'text/plain', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        },
        'data': {
            'application/json', 'text/csv', 'application/xml', 'text/xml'
        }
    }
    
    def __init__(self):
        self.config = get_config()
        self.max_file_size = self.config.get_max_file_size()
        self.allowed_extensions = set(self.config.get_allowed_file_types())
    
    def validate_file(self, file: UploadFile, file_type: str = 'any') -> Tuple[bool, str]:
        """
        验证上传文件的安全性
        
        Args:
            file: 上传的文件
            file_type: 文件类型 ('image', 'document', 'data', 'any')
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            # 1. 检查文件名
            if not file.filename:
                return False, "文件名不能为空"
            
            # 2. 检查文件扩展名
            file_ext = Path(file.filename).suffix.lower()
            if file_ext in self.DANGEROUS_EXTENSIONS:
                return False, f"不允许上传 {file_ext} 类型的文件"
            
            if file_ext not in self.allowed_extensions:
                return False, f"不支持的文件类型: {file_ext}"
            
            # 3. 检查文件大小
            if hasattr(file.file, 'seek') and hasattr(file.file, 'tell'):
                file.file.seek(0, 2)  # 移动到文件末尾
                file_size = file.file.tell()
                file.file.seek(0)  # 重置到文件开头
                
                if file_size > self.max_file_size:
                    return False, f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)"
            
            # 4. 检查MIME类型
            if file_type != 'any':
                allowed_mimes = self.ALLOWED_MIME_TYPES.get(file_type, set())
                if file.content_type not in allowed_mimes:
                    return False, f"不支持的MIME类型: {file.content_type}"
            
            # 5. 检查文件内容（魔数验证）
            content_valid, content_error = self._validate_file_content(file, file_type)
            if not content_valid:
                return False, content_error
            
            return True, "文件验证通过"
            
        except Exception as e:
            logger.error(f"文件验证错误: {str(e)}")
            return False, f"文件验证失败: {str(e)}"
    
    def _validate_file_content(self, file: UploadFile, file_type: str) -> Tuple[bool, str]:
        """验证文件内容（魔数检查）"""
        try:
            # 读取文件头部字节
            file.file.seek(0)
            header = file.file.read(1024)
            file.file.seek(0)
            
            # 使用python-magic检查真实文件类型（如果可用）
            if HAS_MAGIC:
                try:
                    detected_mime = magic.from_buffer(header, mime=True)

                    # 验证声明的MIME类型与实际类型是否匹配
                    if file_type == 'image':
                        if not detected_mime.startswith('image/'):
                            return False, "文件内容与声明的图片类型不符"
                    elif file_type == 'document':
                        allowed_doc_mimes = {'application/pdf', 'text/plain', 'application/msword'}
                        if detected_mime not in allowed_doc_mimes and not detected_mime.startswith('application/vnd'):
                            return False, "文件内容与声明的文档类型不符"

                except Exception as e:
                    logger.warning(f"魔数检查失败，跳过: {str(e)}")
            else:
                # 备用方案：基于文件头的简单检测
                if not self._check_magic_numbers_fallback(header, file_type):
                    return False, "文件头检测失败"

            return True, "内容验证通过"

        except Exception as e:
            logger.error(f"文件内容验证错误: {str(e)}")
            return False, f"内容验证失败: {str(e)}"

    def _check_magic_numbers_fallback(self, header: bytes, file_type: str) -> bool:
        """备用魔数检测方法（不依赖python-magic）"""
        if not header:
            return False

        # 常见文件类型的魔数
        magic_numbers = {
            # 图像文件
            b'\xFF\xD8\xFF': 'image',  # JPEG
            b'\x89PNG\r\n\x1a\n': 'image',  # PNG
            b'GIF87a': 'image',  # GIF87a
            b'GIF89a': 'image',  # GIF89a
            b'BM': 'image',  # BMP
            b'RIFF': 'image',  # WebP (需要进一步检查)

            # 文档文件
            b'%PDF': 'document',  # PDF
            b'\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1': 'document',  # MS Office
            b'PK\x03\x04': 'document',  # ZIP/Office 2007+
        }

        # 检查文件头是否匹配
        for magic_bytes, detected_type in magic_numbers.items():
            if header.startswith(magic_bytes):
                if file_type == 'any' or detected_type == file_type:
                    return True

        # 对于WebP需要特殊处理
        if header.startswith(b'RIFF') and b'WEBP' in header[:12]:
            return file_type in ['image', 'any']

        # 如果没有匹配的魔数，对于文本文件类型比较宽松
        if file_type == 'document':
            # 检查是否为纯文本（ASCII或UTF-8）
            try:
                header.decode('utf-8')
                return True
            except UnicodeDecodeError:
                pass

        return False
    
    def generate_safe_filename(self, original_filename: str) -> str:
        """生成安全的文件名"""
        # 获取文件扩展名
        file_ext = Path(original_filename).suffix.lower()
        
        # 生成UUID作为文件名
        safe_name = str(uuid.uuid4())
        
        return f"{safe_name}{file_ext}"
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        
        return hash_sha256.hexdigest()


class SecureFileUploader:
    """安全文件上传器"""
    
    def __init__(self):
        self.validator = FileSecurityValidator()
        self.config = get_config()
        self.upload_base_dir = self.config.get_upload_dir()
    
    def save_file(self, file: UploadFile, file_type: str = 'any', 
                  subdirectory: str = 'general') -> dict:
        """
        安全保存上传文件
        
        Args:
            file: 上传的文件
            file_type: 文件类型
            subdirectory: 子目录名
            
        Returns:
            文件信息字典
        """
        try:
            # 1. 验证文件安全性
            is_valid, error_msg = self.validator.validate_file(file, file_type)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_msg)
            
            # 2. 创建上传目录
            upload_dir = Path(self.upload_base_dir) / subdirectory
            upload_dir.mkdir(parents=True, exist_ok=True)
            
            # 3. 生成安全文件名
            safe_filename = self.validator.generate_safe_filename(file.filename)
            file_path = upload_dir / safe_filename
            
            # 4. 保存文件
            with open(file_path, "wb") as buffer:
                content = file.file.read()
                buffer.write(content)
            
            # 5. 计算文件哈希
            file_hash = self.validator.calculate_file_hash(str(file_path))
            
            # 6. 获取文件信息
            file_size = file_path.stat().st_size
            
            file_info = {
                "original_filename": file.filename,
                "safe_filename": safe_filename,
                "file_path": str(file_path),
                "relative_path": str(file_path.relative_to(self.upload_base_dir)),
                "file_size": file_size,
                "file_type": file.content_type,
                "file_hash": file_hash,
                "upload_directory": subdirectory
            }
            
            logger.info(f"文件上传成功: {file.filename} -> {safe_filename}")
            return file_info
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"文件保存失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")
    
    def delete_file(self, file_path: str) -> bool:
        """安全删除文件"""
        try:
            path = Path(file_path)
            if path.exists() and path.is_file():
                # 确保文件在上传目录内（防止路径遍历攻击）
                upload_base = Path(self.upload_base_dir).resolve()
                file_abs_path = path.resolve()
                
                if upload_base in file_abs_path.parents:
                    path.unlink()
                    logger.info(f"文件删除成功: {file_path}")
                    return True
                else:
                    logger.warning(f"尝试删除上传目录外的文件: {file_path}")
                    return False
            return False
        except Exception as e:
            logger.error(f"文件删除失败: {str(e)}")
            return False


# 全局实例
file_uploader = SecureFileUploader()


def get_file_uploader() -> SecureFileUploader:
    """获取文件上传器实例"""
    return file_uploader


def validate_and_save_file(file: UploadFile, file_type: str = 'any', 
                          subdirectory: str = 'general') -> dict:
    """便捷函数：验证并保存文件"""
    return file_uploader.save_file(file, file_type, subdirectory)
