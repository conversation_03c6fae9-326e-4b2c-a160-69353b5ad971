#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek-V3错误诊断脚本
专门分析DeepSeek-V3的具体错误原因
"""

import sys
import os
import json
import requests
import time
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deepseek_v3_api_direct():
    """直接测试DeepSeek-V3 API调用"""
    print("🧪 直接测试DeepSeek-V3 API调用")
    
    try:
        # 阿里云DashScope配置
        api_key = "sk-a85369c572d34db5a9880547ebf0a021"
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试DeepSeek-V3请求
        payload = {
            "model": "deepseek-v3",
            "messages": [
                {
                    "role": "user", 
                    "content": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象，请分析故障原因"
                }
            ],
            "temperature": 0.7,
            "max_tokens": 2000,
            "stream": True
        }
        
        print(f"🌊 发送请求到: {base_url}/chat/completions")
        print(f"🌊 模型: {payload['model']}")
        print(f"🌊 流式: {payload['stream']}")
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            stream=True,
            timeout=60
        )
        
        print(f"🌊 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API调用失败: {response.text}")
            return False
        
        # 分析流式响应格式
        content_chunks = []
        reasoning_chunks = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    if data_str.strip() == '[DONE]':
                        print(f"🏁 流式完成")
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            
                            # 检查reasoning_content字段
                            if 'reasoning_content' in delta and delta['reasoning_content']:
                                reasoning_chunk = delta['reasoning_content']
                                reasoning_chunks.append(reasoning_chunk)
                                print(f"🧠 推理chunk {len(reasoning_chunks)}: {len(reasoning_chunk)} 字符")
                            
                            # 检查content字段
                            if 'content' in delta and delta['content']:
                                content_chunk = delta['content']
                                content_chunks.append(content_chunk)
                                print(f"📋 内容chunk {len(content_chunks)}: {len(content_chunk)} 字符")
                        
                        # 限制测试时间，避免过长
                        if chunk_count > 30:
                            print("⏰ 达到测试chunk限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        print(f"   原始数据: {data_str[:100]}...")
                        continue
        
        # 分析结果
        print(f"\n📊 DeepSeek-V3 API响应分析:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理chunks: {len(reasoning_chunks)}")
        print(f"   内容chunks: {len(content_chunks)}")
        
        if reasoning_chunks:
            total_reasoning = ''.join(reasoning_chunks)
            print(f"   推理总长度: {len(total_reasoning)} 字符")
            print(f"   推理内容预览: {total_reasoning[:200]}...")
        
        if content_chunks:
            total_content = ''.join(content_chunks)
            print(f"   内容总长度: {len(total_content)} 字符")
            print(f"   内容预览: {total_content[:200]}...")
        
        # 判断API调用是否成功
        if content_chunks or reasoning_chunks:
            print("✅ DeepSeek-V3 API调用成功")
            return True
        else:
            print("❌ DeepSeek-V3 API没有返回有效内容")
            return False
        
    except Exception as e:
        print(f"❌ DeepSeek-V3 API测试失败: {e}")
        traceback.print_exc()
        return False

def test_web_interface_v3():
    """测试Web界面的DeepSeek-V3处理"""
    print("\n🧪 测试Web界面DeepSeek-V3处理")
    
    try:
        # 测试健康检查
        health_response = requests.get("http://localhost:5002/health", timeout=10)
        if health_response.status_code != 200:
            print("❌ Web服务器未启动")
            return False
        
        print("✅ Web服务器运行正常")
        
        # 测试DeepSeek-V3流式分析接口
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象",
            "thinking_mode": False  # V3模式
        }
        
        print(f"🔍 发送DeepSeek-V3测试请求...")
        print(f"   thinking_mode: {test_data['thinking_mode']}")
        
        # 使用流式请求测试
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        print(f"🌊 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 流式分析请求失败: {response.status_code}")
            try:
                error_text = response.text
                print(f"   错误内容: {error_text}")
            except:
                print("   无法读取错误内容")
            return False
        
        # 分析流式响应
        final_parts = []
        reasoning_parts = []
        error_parts = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        chunk_type = chunk_data.get('type', 'unknown')
                        print(f"📦 收到chunk {chunk_count}: type={chunk_type}")
                        
                        if chunk_data.get('type') == 'reasoning':
                            content = chunk_data.get('content', '')
                            reasoning_parts.append(content)
                            print(f"🧠 推理内容: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'final':
                            content = chunk_data.get('content', '')
                            final_parts.append(content)
                            print(f"📋 最终内容: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'complete':
                            print("🏁 分析完成")
                            break
                        
                        elif chunk_data.get('type') == 'error':
                            error_msg = chunk_data.get('message', '未知错误')
                            error_parts.append(error_msg)
                            print(f"❌ 错误: {error_msg}")
                            return False
                        
                        # 限制测试时间
                        if chunk_count > 50:
                            print("⏰ 达到测试限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        print(f"   原始数据: {data_str[:100]}...")
                        continue
        
        # 分析结果
        print(f"\n📊 Web界面DeepSeek-V3处理结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理部分数: {len(reasoning_parts)}")
        print(f"   最终部分数: {len(final_parts)}")
        print(f"   错误数: {len(error_parts)}")
        
        success = True
        
        if error_parts:
            print(f"❌ 发现错误: {error_parts}")
            success = False
        
        if final_parts:
            total_final = ''.join(final_parts)
            print(f"✅ 最终内容总长度: {len(total_final)} 字符")
            print(f"   内容预览: {total_final[:200]}...")
        else:
            print("❌ 没有收到最终内容")
            success = False
        
        if reasoning_parts:
            print(f"⚠️ DeepSeek-V3收到了推理内容: {len(reasoning_parts)} 部分")
            # 这可能是正常的，因为阿里云API可能返回reasoning_content
        
        return success
        
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        traceback.print_exc()
        return False

def test_model_configuration():
    """测试模型配置"""
    print("\n🧪 测试模型配置")
    
    try:
        # 检查环境变量和配置
        import os
        
        api_key = os.getenv('DEEPSEEK_API_KEY')
        print(f"🔑 环境变量API密钥: {'已设置' if api_key else '未设置'}")
        
        # 检查配置文件
        try:
            from ui.app import DEEPSEEK_API_KEY, DEEPSEEK_CHAT_MODEL, DEEPSEEK_R1_MODEL
            print(f"🔑 应用API密钥: {'已配置' if DEEPSEEK_API_KEY else '未配置'}")
            print(f"🤖 V3模型名称: {DEEPSEEK_CHAT_MODEL}")
            print(f"🧠 R1模型名称: {DEEPSEEK_R1_MODEL}")
        except Exception as e:
            print(f"❌ 配置导入失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 DeepSeek-V3错误诊断")
    print("=" * 60)
    
    # 运行诊断测试
    test_results = []
    
    print("第一步：测试模型配置")
    config_result = test_model_configuration()
    test_results.append(("模型配置", config_result))
    
    print("\n第二步：直接测试DeepSeek-V3 API")
    api_result = test_deepseek_v3_api_direct()
    test_results.append(("V3 API直接调用", api_result))
    
    print("\n第三步：测试Web界面V3处理")
    web_result = test_web_interface_v3()
    test_results.append(("Web界面V3处理", web_result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 诊断结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ DeepSeek-V3功能正常")
    else:
        print("❌ DeepSeek-V3存在问题，需要进一步修复")
        print("\n🔧 建议检查:")
        print("1. API密钥配置是否正确")
        print("2. 模型名称是否正确")
        print("3. 网络连接是否正常")
        print("4. 流式处理逻辑是否有误")
    
    return all_passed

if __name__ == "__main__":
    main()
