#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的Web服务器测试
专门测试DeepSeek-V3的Web界面处理
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def wait_for_server(max_wait=30):
    """等待服务器启动"""
    print("⏳ 等待Web服务器启动...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:5002/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ 服务器已启动 (等待了 {i+1} 秒)")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 4:
            print(f"   仍在等待... ({i+1}/{max_wait})")
    
    print(f"❌ 服务器在 {max_wait} 秒内未启动")
    return False

def test_deepseek_v3_detailed():
    """详细测试DeepSeek-V3"""
    print("🧪 详细测试DeepSeek-V3")
    
    if not wait_for_server():
        return False
    
    try:
        # 测试DeepSeek-V3流式分析
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象",
            "thinking_mode": False
        }
        
        print(f"🔍 发送DeepSeek-V3请求...")
        print(f"   URL: http://localhost:5002/api/v1/analyze_stream")
        print(f"   Data: {test_data}")
        
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        print(f"🌊 响应状态: {response.status_code}")
        print(f"🌊 响应头: {dict(response.headers)}")
        
        if response.status_code != 200:
            print(f"❌ 请求失败")
            print(f"   状态码: {response.status_code}")
            print(f"   响应文本: {response.text}")
            return False
        
        # 详细分析流式响应
        chunks_received = []
        chunk_count = 0
        
        print(f"🌊 开始读取流式响应...")
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                print(f"📦 原始行 {chunk_count + 1}: {line_str[:100]}...")
                
                if line_str.startswith('data: '):
                    data_str = line_str[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        chunks_received.append(chunk_data)
                        
                        chunk_type = chunk_data.get('type', 'unknown')
                        content_length = len(chunk_data.get('content', ''))
                        
                        print(f"✅ 解析成功 chunk {chunk_count}: type={chunk_type}, content_length={content_length}")
                        
                        if chunk_data.get('type') == 'error':
                            error_msg = chunk_data.get('message', '未知错误')
                            print(f"❌ 收到错误: {error_msg}")
                            return False
                        
                        elif chunk_data.get('type') == 'complete':
                            print("🏁 收到完成信号")
                            break
                        
                        # 限制测试
                        if chunk_count > 20:
                            print("⏰ 达到测试限制")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print(f"   数据: {data_str[:200]}...")
                        continue
        
        # 分析结果
        print(f"\n📊 详细分析结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   收到的chunk类型:")
        
        type_counts = {}
        for chunk in chunks_received:
            chunk_type = chunk.get('type', 'unknown')
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1
        
        for chunk_type, count in type_counts.items():
            print(f"     {chunk_type}: {count} 个")
        
        # 检查是否有final类型的chunk
        final_chunks = [c for c in chunks_received if c.get('type') == 'final']
        if final_chunks:
            total_content = ''.join([c.get('content', '') for c in final_chunks])
            print(f"✅ 收到final内容: {len(final_chunks)} 个chunk, 总长度: {len(total_content)} 字符")
            print(f"   内容预览: {total_content[:200]}...")
            return True
        else:
            print("❌ 没有收到final类型的chunk")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_logs():
    """检查服务器日志"""
    print("\n🧪 检查服务器日志")
    
    try:
        # 发送一个简单请求来触发日志
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json={"query": "测试", "thinking_mode": False},
            timeout=10
        )
        
        print(f"触发请求状态: {response.status_code}")
        
        # 这里我们无法直接读取服务器日志，但可以检查响应
        if response.status_code == 200:
            print("✅ 服务器响应正常")
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            print(f"   响应内容: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 服务器日志检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 详细Web服务器测试")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    print("第一步：详细测试DeepSeek-V3")
    v3_result = test_deepseek_v3_detailed()
    test_results.append(("DeepSeek-V3详细测试", v3_result))
    
    print("\n第二步：检查服务器日志")
    log_result = test_server_logs()
    test_results.append(("服务器日志检查", log_result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ DeepSeek-V3 Web界面功能正常")
    else:
        print("❌ DeepSeek-V3 Web界面存在问题")
        print("\n🔧 可能的问题:")
        print("1. 流式处理逻辑错误")
        print("2. 模型配置问题")
        print("3. API调用参数错误")
        print("4. 响应解析问题")
    
    return all_passed

if __name__ == "__main__":
    main()
