#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek-R1 最终修复测试
验证思考过程和最终结果的正确分离
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deepseek_r1_api_direct():
    """直接测试DeepSeek-R1 API的响应格式"""
    print("🧪 直接测试DeepSeek-R1 API响应格式")
    
    try:
        # 阿里云DashScope配置
        api_key = "sk-a85369c572d34db5a9880547ebf0a021"
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试请求
        payload = {
            "model": "deepseek-r1",
            "messages": [
                {
                    "role": "user", 
                    "content": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象，请分析故障原因"
                }
            ],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": True
        }
        
        print(f"🌊 发送请求到: {base_url}/chat/completions")
        print(f"🌊 模型: {payload['model']}")
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            stream=True,
            timeout=60
        )
        
        print(f"🌊 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API调用失败: {response.text}")
            return False
        
        # 分析流式响应格式
        reasoning_chunks = []
        content_chunks = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    if data_str.strip() == '[DONE]':
                        print(f"🏁 流式完成")
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            
                            # 检查reasoning_content字段
                            if 'reasoning_content' in delta and delta['reasoning_content']:
                                reasoning_chunk = delta['reasoning_content']
                                reasoning_chunks.append(reasoning_chunk)
                                print(f"🧠 推理chunk {len(reasoning_chunks)}: {len(reasoning_chunk)} 字符")
                                if len(reasoning_chunks) <= 3:  # 只显示前3个chunk的内容
                                    print(f"   内容预览: {reasoning_chunk[:100]}...")
                            
                            # 检查content字段
                            if 'content' in delta and delta['content']:
                                content_chunk = delta['content']
                                content_chunks.append(content_chunk)
                                print(f"📋 内容chunk {len(content_chunks)}: {len(content_chunk)} 字符")
                                if len(content_chunks) <= 3:  # 只显示前3个chunk的内容
                                    print(f"   内容预览: {content_chunk[:100]}...")
                        
                        # 限制测试时间，避免过长
                        if chunk_count > 50:
                            print("⏰ 达到测试chunk限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        continue
        
        # 分析结果
        print(f"\n📊 API响应分析结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理chunks: {len(reasoning_chunks)}")
        print(f"   内容chunks: {len(content_chunks)}")
        
        if reasoning_chunks:
            total_reasoning = ''.join(reasoning_chunks)
            print(f"   推理总长度: {len(total_reasoning)} 字符")
            print(f"   推理内容预览: {total_reasoning[:200]}...")
        
        if content_chunks:
            total_content = ''.join(content_chunks)
            print(f"   内容总长度: {len(total_content)} 字符")
            print(f"   内容预览: {total_content[:200]}...")
        
        # 验证分离效果
        success = True
        if not reasoning_chunks:
            print("❌ 没有收到推理内容")
            success = False
        
        if not content_chunks:
            print("❌ 没有收到最终内容")
            success = False
        
        if success:
            print("✅ API响应格式测试成功")
        
        return success
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_web_interface():
    """测试Web界面的处理效果"""
    print("\n🧪 测试Web界面处理效果")
    
    try:
        # 测试健康检查
        health_response = requests.get("http://localhost:5002/health", timeout=10)
        if health_response.status_code != 200:
            print("❌ Web服务器未启动")
            return False
        
        print("✅ Web服务器运行正常")
        
        # 测试DeepSeek-R1分析接口
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象",
            "thinking_mode": True
        }
        
        print(f"🔍 发送测试请求...")
        
        # 使用流式请求测试
        response = requests.post(
            "http://localhost:5002/analyze_stream",
            json=test_data,
            stream=True,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ 流式分析请求失败: {response.status_code}")
            return False
        
        # 分析流式响应
        reasoning_parts = []
        final_parts = []
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        if chunk_data.get('type') == 'reasoning':
                            content = chunk_data.get('content', '')
                            reasoning_parts.append(content)
                            print(f"🧠 推理部分 {len(reasoning_parts)}: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'final':
                            content = chunk_data.get('content', '')
                            final_parts.append(content)
                            print(f"📋 最终部分 {len(final_parts)}: {len(content)} 字符")
                        
                        elif chunk_data.get('type') == 'complete':
                            print("🏁 分析完成")
                            break
                        
                        elif chunk_data.get('type') == 'error':
                            print(f"❌ 分析错误: {chunk_data.get('message')}")
                            return False
                        
                        # 限制测试时间
                        if chunk_count > 30:
                            print("⏰ 达到测试限制，停止测试")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        continue
        
        # 分析结果
        print(f"\n📊 Web界面处理结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理部分数: {len(reasoning_parts)}")
        print(f"   最终部分数: {len(final_parts)}")
        
        success = True
        
        if reasoning_parts:
            total_reasoning = ''.join(reasoning_parts)
            print(f"   推理总长度: {len(total_reasoning)} 字符")
            print(f"   推理预览: {total_reasoning[:150]}...")
        else:
            print("❌ 没有收到推理内容")
            success = False
        
        if final_parts:
            total_final = ''.join(final_parts)
            print(f"   最终总长度: {len(total_final)} 字符")
            print(f"   最终预览: {total_final[:150]}...")
        else:
            print("❌ 没有收到最终内容")
            success = False
        
        if success:
            print("✅ Web界面处理测试成功")
        
        return success
        
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 DeepSeek-R1 最终修复测试")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    print("第一步：直接测试API响应格式")
    api_result = test_deepseek_r1_api_direct()
    test_results.append(("API响应格式", api_result))
    
    print("\n第二步：测试Web界面处理")
    web_result = test_web_interface()
    test_results.append(("Web界面处理", web_result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！DeepSeek-R1修复成功！")
        print("\n📋 修复要点:")
        print("✅ API正确返回reasoning_content和content字段")
        print("✅ Web界面正确分离推理过程和最终结果")
        print("✅ 流式输出实时显示思考过程和结论")
        print("✅ 符合DeepSeek-R1官方标准格式")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return all_passed

if __name__ == "__main__":
    main()
