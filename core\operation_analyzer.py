"""
运行方式分析器

分析电力系统运行方式和操作记录
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from data_processing.text_processor import TextProcessor


class OperationAnalyzer:
    """运行方式分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.text_processor = TextProcessor(config.get("data_processing", {}))
        
        # 初始化分析模式
        self._init_patterns()
    
    def _init_patterns(self):
        """初始化分析模式"""
        # 操作类型模式
        self.operation_patterns = {
            "投运": ["投运", "投入", "合闸", "启动", "开启"],
            "停运": ["停运", "退出", "分闸", "停止", "关闭"],
            "切换": ["切换", "转换", "倒闸", "换位"],
            "检修": ["检修", "维护", "保养", "试验"],
            "故障处理": ["故障", "跳闸", "保护动作", "事故"]
        }
        
        # 设备类型模式
        self.equipment_patterns = {
            "主变压器": ["主变", "主变压器", "1#主变", "2#主变"],
            "线路": ["线路", "输电线路", "配电线路", "馈线"],
            "母线": ["母线", "I母", "II母", "工作母线", "备用母线"],
            "断路器": ["断路器", "开关", "主开关"],
            "隔离开关": ["隔离开关", "刀闸"],
            "电容器": ["电容器", "补偿装置"],
            "发电机": ["发电机", "机组"]
        }
        
        # 运行状态模式
        self.status_patterns = {
            "正常运行": ["正常运行", "运行正常", "正常"],
            "热备用": ["热备用", "备用"],
            "冷备用": ["冷备用", "停用备用"],
            "检修": ["检修", "停电检修", "计划检修"],
            "故障": ["故障", "异常", "跳闸"]
        }
        
        # 时间模式
        self.time_patterns = {
            "datetime": re.compile(r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?\s*\d{1,2}[：:]\d{1,2}(?:[：:]\d{1,2})?)')
        }
        
        # 数值模式
        self.value_patterns = {
            "voltage": re.compile(r'(\d+\.?\d*)\s*[kK]?[vV]'),
            "current": re.compile(r'(\d+\.?\d*)\s*[aA]'),
            "power": re.compile(r'(\d+\.?\d*)\s*[mM]?[wW]'),
            "frequency": re.compile(r'(\d+\.?\d*)\s*[hH][zZ]')
        }
    
    def analyze_operation_mode(self, content: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        分析运行方式
        
        Args:
            content: 运行方式描述或操作记录
            context: 上下文信息（设备信息、历史数据等）
            
        Returns:
            分析结果
        """
        try:
            logger.info("开始分析运行方式")
            
            # 文本预处理
            cleaned_content = self.text_processor.clean_text(content)
            
            # 解析操作序列
            operations = self._parse_operations(cleaned_content)
            
            # 分析设备状态
            equipment_status = self._analyze_equipment_status(cleaned_content, operations)
            
            # 分析运行拓扑
            topology = self._analyze_topology(equipment_status, operations)
            
            # 风险评估
            risks = self._assess_risks(operations, equipment_status, topology)
            
            # 生成建议
            recommendations = self._generate_recommendations(operations, equipment_status, risks)
            
            result = {
                "success": True,
                "operations": operations,
                "equipment_status": equipment_status,
                "topology": topology,
                "risks": risks,
                "recommendations": recommendations,
                "analysis_time": datetime.now().isoformat(),
                "raw_content": content
            }
            
            logger.info("运行方式分析完成")
            return result
            
        except Exception as e:
            logger.error(f"运行方式分析失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _parse_operations(self, content: str) -> List[Dict[str, Any]]:
        """解析操作序列"""
        operations = []
        
        # 按行分割
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if not line.strip():
                continue
            
            # 提取时间
            time_match = self.time_patterns["datetime"].search(line)
            operation_time = time_match.group(1) if time_match else None
            
            # 识别操作类型
            operation_type = None
            for op_type, patterns in self.operation_patterns.items():
                if any(pattern in line for pattern in patterns):
                    operation_type = op_type
                    break
            
            # 识别设备
            equipment = []
            for eq_type, patterns in self.equipment_patterns.items():
                if any(pattern in line for pattern in patterns):
                    equipment.append(eq_type)
            
            # 提取参数值
            parameters = self._extract_parameters(line)
            
            if operation_type or equipment:
                operations.append({
                    "sequence": i + 1,
                    "time": operation_time,
                    "type": operation_type,
                    "equipment": equipment,
                    "parameters": parameters,
                    "description": line.strip(),
                    "line_number": i + 1
                })
        
        return operations
    
    def _extract_parameters(self, text: str) -> Dict[str, Any]:
        """提取操作参数"""
        parameters = {}
        
        # 提取各种数值参数
        for param_type, pattern in self.value_patterns.items():
            matches = pattern.findall(text)
            if matches:
                parameters[param_type] = [float(match) for match in matches]
        
        return parameters
    
    def _analyze_equipment_status(self, content: str, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析设备状态"""
        equipment_status = {}
        
        # 从操作序列推断设备状态变化
        for operation in operations:
            for equipment in operation.get("equipment", []):
                if equipment not in equipment_status:
                    equipment_status[equipment] = {
                        "current_status": "未知",
                        "status_history": [],
                        "last_operation": None
                    }
                
                # 根据操作类型推断状态
                op_type = operation.get("type")
                if op_type == "投运":
                    new_status = "正常运行"
                elif op_type == "停运":
                    new_status = "停运"
                elif op_type == "检修":
                    new_status = "检修"
                elif op_type == "故障处理":
                    new_status = "故障"
                else:
                    new_status = "未知"
                
                # 更新状态
                if new_status != "未知":
                    equipment_status[equipment]["status_history"].append({
                        "status": new_status,
                        "time": operation.get("time"),
                        "operation": operation.get("description")
                    })
                    equipment_status[equipment]["current_status"] = new_status
                
                equipment_status[equipment]["last_operation"] = operation
        
        return equipment_status
    
    def _analyze_topology(self, equipment_status: Dict[str, Any], operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析运行拓扑"""
        topology = {
            "connections": [],
            "power_flow": [],
            "backup_paths": [],
            "isolated_equipment": []
        }
        
        # 分析设备连接关系
        running_equipment = [
            eq for eq, status in equipment_status.items()
            if status.get("current_status") == "正常运行"
        ]
        
        # 简化的拓扑分析
        if "主变压器" in running_equipment and "母线" in running_equipment:
            topology["connections"].append({
                "from": "主变压器",
                "to": "母线",
                "type": "主接线"
            })
        
        # 识别备用路径
        backup_equipment = [
            eq for eq, status in equipment_status.items()
            if status.get("current_status") in ["热备用", "冷备用"]
        ]
        
        topology["backup_paths"] = backup_equipment
        
        # 识别孤立设备
        isolated_equipment = [
            eq for eq, status in equipment_status.items()
            if status.get("current_status") in ["停运", "检修"]
        ]
        
        topology["isolated_equipment"] = isolated_equipment
        
        return topology
    
    def _assess_risks(self, operations: List[Dict[str, Any]], equipment_status: Dict[str, Any], topology: Dict[str, Any]) -> List[Dict[str, Any]]:
        """风险评估"""
        risks = []
        
        # 检查单点故障风险
        running_critical_equipment = [
            eq for eq in ["主变压器", "母线"]
            if equipment_status.get(eq, {}).get("current_status") == "正常运行"
        ]
        
        if len(running_critical_equipment) < 2:
            risks.append({
                "type": "单点故障风险",
                "level": "高",
                "description": "关键设备数量不足，存在单点故障风险",
                "affected_equipment": running_critical_equipment
            })
        
        # 检查备用设备状态
        backup_equipment_count = len(topology.get("backup_paths", []))
        if backup_equipment_count == 0:
            risks.append({
                "type": "备用不足",
                "level": "中",
                "description": "缺少备用设备，系统可靠性降低",
                "affected_equipment": []
            })
        
        # 检查操作风险
        recent_operations = [op for op in operations if op.get("type") in ["投运", "停运", "切换"]]
        if len(recent_operations) > 5:
            risks.append({
                "type": "操作频繁",
                "level": "中",
                "description": "近期操作频繁，增加误操作风险",
                "affected_equipment": []
            })
        
        # 检查设备状态风险
        fault_equipment = [
            eq for eq, status in equipment_status.items()
            if status.get("current_status") == "故障"
        ]
        
        if fault_equipment:
            risks.append({
                "type": "设备故障",
                "level": "高",
                "description": f"设备故障影响系统安全运行",
                "affected_equipment": fault_equipment
            })
        
        return risks
    
    def _generate_recommendations(self, operations: List[Dict[str, Any]], equipment_status: Dict[str, Any], risks: List[Dict[str, Any]]) -> List[str]:
        """生成运行建议"""
        recommendations = []
        
        # 基于风险生成建议
        for risk in risks:
            if risk.get("type") == "单点故障风险":
                recommendations.append("建议投入备用设备，提高系统可靠性")
            elif risk.get("type") == "备用不足":
                recommendations.append("建议检查备用设备状态，确保可用性")
            elif risk.get("type") == "操作频繁":
                recommendations.append("建议优化操作计划，减少不必要的操作")
            elif risk.get("type") == "设备故障":
                recommendations.append("建议立即处理故障设备，恢复正常运行")
        
        # 基于设备状态生成建议
        maintenance_equipment = [
            eq for eq, status in equipment_status.items()
            if status.get("current_status") == "检修"
        ]
        
        if maintenance_equipment:
            recommendations.append(f"建议加快{', '.join(maintenance_equipment)}检修进度")
        
        # 基于操作序列生成建议
        if len(operations) > 0:
            last_operation = operations[-1]
            if last_operation.get("type") == "故障处理":
                recommendations.append("建议分析故障原因，制定预防措施")
        
        return recommendations
    
    def analyze_switching_sequence(self, sequence: List[str]) -> Dict[str, Any]:
        """
        分析倒闸操作序列
        
        Args:
            sequence: 操作步骤列表
            
        Returns:
            分析结果
        """
        try:
            logger.info("开始分析倒闸操作序列")
            
            result = {
                "success": True,
                "total_steps": len(sequence),
                "analyzed_steps": [],
                "risks": [],
                "recommendations": []
            }
            
            for i, step in enumerate(sequence):
                step_analysis = {
                    "step_number": i + 1,
                    "description": step,
                    "operation_type": None,
                    "equipment": [],
                    "risks": [],
                    "precautions": []
                }
                
                # 分析操作类型
                for op_type, patterns in self.operation_patterns.items():
                    if any(pattern in step for pattern in patterns):
                        step_analysis["operation_type"] = op_type
                        break
                
                # 识别设备
                for eq_type, patterns in self.equipment_patterns.items():
                    if any(pattern in step for pattern in patterns):
                        step_analysis["equipment"].append(eq_type)
                
                # 风险分析
                if "合闸" in step or "投入" in step:
                    step_analysis["risks"].append("带电操作风险")
                    step_analysis["precautions"].append("确认设备状态正常")
                
                if "分闸" in step or "断开" in step:
                    step_analysis["risks"].append("负荷转移风险")
                    step_analysis["precautions"].append("确认负荷已转移")
                
                result["analyzed_steps"].append(step_analysis)
            
            # 整体风险评估
            high_risk_steps = [
                step for step in result["analyzed_steps"]
                if step.get("risks")
            ]
            
            if len(high_risk_steps) > len(sequence) * 0.5:
                result["risks"].append("操作序列风险较高，建议详细核查")
            
            # 生成建议
            result["recommendations"].extend([
                "操作前应核对设备状态",
                "严格按照操作票执行",
                "操作过程中保持通信畅通",
                "异常情况立即停止操作"
            ])
            
            logger.info("倒闸操作序列分析完成")
            return result
            
        except Exception as e:
            logger.error(f"倒闸操作序列分析失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def generate_operation_report(self, analysis_result: Dict[str, Any]) -> str:
        """
        生成运行方式分析报告
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            报告文本
        """
        try:
            report_lines = []
            
            # 报告标题
            report_lines.append("# 运行方式分析报告")
            report_lines.append(f"分析时间: {analysis_result.get('analysis_time', 'N/A')}")
            report_lines.append("")
            
            # 操作概况
            operations = analysis_result.get("operations", [])
            report_lines.append("## 操作概况")
            report_lines.append(f"总操作数: {len(operations)}")
            
            if operations:
                op_types = {}
                for op in operations:
                    op_type = op.get("type", "未知")
                    op_types[op_type] = op_types.get(op_type, 0) + 1
                
                for op_type, count in op_types.items():
                    report_lines.append(f"- {op_type}: {count}次")
            
            report_lines.append("")
            
            # 设备状态
            equipment_status = analysis_result.get("equipment_status", {})
            report_lines.append("## 设备状态")
            
            for equipment, status in equipment_status.items():
                current_status = status.get("current_status", "未知")
                report_lines.append(f"- {equipment}: {current_status}")
            
            report_lines.append("")
            
            # 风险评估
            risks = analysis_result.get("risks", [])
            report_lines.append("## 风险评估")
            
            if risks:
                for risk in risks:
                    risk_type = risk.get("type", "未知风险")
                    risk_level = risk.get("level", "未知")
                    description = risk.get("description", "")
                    report_lines.append(f"- **{risk_type}** (风险等级: {risk_level})")
                    report_lines.append(f"  {description}")
            else:
                report_lines.append("未发现明显风险")
            
            report_lines.append("")
            
            # 建议措施
            recommendations = analysis_result.get("recommendations", [])
            report_lines.append("## 建议措施")
            
            for i, recommendation in enumerate(recommendations, 1):
                report_lines.append(f"{i}. {recommendation}")
            
            return "\n".join(report_lines)
            
        except Exception as e:
            logger.error(f"生成运行方式分析报告失败: {str(e)}")
            return f"报告生成失败: {str(e)}"
