<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - 故障分析智能助手</title>
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        .container-fluid { width: 100%; padding: 15px; }
        .navbar { display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 1rem; background-color: #0d6efd; }
        .navbar-brand { color: white; font-size: 1.25rem; text-decoration: none; }
        .card { border: 1px solid #dee2e6; border-radius: 0.25rem; margin-bottom: 1rem; }
        .card-header { padding: 0.75rem 1rem; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold; }
        .card-body { padding: 1rem; }
        .form-control { width: 100%; padding: 0.375rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.25rem; margin-bottom: 0.5rem; }
        .btn { padding: 0.375rem 0.75rem; border: 1px solid transparent; border-radius: 0.25rem; cursor: pointer; margin-right: 0.5rem; }
        .btn-primary { background-color: #0d6efd; color: white; border-color: #0d6efd; }
        .btn-success { background-color: #198754; color: white; border-color: #198754; }
        .btn-warning { background-color: #ffc107; color: black; border-color: #ffc107; }
        .btn-danger { background-color: #dc3545; color: white; border-color: #dc3545; }
        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 0.75rem; border-top: 1px solid #dee2e6; }
        .table thead th { border-bottom: 2px solid #dee2e6; background-color: #f8f9fa; }
        .badge { padding: 0.25em 0.4em; font-size: 0.75em; border-radius: 0.25rem; }
        .badge-success { background-color: #198754; color: white; }
        .badge-warning { background-color: #ffc107; color: black; }
        .badge-danger { background-color: #dc3545; color: white; }
        .badge-secondary { background-color: #6c757d; color: white; }
        .modal { position: fixed; top: 0; left: 0; z-index: 1050; display: none; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
        .modal.show { display: block; }
        .modal-dialog { position: relative; width: auto; margin: 1.75rem; max-width: 500px; margin-left: auto; margin-right: auto; }
        .modal-content { background-color: white; border-radius: 0.3rem; padding: 1rem; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; }
        .modal-title { font-size: 1.25rem; font-weight: bold; }
        .btn-close { background: none; border: none; font-size: 1.5rem; cursor: pointer; }
        .d-none { display: none; }
        .text-center { text-align: center; }
        .mb-3 { margin-bottom: 1rem; }
        .row { display: flex; flex-wrap: wrap; margin: -0.5rem; }
        .col-md-6 { flex: 0 0 50%; max-width: 50%; padding: 0.5rem; }
        .col-12 { flex: 0 0 100%; max-width: 100%; padding: 0.5rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <a class="navbar-brand" href="/">⚙️ 故障分析智能助手</a>
        <div>
            <a href="/" style="color: white; text-decoration: none; margin-right: 1rem;">首页</a>
            <a href="/fault-analysis" style="color: white; text-decoration: none; margin-right: 1rem;">故障分析</a>
            <a href="/knowledge-base" style="color: white; text-decoration: none;">知识库</a>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2>⚙️ 设备管理</h2>
                <p class="text-muted">电力设备信息管理与状态监控</p>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索设备..." style="width: 300px; display: inline-block;">
                                <button class="btn btn-primary" onclick="searchEquipment()">搜索</button>
                                <button class="btn btn-success" onclick="refreshEquipmentList()">刷新</button>
                            </div>
                            <div>
                                <button class="btn btn-success" onclick="showAddEquipmentModal()">+ 添加设备</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备统计 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">设备统计</div>
                    <div class="card-body">
                        <div id="equipmentStats">
                            <div class="text-center">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">状态分布</div>
                    <div class="card-body">
                        <div id="statusStats">
                            <div class="text-center">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">设备列表</div>
                    <div class="card-body">
                        <div id="equipmentTableContainer">
                            <div class="text-center">加载设备数据中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加设备模态框 -->
    <div class="modal" id="addEquipmentModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新设备</h5>
                    <button type="button" class="btn-close" onclick="hideAddEquipmentModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addEquipmentForm">
                        <div class="mb-3">
                            <label class="form-label">设备名称 *</label>
                            <input type="text" class="form-control" id="equipmentName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">设备类型 *</label>
                            <select class="form-control" id="equipmentType" required>
                                <option value="">请选择</option>
                                <option value="transformer">变压器</option>
                                <option value="breaker">断路器</option>
                                <option value="switch">开关</option>
                                <option value="cable">电缆</option>
                                <option value="capacitor">电容器</option>
                                <option value="arrester">避雷器</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">安装位置 *</label>
                            <input type="text" class="form-control" id="equipmentLocation" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">运行状态 *</label>
                            <select class="form-control" id="equipmentStatus" required>
                                <option value="">请选择</option>
                                <option value="running">运行中</option>
                                <option value="maintenance">检修中</option>
                                <option value="standby">备用</option>
                                <option value="fault">故障</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">制造商</label>
                            <input type="text" class="form-control" id="equipmentManufacturer">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">型号</label>
                            <input type="text" class="form-control" id="equipmentModel">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideAddEquipmentModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addEquipment()">添加设备</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let equipmentData = [];

        // 页面加载时获取设备列表
        window.addEventListener('load', function() {
            loadEquipmentList();
        });

        async function loadEquipmentList() {
            try {
                const response = await fetch('/api/v1/equipment');
                const result = await response.json();
                
                if (result.success) {
                    equipmentData = result.equipment;
                    displayEquipmentTable(equipmentData);
                    displayStatistics(result.statistics);
                } else {
                    console.error('获取设备列表失败:', result.error);
                    document.getElementById('equipmentTableContainer').innerHTML = 
                        '<div class="text-center text-danger">获取设备数据失败</div>';
                }
            } catch (error) {
                console.error('请求失败:', error);
                document.getElementById('equipmentTableContainer').innerHTML = 
                    '<div class="text-center text-danger">网络请求失败</div>';
            }
        }

        function displayEquipmentTable(equipment) {
            const container = document.getElementById('equipmentTableContainer');
            
            if (equipment.length === 0) {
                container.innerHTML = '<div class="text-center">暂无设备数据</div>';
                return;
            }

            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>设备名称</th>
                            <th>类型</th>
                            <th>位置</th>
                            <th>状态</th>
                            <th>制造商</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${equipment.map(eq => `
                            <tr>
                                <td>${eq.id || 'N/A'}</td>
                                <td>${eq.name || 'N/A'}</td>
                                <td>${eq.type || 'N/A'}</td>
                                <td>${eq.location || 'N/A'}</td>
                                <td><span class="badge ${getStatusBadgeClass(eq.status)}">${getStatusText(eq.status)}</span></td>
                                <td>${eq.manufacturer || 'N/A'}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewEquipment('${eq.id}')">查看</button>
                                    <button class="btn btn-sm btn-warning" onclick="editEquipment('${eq.id}')">编辑</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteEquipment('${eq.id}')">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = tableHTML;
        }

        function displayStatistics(stats) {
            const equipmentStats = document.getElementById('equipmentStats');
            const statusStats = document.getElementById('statusStats');
            
            equipmentStats.innerHTML = `
                <div><strong>总设备数:</strong> ${stats.total || 0}</div>
                <div><strong>设备类型:</strong> ${Object.keys(stats.type || {}).length}</div>
            `;
            
            const statusHTML = Object.entries(stats.status || {}).map(([status, count]) => 
                `<div><span class="badge ${getStatusBadgeClass(status)}">${getStatusText(status)}</span> ${count}</div>`
            ).join('');
            
            statusStats.innerHTML = statusHTML || '<div>暂无状态数据</div>';
        }

        function getStatusBadgeClass(status) {
            const statusMap = {
                'running': 'badge-success',
                'maintenance': 'badge-warning', 
                'standby': 'badge-secondary',
                'fault': 'badge-danger'
            };
            return statusMap[status] || 'badge-secondary';
        }

        function getStatusText(status) {
            const statusMap = {
                'running': '运行中',
                'maintenance': '检修中',
                'standby': '备用',
                'fault': '故障'
            };
            return statusMap[status] || status || '未知';
        }

        function showAddEquipmentModal() {
            document.getElementById('addEquipmentModal').classList.add('show');
        }

        function hideAddEquipmentModal() {
            document.getElementById('addEquipmentModal').classList.remove('show');
            document.getElementById('addEquipmentForm').reset();
        }

        async function addEquipment() {
            const form = document.getElementById('addEquipmentForm');
            const formData = new FormData(form);
            
            const equipmentData = {
                name: document.getElementById('equipmentName').value,
                type: document.getElementById('equipmentType').value,
                location: document.getElementById('equipmentLocation').value,
                status: document.getElementById('equipmentStatus').value,
                manufacturer: document.getElementById('equipmentManufacturer').value,
                model: document.getElementById('equipmentModel').value
            };

            try {
                const response = await fetch('/api/v1/equipment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(equipmentData)
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('设备添加成功');
                    hideAddEquipmentModal();
                    loadEquipmentList();
                } else {
                    alert('添加失败: ' + result.error);
                }
            } catch (error) {
                console.error('添加设备失败:', error);
                alert('添加设备失败，请检查网络连接');
            }
        }

        function searchEquipment() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const filtered = equipmentData.filter(eq => 
                (eq.name || '').toLowerCase().includes(query) ||
                (eq.type || '').toLowerCase().includes(query) ||
                (eq.location || '').toLowerCase().includes(query)
            );
            displayEquipmentTable(filtered);
        }

        function refreshEquipmentList() {
            loadEquipmentList();
        }

        function viewEquipment(id) {
            alert('查看设备功能开发中: ' + id);
        }

        function editEquipment(id) {
            alert('编辑设备功能开发中: ' + id);
        }

        function deleteEquipment(id) {
            if (confirm('确定要删除这个设备吗？')) {
                alert('删除设备功能开发中: ' + id);
            }
        }
    </script>
</body>
</html>
