// 文件上传功能测试脚本
// 用于在浏览器中测试文件上传和结果显示

(function() {
    'use strict';
    
    // 测试数据生成器
    const testDataGenerator = {
        // 生成文档上传响应
        generateDocumentResponse: () => ({
            message: 'Document uploaded successfully',
            document_info: {
                document_id: 'doc-test-12345',
                filename: 'test_document.pdf',
                size: 2048000,
                type: 'application/pdf',
                extension: '.pdf',
                status: 'processing',
                timestamp: new Date().toISOString(),
                processing_status: 'extracting_text',
                estimated_completion: '2-5分钟'
            }
        }),
        
        // 生成图像上传响应
        generateImageResponse: () => ({
            message: 'Image uploaded and processed successfully',
            image_info: {
                image_id: 'img-test-67890',
                filename: 'test_image.jpg',
                size: 1024000,
                type: 'image/jpeg',
                extension: '.jpg',
                status: 'processed',
                timestamp: new Date().toISOString(),
                processing_results: {
                    dimensions: '1920x1080',
                    color_space: 'RGB',
                    quality: 'high',
                    features_detected: ['edges', 'textures', 'objects']
                }
            }
        }),
        
        // 生成OCR响应
        generateOCRResponse: () => ({
            message: 'OCR processing completed successfully',
            ocr_info: {
                ocr_id: 'ocr-test-11111',
                filename: 'test_ocr.png',
                size: 512000,
                type: 'image/png',
                extension: '.png',
                status: 'completed',
                timestamp: new Date().toISOString(),
                ocr_results: {
                    text_content: '设备编号: T001\n故障类型: 过载\n发生时间: 2024-01-15 14:30\n处理状态: 待处理\n负责人: 张工程师',
                    confidence: 0.95,
                    language: 'zh-CN',
                    word_count: 28,
                    processing_time: '2.3秒'
                }
            }
        }),
        
        // 生成缺陷检测响应
        generateDefectResponse: () => ({
            message: 'Defect detection completed successfully',
            defect_info: {
                detection_id: 'defect-test-22222',
                filename: 'test_defect.jpg',
                size: 1536000,
                type: 'image/jpeg',
                extension: '.jpg',
                status: 'completed',
                timestamp: new Date().toISOString(),
                detection_results: {
                    defects_found: 3,
                    defect_types: ['裂纹', '腐蚀', '磨损'],
                    severity_levels: ['中等', '轻微', '严重'],
                    confidence_scores: [0.89, 0.76, 0.94],
                    processing_time: '5.2秒',
                    recommendations: [
                        '建议立即检修严重磨损部位',
                        '定期监控腐蚀区域',
                        '关注裂纹扩展情况'
                    ]
                }
            }
        }),
        
        // 生成波形分析响应
        generateWaveformResponse: () => ({
            message: 'Waveform analysis completed successfully',
            waveform_info: {
                analysis_id: 'wave-test-33333',
                filename: 'test_waveform.csv',
                size: 256000,
                type: 'text/csv',
                extension: '.csv',
                status: 'completed',
                timestamp: new Date().toISOString(),
                analysis_results: {
                    sample_rate: '1000 Hz',
                    duration: '10.5秒',
                    frequency_components: ['50Hz', '150Hz', '250Hz'],
                    amplitude_range: '±5V',
                    anomalies_detected: 2,
                    anomaly_types: ['频率偏移', '幅值异常'],
                    processing_time: '3.8秒',
                    quality_score: 0.87,
                    recommendations: [
                        '检查50Hz基频稳定性',
                        '监控幅值波动原因',
                        '建议进行频谱分析'
                    ]
                }
            }
        }),
        
        // 生成批量上传响应
        generateBatchResponse: () => ({
            message: '批量上传成功，共处理 3 个文件',
            upload_results: [
                {
                    upload_id: 'batch-test-1',
                    filename: 'file1.txt',
                    size: 1024,
                    type: 'text/plain',
                    status: 'uploaded',
                    timestamp: new Date().toISOString()
                },
                {
                    upload_id: 'batch-test-2',
                    filename: 'file2.pdf',
                    size: 2048,
                    type: 'application/pdf',
                    status: 'uploaded',
                    timestamp: new Date().toISOString()
                },
                {
                    upload_id: 'batch-test-3',
                    filename: 'file3.jpg',
                    size: 4096,
                    type: 'image/jpeg',
                    status: 'uploaded',
                    timestamp: new Date().toISOString()
                }
            ],
            total_files: 3
        })
    };
    
    // 测试函数
    const uploadTester = {
        // 测试所有上传类型的显示效果
        testAllUploadDisplays: function() {
            console.group('🧪 文件上传显示测试');
            
            const testCases = [
                { name: '文档上传', generator: testDataGenerator.generateDocumentResponse },
                { name: '图像上传', generator: testDataGenerator.generateImageResponse },
                { name: 'OCR识别', generator: testDataGenerator.generateOCRResponse },
                { name: '缺陷检测', generator: testDataGenerator.generateDefectResponse },
                { name: '波形分析', generator: testDataGenerator.generateWaveformResponse },
                { name: '批量上传', generator: testDataGenerator.generateBatchResponse }
            ];
            
            testCases.forEach((testCase, index) => {
                setTimeout(() => {
                    console.log(`测试 ${testCase.name}...`);
                    const testData = testCase.generator();
                    
                    // 检查 displayUploadResults 函数是否存在
                    if (typeof window.displayUploadResults === 'function') {
                        try {
                            window.displayUploadResults(testData);
                            console.log(`✅ ${testCase.name} 显示成功`);
                        } catch (error) {
                            console.error(`❌ ${testCase.name} 显示失败:`, error);
                        }
                    } else {
                        console.error('❌ displayUploadResults 函数未找到');
                    }
                }, index * 3000); // 每3秒测试一个
            });
            
            console.groupEnd();
        },
        
        // 测试单个上传类型
        testSingleUpload: function(type) {
            const generators = {
                'document': testDataGenerator.generateDocumentResponse,
                'image': testDataGenerator.generateImageResponse,
                'ocr': testDataGenerator.generateOCRResponse,
                'defect': testDataGenerator.generateDefectResponse,
                'waveform': testDataGenerator.generateWaveformResponse,
                'batch': testDataGenerator.generateBatchResponse
            };
            
            if (generators[type]) {
                const testData = generators[type]();
                console.log(`测试 ${type} 上传显示:`, testData);
                
                if (typeof window.displayUploadResults === 'function') {
                    window.displayUploadResults(testData);
                } else {
                    console.error('displayUploadResults 函数未找到');
                }
            } else {
                console.error('未知的上传类型:', type);
            }
        },
        
        // 清空上传结果显示
        clearResults: function() {
            const resultsContainer = document.getElementById('upload-results');
            if (resultsContainer) {
                resultsContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-cloud-upload" style="font-size: 3rem;"></i>
                        <p class="mt-2">请选择文件并上传</p>
                    </div>
                `;
                console.log('✅ 上传结果已清空');
            } else {
                console.error('❌ 未找到上传结果容器');
            }
        }
    };
    
    // 暴露测试工具到全局
    window.uploadTester = uploadTester;
    
    // 页面加载完成后提供测试提示
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.group('📋 文件上传测试工具已加载');
                console.log('使用方法:');
                console.log('- uploadTester.testAllUploadDisplays() - 测试所有上传类型显示');
                console.log('- uploadTester.testSingleUpload("document") - 测试单个类型');
                console.log('- uploadTester.clearResults() - 清空结果显示');
                console.log('可用类型: document, image, ocr, defect, waveform, batch');
                console.groupEnd();
            }, 1000);
        });
    }
    
})();
