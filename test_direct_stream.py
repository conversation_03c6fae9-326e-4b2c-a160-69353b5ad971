#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试流式API
绕过健康检查，直接测试DeepSeek-V3流式功能
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deepseek_v3_stream_direct():
    """直接测试DeepSeek-V3流式API"""
    print("🧪 直接测试DeepSeek-V3流式API")
    
    try:
        # 测试DeepSeek-V3流式分析
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象",
            "thinking_mode": False
        }
        
        print(f"🔍 发送DeepSeek-V3请求...")
        print(f"   URL: http://localhost:5002/api/v1/analyze_stream")
        print(f"   thinking_mode: {test_data['thinking_mode']}")
        
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        print(f"🌊 响应状态: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
        
        # 分析流式响应
        final_chunks = []
        reasoning_chunks = []
        error_chunks = []
        chunk_count = 0
        
        print(f"🌊 开始读取流式响应...")
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                
                if line_str.startswith('data: '):
                    data_str = line_str[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        chunk_type = chunk_data.get('type', 'unknown')
                        content = chunk_data.get('content', '')
                        
                        print(f"📦 Chunk {chunk_count}: type={chunk_type}, length={len(content)}")
                        
                        if chunk_type == 'reasoning':
                            reasoning_chunks.append(content)
                            print(f"🧠 推理内容: {content[:50]}...")
                        
                        elif chunk_type == 'final':
                            final_chunks.append(content)
                            print(f"📋 最终内容: {content[:50]}...")
                        
                        elif chunk_type == 'error':
                            error_msg = chunk_data.get('message', '未知错误')
                            error_chunks.append(error_msg)
                            print(f"❌ 错误: {error_msg}")
                            return False
                        
                        elif chunk_type == 'complete':
                            print("🏁 完成信号")
                            break
                        
                        # 限制测试
                        if chunk_count > 30:
                            print("⏰ 达到测试限制")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print(f"   数据: {data_str[:100]}...")
                        continue
        
        # 分析结果
        print(f"\n📊 DeepSeek-V3流式测试结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理chunks: {len(reasoning_chunks)}")
        print(f"   最终chunks: {len(final_chunks)}")
        print(f"   错误chunks: {len(error_chunks)}")
        
        if error_chunks:
            print(f"❌ 发现错误: {error_chunks}")
            return False
        
        if final_chunks:
            total_final = ''.join(final_chunks)
            print(f"✅ 最终内容总长度: {len(total_final)} 字符")
            print(f"   内容预览: {total_final[:200]}...")
            
            # 检查内容质量
            has_analysis = any(word in total_final for word in ["分析", "故障", "保护", "变压器"])
            has_natural_language = any(word in total_final for word in ["根据", "从", "可以", "需要", "建议"])
            
            print(f"   包含分析内容: {'✅' if has_analysis else '❌'}")
            print(f"   自然语言格式: {'✅' if has_natural_language else '❌'}")
            
            return has_analysis and has_natural_language
        else:
            print("❌ 没有收到最终内容")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deepseek_r1_comparison():
    """对比测试DeepSeek-R1"""
    print("\n🧪 对比测试DeepSeek-R1")
    
    try:
        test_data = {
            "query": "变压器差动保护动作，现场有异响和油温升高，套管有渗油现象",
            "thinking_mode": True
        }
        
        print(f"🔍 发送DeepSeek-R1请求...")
        
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json=test_data,
            stream=True,
            timeout=60
        )
        
        print(f"🌊 R1响应状态: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ R1请求失败: {response.status_code}")
            return False
        
        # 快速检查R1响应
        chunk_count = 0
        has_reasoning = False
        has_final = False
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                
                if line_str.startswith('data: '):
                    data_str = line_str[6:]
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk_count += 1
                        
                        chunk_type = chunk_data.get('type', 'unknown')
                        
                        if chunk_type == 'reasoning':
                            has_reasoning = True
                        elif chunk_type == 'final':
                            has_final = True
                        elif chunk_type == 'complete':
                            break
                        
                        if chunk_count > 20:
                            break
                            
                    except:
                        continue
        
        print(f"📊 R1测试结果: chunks={chunk_count}, reasoning={has_reasoning}, final={has_final}")
        return has_reasoning or has_final
        
    except Exception as e:
        print(f"❌ R1测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 直接流式API测试")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    print("第一步：直接测试DeepSeek-V3流式API")
    v3_result = test_deepseek_v3_stream_direct()
    test_results.append(("DeepSeek-V3流式", v3_result))
    
    print("\n第二步：对比测试DeepSeek-R1")
    r1_result = test_deepseek_r1_comparison()
    test_results.append(("DeepSeek-R1对比", r1_result))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ 流式API功能正常")
        print("\n🎉 DeepSeek-V3和R1都能正常工作！")
    else:
        print("❌ 流式API存在问题")
        
        if not v3_result:
            print("\n🔧 DeepSeek-V3问题可能原因:")
            print("1. 后端流式处理逻辑错误")
            print("2. thinking_mode=False时的处理有误")
            print("3. content字段处理不正确")
        
        if not r1_result:
            print("\n🔧 DeepSeek-R1问题可能原因:")
            print("1. reasoning_content字段处理错误")
            print("2. 智能分离逻辑失效")
    
    return all_passed

if __name__ == "__main__":
    main()
