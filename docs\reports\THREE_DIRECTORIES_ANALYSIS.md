# data/ vs embeddings/ vs knowledge_base/ 目录结构分析

## 🎯 三个目录的核心区别

基于对项目实际文件的分析，这三个目录在电力故障诊断系统中扮演不同的角色：

## 📁 1. data/ - 数据处理流水线

**作用**: 原始数据 → 结构化数据 → 训练就绪数据的**完整处理流水线**

### 目录结构与功能
```
data/
├── raw/                    # 🔸 原始数据存储
│   ├── maintenance_log_001.json      # 维护日志原始记录
│   ├── protection_event_log_001.json # 保护事件原始数据
│   ├── sensor_data_001.csv          # 传感器原始数据
│   ├── fault_waveform_001.txt       # 故障波形原始文件
│   └── ...                          # 各种格式的原始数据
│
├── structured/             # 🔸 结构化处理后数据
│   ├── equipment_database_001.json   # 标准化设备数据
│   ├── fault_patterns_001.json       # 结构化故障模式
│   ├── alarm_events_001.json         # 标准化告警事件
│   └── ...                          # 统一格式的结构化数据
│
└── processed/              # 🔸 模型训练就绪数据
    ├── text/               # 文本特征提取结果
    ├── images/             # 图像特征提取结果
    └── vectors/            # 向量化处理结果
```

### 数据特点
- **时间性**: 按处理阶段组织 (raw → structured → processed)
- **格式多样**: 支持JSON、CSV、TXT、XML等多种原始格式
- **业务导向**: 直接对应电力系统的业务数据
- **可追溯**: 保持从原始到最终的完整数据链路

### 实际内容示例
<augment_code_snippet path="data/raw/maintenance_log_001.json" mode="EXCERPT">
````json
{
  "maintenance_record": {
    "record_id": "MR-2024-001",
    "equipment_id": "TR001",
    "equipment_name": "1号主变压器",
    "maintenance_date": "2024-01-10",
    "maintenance_type": "预防性维护",
    "technicians": ["张三", "李四", "王五"]
  }
}
````
</augment_code_snippet>

---

## 🔍 2. embeddings/ - 向量检索引擎

**作用**: 为RAG检索提供**高性能向量搜索能力**的技术基础设施

### 目录结构与功能
```
embeddings/
├── faiss_store/            # 🔸 FAISS向量数据库文件
│   ├── index.faiss                    # 主向量索引文件
│   ├── equipment_manual_index.faiss   # 设备手册专用索引
│   ├── fault_diagnosis_index.faiss    # 故障诊断专用索引
│   ├── metadata.json                  # 索引元数据配置
│   └── sample_documents.json          # 文档样本映射
│
└── index/                  # 🔸 索引管理文件
    └── (索引管理相关文件)
```

### 技术特点
- **高性能**: 使用FAISS实现毫秒级向量检索
- **专业化**: 针对不同业务场景建立专用索引
- **可扩展**: 支持IndexFlatIP、IndexIVF等多种索引类型
- **元数据驱动**: 通过metadata.json管理索引配置

### 实际配置示例
<augment_code_snippet path="embeddings/faiss_store/metadata.json" mode="EXCERPT">
````json
{
  "dimension": 384,
  "index_type": "IndexFlatIP",
  "created_at": "2025-01-03",
  "document_count": 0
}
````
</augment_code_snippet>

---

## 📚 3. knowledge_base/ - 领域知识库

**作用**: 电力系统**专业知识的结构化存储**，为AI提供领域专业性

### 目录结构与功能
```
knowledge_base/
├── text/                   # 🔸 文本知识库
│   ├── case_studies/       # 故障案例研究
│   ├── manuals/            # 设备操作手册
│   ├── procedures/         # 操作规程
│   ├── standards/          # 行业标准
│   └── technical_specs/    # 技术规格
│
├── images/                 # 🔸 图像知识库
│   ├── equipment_photos_001.json     # 设备照片索引
│   ├── fault_case_images_001.json    # 故障案例图片
│   ├── thermal_imaging_database_001.json # 热成像数据库
│   └── ...                           # 各类专业图像索引
│
└── mappings/               # 🔸 知识关系映射
    ├── documents.json      # 文档关系映射
    └── sample_documents.json # 样本文档映射
```

### 知识特点
- **专业性**: 电力系统领域的专业知识
- **结构化**: 按知识类型和应用场景分类
- **关联性**: 通过mappings建立知识间的关联关系
- **多模态**: 同时包含文本、图像等多种知识形式

### 实际内容示例
<augment_code_snippet path="knowledge_base/images/equipment_photos_001.json" mode="EXCERPT">
````json
{
  "image_collection": {
    "collection_id": "EQUIP_PHOTOS_001",
    "collection_name": "110kV变电站设备照片集",
    "total_images": 25,
    "equipment_coverage": ["transformer", "circuit_breaker", "disconnector"]
  },
  "images": [
    {
      "image_id": "IMG_001",
      "filename": "transformer_main_view_001.jpg",
      "equipment_id": "TR001",
      "equipment_name": "1号主变压器"
    }
  ]
}
````
</augment_code_snippet>

---

## 🔄 三个目录的协作关系

### 数据流向图
```mermaid
graph TD
    A[原始业务数据] --> B[data/raw/]
    B --> C[data/structured/]
    C --> D[data/processed/]
    
    E[专业知识内容] --> F[knowledge_base/text/]
    G[专业图像资料] --> H[knowledge_base/images/]
    
    D --> I[文本向量化]
    F --> I
    H --> J[图像向量化]
    
    I --> K[embeddings/faiss_store/]
    J --> K
    
    K --> L[RAG检索系统]
    
    style B fill:#e1f5fe
    style F fill:#f3e5f5
    style K fill:#e8f5e8
```

### 协作机制

#### 1. 数据预处理阶段
- **data/raw/** → **data/structured/** → **data/processed/**
- 将业务数据转换为模型可用格式

#### 2. 知识库构建阶段  
- **knowledge_base/text/** + **knowledge_base/images/** → 专业知识整理
- **knowledge_base/mappings/** → 建立知识关联关系

#### 3. 向量化阶段
- **data/processed/** + **knowledge_base/** → **embeddings/faiss_store/**
- 将所有文本和图像转换为向量并建立索引

#### 4. 检索服务阶段
- **embeddings/faiss_store/** → 提供高性能向量检索
- 支持故障诊断的实时知识检索

---

## 💡 设计理念对比

| 维度 | data/ | embeddings/ | knowledge_base/ |
|------|-------|-------------|-----------------|
| **主要目的** | 数据处理流水线 | 检索技术基础设施 | 领域知识存储 |
| **组织原则** | 按处理阶段 | 按技术架构 | 按知识类型 |
| **数据来源** | 业务系统产生 | 向量化处理结果 | 专家整理编写 |
| **更新频率** | 高频(实时业务数据) | 中频(重建索引) | 低频(知识积累) |
| **技术特点** | 格式多样化 | 高性能检索 | 结构化专业性 |
| **服务对象** | 数据处理管道 | RAG检索引擎 | AI知识推理 |

---

## 🎯 实际应用场景

### 故障诊断流程中的作用

1. **用户上传故障信息** → 存储到 `data/raw/`
2. **系统结构化处理** → 转换到 `data/structured/`
3. **特征提取向量化** → 处理到 `data/processed/`
4. **向量相似度检索** → 查询 `embeddings/faiss_store/`
5. **匹配专业知识** → 检索 `knowledge_base/`
6. **生成诊断报告** → 结合所有信息源

### 各目录的独特价值

- **data/**: 确保数据质量和可追溯性
- **embeddings/**: 提供毫秒级检索性能
- **knowledge_base/**: 保证诊断的专业性和准确性

---

## 📊 总结

这三个目录形成了一个完整的**智能故障诊断生态系统**：

- **data/** = 数据工厂 (生产高质量训练数据)
- **embeddings/** = 检索引擎 (提供高性能向量搜索)  
- **knowledge_base/** = 专家大脑 (存储领域专业知识)

它们各司其职，又紧密协作，共同支撑起电力故障诊断系统的核心能力。
