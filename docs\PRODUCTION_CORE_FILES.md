# 生产环境核心文件清单

## 📋 核心文件识别

基于项目功能分析，确定生产环境部署必需的核心业务文件和配置文件。

## 🎯 分类标准

- **🟢 必需 (REQUIRED)**: 系统无法运行的核心文件
- **🟡 重要 (IMPORTANT)**: 影响功能完整性的文件
- **🔵 可选 (OPTIONAL)**: 增强功能的文件
- **🔴 开发 (DEVELOPMENT)**: 仅开发环境需要的文件

## 📁 生产环境核心文件清单

### 🟢 应用程序核心 (REQUIRED)

#### 主要入口点
```
🟢 start_project.py                    # 项目启动脚本
🟢 ui/app.py                          # 主Web应用入口
🟢 api/main.py                        # FastAPI应用入口
```

#### 核心业务模块
```
🟢 core/
├── __init__.py
├── config_manager.py                  # 配置管理器
├── equipment_manager.py               # 设备管理器
├── fault_analyzer.py                  # 故障分析器
├── inspection_parser.py               # 检查解析器
├── operation_analyzer.py              # 运行分析器
└── security_utils.py                  # 安全工具

🟢 api/
├── __init__.py
├── main.py                           # FastAPI主应用
├── models.py                         # 数据模型定义
└── routers/                          # API路由模块
    ├── __init__.py
    ├── equipment.py                  # 设备管理路由
    ├── analysis.py                   # 分析功能路由
    ├── upload.py                     # 文件上传路由
    └── knowledge.py                  # 知识库路由

🟢 ui/
├── __init__.py
├── app.py                           # Flask Web应用
├── static/                          # 静态资源
│   ├── css/                        # 样式文件
│   ├── js/                         # JavaScript文件
│   └── images/                     # 图片资源
└── templates/                       # HTML模板
    ├── base.html                   # 基础模板
    ├── index.html                  # 主页模板
    ├── analysis.html               # 分析页面
    ├── equipment.html              # 设备管理页面
    └── upload.html                 # 上传页面
```

#### 数据处理模块
```
🟢 data_processing/
├── __init__.py
├── text_processor.py                 # 文本处理
├── image_processor.py                # 图像处理
├── ocr_processor.py                  # OCR处理
└── vector_processor.py               # 向量处理

🟢 retriever/
├── __init__.py
├── knowledge_base.py                 # 知识库管理
├── text_retriever.py                # 文本检索
└── multimodal_retriever.py           # 多模态检索

🟢 langchain_modules/
├── __init__.py
├── agents/                          # 智能代理
│   ├── __init__.py
│   └── fault_agent.py              # 故障分析代理
├── chains/                          # 处理链
│   ├── __init__.py
│   ├── analysis_chain.py           # 分析链
│   └── retrieval_chain.py          # 检索链
├── prompts/                         # 提示模板
│   ├── __init__.py
│   ├── fault_analysis.py           # 故障分析提示
│   └── equipment_inspection.py     # 设备检查提示
└── tools/                           # 工具集
    ├── __init__.py
    ├── search_tool.py              # 搜索工具
    └── analysis_tool.py            # 分析工具
```

### 🟢 配置和依赖 (REQUIRED)

#### 配置文件
```
🟢 configs/config.yaml                 # 主配置文件
🟢 requirements.txt                    # Python依赖包
🟢 .env                               # 环境变量（生产环境需要）
```

#### 部署文件
```
🟢 Dockerfile                         # Docker镜像构建
🟢 docker-compose.prod.yml            # 生产环境Docker编排
```

### 🟢 数据存储 (REQUIRED)

#### 数据目录结构
```
🟢 data/
├── raw/                              # 原始数据
│   └── uploads/                     # 上传文件目录
├── structured/                       # 结构化数据
│   ├── equipment_data.json          # 设备数据
│   └── fault_cases.json            # 故障案例
└── processed/                        # 处理后数据
    ├── text/                        # 处理后文本
    ├── images/                      # 处理后图像
    └── vectors/                     # 向量数据

🟢 knowledge_base/
├── text/                            # 文本知识库
├── images/                          # 图像知识库
└── mappings/                        # 映射关系
    └── documents.json               # 文档映射

🟢 embeddings/
├── faiss_store/                     # FAISS向量存储
│   ├── index.faiss                 # FAISS索引文件
│   └── metadata.json              # 索引元数据
└── index/                           # 其他索引文件

🟢 uploads/                           # 运行时上传目录
└── images/                          # 图像上传子目录
```

### 🟡 运维支持 (IMPORTANT)

#### 日志和监控
```
🟡 logs/                              # 日志目录（运行时创建）
├── app.log                          # 应用日志
├── error.log                        # 错误日志
└── access.log                       # 访问日志
```

#### 部署脚本
```
🟡 scripts/
├── deploy.sh                        # 部署脚本
├── start.sh                         # 启动脚本
└── setup.py                         # 环境设置脚本
```

### 🔵 增强功能 (OPTIONAL)

#### Kubernetes部署
```
🔵 k8s/
├── namespace.yaml                    # 命名空间
├── app-deployment.yaml               # 应用部署
├── database.yaml                     # 数据库配置
└── ingress.yaml                      # 入口配置
```

#### Nginx代理
```
🔵 nginx/
├── nginx.conf                        # Nginx配置
└── generate-ssl.sh                   # SSL证书生成
```

#### 额外配置
```
🔵 config/
├── postgres/                         # PostgreSQL配置
└── redis.conf                        # Redis配置
```

### 🟢 必需的空目录

生产环境需要确保以下目录存在（即使为空）：
```
🟢 logs/                              # 日志目录
🟢 uploads/                           # 上传目录
🟢 uploads/images/                    # 图像上传目录
🟢 data/raw/uploads/                  # 原始数据上传
🟢 data/processed/                    # 处理后数据
🟢 embeddings/faiss_store/            # 向量存储
🟢 knowledge_base/mappings/           # 知识库映射
```

## 📊 文件统计

### 核心文件数量
- **Python模块**: ~45个核心业务文件
- **配置文件**: ~8个配置和部署文件
- **静态资源**: ~20个前端资源文件
- **模板文件**: ~10个HTML模板
- **数据文件**: ~5个初始数据文件

### 目录结构
- **核心业务目录**: 6个
- **数据存储目录**: 3个
- **配置部署目录**: 2个
- **运维支持目录**: 3个

## 🚀 部署建议

### 最小化部署（适用于资源受限环境）
```bash
# 仅包含核心必需文件
api/ core/ ui/ retriever/ data_processing/ langchain_modules/
configs/ data/ knowledge_base/ embeddings/ uploads/
requirements.txt start_project.py Dockerfile
```

### 标准部署（推荐）
```bash
# 包含核心文件 + 运维工具
[最小化部署的所有文件]
+ logs/ scripts/ docker-compose.prod.yml
```

### 完整部署（适用于复杂环境）
```bash
# 包含所有生产相关文件
[标准部署的所有文件]
+ k8s/ nginx/ config/
```

---
**文件清单版本**: v1.0  
**适用环境**: 生产环境  
**更新时间**: 2025-07-03
