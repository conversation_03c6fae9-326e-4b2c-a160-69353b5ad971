2025-07-09 14:17:57.968 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:17:58.118 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:17:58.118 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:17:58.157 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:17:58.163 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:17:58.163 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:17:58.163 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:17:58.214 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:17:58.214 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:17:58.214 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:17:58.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:17:58.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:17:58.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:17:58.219 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:18:07.092 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:18:07.110 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:18:07.110 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:18:08.256 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:18:08.417 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:18:08.417 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:18:08.451 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:18:08.455 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:18:08.456 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:18:08.456 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:18:08.503 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:18:08.508 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:18:15.861 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:18:15.875 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:18:15.875 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:22:55.593 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:22:55.766 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:22:55.766 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:22:55.801 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:22:55.806 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:22:55.806 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:22:55.807 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:22:55.853 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:22:55.853 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:22:55.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:22:55.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:22:55.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:22:55.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:22:55.858 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:23:05.944 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:23:05.954 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:23:05.954 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:32:55.292 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:32:55.463 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:32:55.463 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:32:55.505 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:32:55.510 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:32:55.511 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:32:55.511 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:32:55.556 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:32:55.559 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:32:55.560 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:33:07.520 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:33:07.531 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:33:07.531 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:33:08.471 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:33:08.650 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:33:08.650 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:33:08.679 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:33:08.684 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:33:08.684 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:33:08.684 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:33:08.726 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:33:08.726 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:33:08.726 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:33:08.727 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:33:08.728 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:33:08.728 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:33:08.728 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:33:08.729 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:33:08.729 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:33:08.729 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:33:08.731 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:33:17.879 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:33:17.888 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:33:17.888 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:51:57.804 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:51:57.964 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:51:57.964 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:51:57.998 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:51:58.003 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:51:58.003 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:51:58.003 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:51:58.047 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:51:58.051 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:52:06.383 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:52:06.392 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:52:06.392 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:26:31.275 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:26:31.446 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:26:31.446 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:26:31.477 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:26:31.481 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:26:31.481 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:26:31.481 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:26:31.525 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:26:31.526 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:26:31.526 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:26:31.526 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:26:31.527 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:26:31.527 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:26:31.527 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:26:31.528 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:26:31.528 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:26:31.528 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:26:31.530 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:26:38.184 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:26:38.193 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:26:38.193 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:26:38.964 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:26:39.072 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:26:39.072 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:26:39.093 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:26:39.096 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:26:39.096 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:26:39.096 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:26:39.127 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:26:39.127 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:26:39.128 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:26:39.128 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:26:39.128 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:26:39.129 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:26:39.129 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:26:39.129 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:26:39.130 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:26:39.130 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:26:39.132 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:26:45.839 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:26:45.849 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:26:45.849 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:45:28.598 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:45:28.697 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:45:28.697 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:45:28.726 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:45:28.728 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:45:28.729 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:45:28.729 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:45:28.770 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:45:28.770 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:45:28.770 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:45:28.771 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:45:28.771 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:45:28.771 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:45:28.771 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:45:28.771 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:45:28.772 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:45:28.772 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:45:28.773 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:45:36.228 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:45:36.237 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:45:36.237 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:45:37.008 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:45:37.120 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:45:37.121 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:45:37.144 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:45:37.147 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:45:37.147 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:45:37.147 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:45:37.174 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:45:37.175 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:45:37.175 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:45:37.175 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:45:37.175 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:45:37.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:45:37.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:45:37.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:45:37.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:45:37.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:45:37.178 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:45:43.415 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:45:43.427 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:45:43.428 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:47:23.681 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:47:23.786 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:47:23.786 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:47:23.813 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:47:23.816 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:47:23.816 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:47:23.816 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:47:23.845 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:47:23.846 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:47:23.846 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:47:23.846 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:47:23.846 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:47:23.846 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:47:23.847 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:47:23.847 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:47:23.847 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:47:23.847 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:47:23.849 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:47:30.250 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:47:30.258 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:47:30.259 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:47:30.984 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:47:31.099 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:47:31.099 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:47:31.120 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:47:31.123 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:47:31.124 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:47:31.124 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:47:31.152 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:47:31.153 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:47:31.153 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:47:31.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:47:31.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:47:31.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:47:31.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:47:31.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:47:31.155 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:47:31.155 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:47:31.156 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:47:37.599 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:47:37.608 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:47:37.609 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:55:43.918 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:55:44.059 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:55:44.060 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:55:44.092 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:55:44.096 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:55:44.097 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:55:44.097 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:55:44.140 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:55:44.140 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:55:44.141 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:55:44.141 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:55:44.141 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:55:44.142 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:55:44.142 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:55:44.142 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:55:44.142 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:55:44.143 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:55:44.144 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:55:52.623 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:55:52.632 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:55:52.632 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 15:58:12.448 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 15:58:12.605 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 15:58:12.606 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 15:58:12.645 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 15:58:12.652 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 15:58:12.652 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 15:58:12.653 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 15:58:12.706 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 15:58:12.706 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 15:58:12.707 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 15:58:12.707 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 15:58:12.707 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 15:58:12.707 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 15:58:12.707 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 15:58:12.708 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 15:58:12.708 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 15:58:12.708 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 15:58:12.711 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 15:58:20.944 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 15:58:20.953 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 15:58:20.953 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:00:19.578 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:00:19.688 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:00:19.688 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:00:19.709 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:00:19.713 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:00:19.713 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:00:19.713 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:00:19.746 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:00:19.747 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:00:19.747 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:00:19.747 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:00:19.747 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:00:19.748 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:00:19.748 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:00:19.748 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:00:19.748 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:00:19.748 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:00:19.750 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:00:27.043 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:00:27.054 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:00:27.054 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:02:18.318 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:02:18.417 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:02:18.417 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:02:18.436 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:02:18.440 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:02:18.440 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:02:18.440 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:02:18.467 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:02:18.468 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:02:18.468 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:02:18.468 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:02:18.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:02:18.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:02:18.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:02:18.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:02:18.469 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:02:18.470 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:02:18.471 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:02:25.210 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:02:25.219 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:02:25.220 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:03:15.926 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:03:16.030 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:03:16.030 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:03:16.052 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:03:16.054 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:03:16.054 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:03:16.054 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:03:16.082 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:03:16.083 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:03:16.083 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:03:16.083 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:03:16.083 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:03:16.084 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:03:16.084 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:03:16.084 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:03:16.084 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:03:16.084 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:03:16.086 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:03:22.650 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:03:22.660 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:03:22.660 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:04:06.751 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:04:06.857 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:04:06.858 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:04:06.878 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:04:06.882 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:04:06.882 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:04:06.882 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:04:06.912 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:04:06.912 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:04:06.912 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:04:06.913 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:04:06.913 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:04:06.913 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:04:06.913 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:04:06.913 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:04:06.914 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:04:06.914 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:04:06.916 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:04:13.800 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:04:13.809 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:04:13.810 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:04:58.787 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:04:58.895 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:04:58.895 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:04:58.915 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:04:58.918 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:04:58.918 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:04:58.918 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:04:58.945 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:04:58.946 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:04:58.946 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:04:58.946 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:04:58.946 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:04:58.946 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:04:58.947 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:04:58.947 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:04:58.947 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:04:58.947 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:04:58.949 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:05:05.574 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:05:05.583 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:05:05.584 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:34:13.562 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:34:13.662 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:34:13.662 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:34:13.691 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:34:13.694 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:34:13.694 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:34:13.694 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:34:13.725 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:34:13.725 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:34:13.725 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:34:13.726 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:34:13.726 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:34:13.726 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:34:13.726 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:34:13.727 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:34:13.727 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:34:13.727 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:34:13.728 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:34:19.938 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:34:19.947 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:34:19.947 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:34:20.691 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:34:20.802 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:34:20.803 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:34:20.823 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:34:20.826 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:34:20.826 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:34:20.826 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:34:20.854 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:34:20.854 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:34:20.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:34:20.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:34:20.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:34:20.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:34:20.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:34:20.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:34:20.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:34:20.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:34:20.858 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:34:27.188 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:34:27.196 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:34:27.197 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:35:59.352 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:35:59.456 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:35:59.456 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:35:59.478 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:35:59.481 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:35:59.481 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:35:59.481 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:35:59.511 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:35:59.512 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:35:59.512 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:35:59.512 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:35:59.513 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:35:59.513 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:35:59.513 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:35:59.513 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:35:59.513 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:35:59.514 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:35:59.515 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:36:07.677 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:36:07.686 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:36:07.687 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:36:08.601 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:36:08.734 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:36:08.735 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:36:08.760 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:36:08.764 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:36:08.764 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:36:08.764 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:36:08.798 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:36:08.798 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:36:08.799 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:36:08.799 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:36:08.799 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:36:08.800 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:36:08.800 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:36:08.801 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:36:08.801 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:36:08.801 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:36:08.803 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:36:16.286 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:36:16.295 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:36:16.295 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:40:04.271 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:40:04.366 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:40:04.366 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:40:04.388 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:40:04.390 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:40:04.390 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:40:04.391 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:40:04.420 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:40:04.420 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:40:04.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:40:04.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:40:04.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:40:04.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:40:04.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:40:04.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:40:04.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:40:04.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:40:04.424 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:40:11.163 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:40:11.171 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:40:11.171 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 16:40:11.897 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 16:40:12.003 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 16:40:12.003 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 16:40:12.023 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 16:40:12.026 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 16:40:12.026 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 16:40:12.027 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 16:40:12.062 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 16:40:12.062 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 16:40:12.063 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 16:40:12.063 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 16:40:12.063 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 16:40:12.063 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 16:40:12.064 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 16:40:12.064 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 16:40:12.064 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 16:40:12.064 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 16:40:12.067 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 16:40:19.389 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 16:40:19.399 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 16:40:19.399 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:11:32.635 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:11:32.849 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:11:32.849 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:11:32.911 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:11:32.916 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:11:32.917 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:11:32.917 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:11:32.996 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:11:32.996 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:11:32.997 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:11:32.997 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:11:32.997 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:11:32.997 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:11:32.998 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:11:32.998 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:11:32.998 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:11:32.998 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:11:33.001 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:11:48.070 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:11:48.082 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:11:48.086 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:11:49.646 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:11:50.085 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:11:50.086 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:11:50.212 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:11:50.223 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:11:50.223 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:11:50.224 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:11:50.308 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:11:50.309 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:11:50.309 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:11:50.311 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:11:50.311 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:11:50.311 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:11:50.312 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:11:50.312 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:11:50.312 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:11:50.313 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:11:50.319 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:12:03.886 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:12:03.896 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:12:03.896 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:27:49.793 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:27:49.998 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:27:49.999 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:27:50.054 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:27:50.059 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:27:50.059 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:27:50.059 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:27:50.110 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:27:50.110 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:27:50.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:27:50.115 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:27:50.116 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:27:50.116 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:27:50.117 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:27:50.117 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:27:50.117 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:27:50.118 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:27:50.119 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:28:01.538 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:28:01.552 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:28:01.553 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:34:23.470 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:34:23.690 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:34:23.690 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:34:23.742 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:34:23.748 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:34:23.749 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:34:23.749 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:34:23.801 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:34:23.802 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:34:23.802 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:34:23.802 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:34:23.803 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:34:23.803 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:34:23.803 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:34:23.803 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:34:23.803 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:34:23.804 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:34:23.805 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:34:34.763 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:34:34.775 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:34:34.775 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:34:35.846 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:34:36.086 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:34:36.086 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:34:36.112 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:34:36.121 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:34:36.121 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:34:36.121 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:34:36.175 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:34:36.175 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:34:36.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:34:36.176 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:34:36.177 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:34:36.177 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:34:36.177 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:34:36.177 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:34:36.178 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:34:36.178 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:34:36.180 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:34:54.355 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:34:54.386 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:34:54.387 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:35:08.374 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:35:08.703 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:35:08.705 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:35:08.777 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:35:08.787 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:35:08.788 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:35:08.789 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:35:08.885 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:35:08.886 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:35:08.886 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:35:08.888 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:35:08.888 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:35:08.889 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:35:08.889 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:35:08.889 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:35:08.890 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:35:08.890 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:35:08.893 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:35:21.822 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:35:21.838 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:35:21.839 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:37:23.841 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:37:24.164 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:37:24.164 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:37:24.236 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:37:24.251 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:37:24.252 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:37:24.253 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:37:24.354 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:37:24.354 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:37:24.355 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:37:24.358 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:37:24.359 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:37:24.359 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:37:24.359 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:37:24.360 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:37:24.360 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:37:24.360 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:37:24.362 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:37:34.742 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:37:34.765 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:37:34.766 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:38:02.426 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:38:02.752 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:38:02.752 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:38:02.801 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:38:02.804 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:38:02.804 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:38:02.804 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:38:02.868 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:38:02.869 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:38:02.869 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:38:02.869 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:38:02.869 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:38:02.870 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:38:02.870 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:38:02.870 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:38:02.870 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:38:02.871 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:38:02.893 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 17:38:12.079 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 17:38:12.090 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 17:38:12.091 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 17:40:32.807 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 17:40:33.000 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 17:40:33.001 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 17:40:33.050 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 17:40:33.058 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 17:40:33.058 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 17:40:33.058 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 17:40:33.111 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 17:40:33.112 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 17:40:33.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 17:40:33.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 17:40:33.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 17:40:33.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 17:40:33.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 17:40:33.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 17:40:33.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 17:40:33.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 17:40:33.116 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:22:17.022 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:22:17.236 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:22:17.236 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:22:17.279 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:22:17.286 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:22:17.286 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:22:17.286 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:22:17.342 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:22:17.343 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:22:17.343 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:22:17.343 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:22:17.343 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:22:17.344 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:22:17.344 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:22:17.344 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:22:17.344 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:22:17.345 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:22:17.346 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:22:29.191 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:22:29.204 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:22:29.204 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 18:22:30.210 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:22:30.353 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:22:30.353 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:22:30.387 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:22:30.394 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:22:30.394 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:22:30.395 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:22:30.435 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:22:30.435 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:22:30.435 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:22:30.435 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:22:30.436 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:22:30.436 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:22:30.436 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:22:30.436 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:22:30.436 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:22:30.437 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:22:30.438 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:22:39.214 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:22:39.224 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:22:39.224 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 18:26:12.646 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:26:13.001 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:26:13.002 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:26:13.070 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:26:13.077 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:26:13.078 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:26:13.078 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:26:13.168 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:26:13.169 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:26:13.169 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:26:13.169 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:26:13.169 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:26:13.170 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:26:13.170 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:26:13.171 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:26:13.171 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:26:13.171 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:26:13.174 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:26:25.600 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:26:25.610 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:26:25.611 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 18:27:20.029 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:27:20.184 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:27:20.184 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:27:20.208 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:27:20.210 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:27:20.210 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:27:20.210 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:27:20.268 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:27:20.269 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:27:20.270 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:27:20.270 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:27:20.270 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:27:20.270 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:27:20.271 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:27:20.271 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:27:20.271 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:27:20.271 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:27:20.272 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:27:31.301 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:27:31.319 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:27:31.319 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 18:27:32.350 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:27:32.535 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:27:32.535 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:27:32.573 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:27:32.581 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:27:32.584 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:27:32.585 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:27:32.638 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:27:32.639 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:27:32.639 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:27:32.640 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:27:32.640 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:27:32.640 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:27:32.641 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:27:32.641 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:27:32.641 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:27:32.641 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:27:32.644 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:27:42.063 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:27:42.090 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:27:42.103 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 18:30:35.074 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:30:35.277 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:30:35.277 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:30:35.328 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:30:35.331 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:30:35.332 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:30:35.332 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:30:35.383 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:30:35.384 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:30:35.385 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:30:35.385 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:30:35.385 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:30:35.385 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:30:35.386 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:30:35.386 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:30:35.386 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:30:35.386 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:30:35.388 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:31:39.775 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:31:39.946 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:31:39.946 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:31:39.971 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:31:39.974 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:31:39.975 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:31:39.975 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:31:40.020 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:31:40.020 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:31:40.020 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:31:40.021 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:31:40.021 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:31:40.021 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:31:40.021 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:31:40.022 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:31:40.022 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:31:40.022 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:31:40.024 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:31:49.313 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:31:49.325 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:31:49.325 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 18:31:50.277 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 18:31:50.420 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 18:31:50.420 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 18:31:50.445 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 18:31:50.449 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 18:31:50.449 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 18:31:50.449 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 18:31:50.491 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 18:31:50.492 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 18:31:50.492 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 18:31:50.493 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 18:31:50.493 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 18:31:50.493 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 18:31:50.493 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 18:31:50.493 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 18:31:50.494 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 18:31:50.494 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 18:31:50.496 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 18:32:00.000 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 18:32:00.010 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 18:32:00.010 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 09:16:16.662 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 09:16:16.817 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 09:16:16.817 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 09:16:16.846 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 09:16:16.850 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 09:16:16.850 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 09:16:16.850 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 09:16:16.894 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 09:16:16.895 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 09:16:16.895 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 09:16:16.895 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 09:16:16.895 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 09:16:16.895 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 09:16:16.896 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 09:16:16.896 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 09:16:16.896 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 09:16:16.896 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 09:16:16.897 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 09:16:25.195 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 09:16:25.206 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 09:16:25.206 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 09:16:25.948 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 09:16:26.043 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 09:16:26.044 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 09:16:26.063 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 09:16:26.065 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 09:16:26.066 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 09:16:26.066 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 09:16:26.093 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 09:16:26.093 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 09:16:26.093 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 09:16:26.093 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 09:16:26.094 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 09:16:26.094 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 09:16:26.094 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 09:16:26.094 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 09:16:26.095 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 09:16:26.095 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 09:16:26.096 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 09:16:32.827 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 09:16:32.839 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 09:16:32.840 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 09:23:37.556 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 09:23:37.740 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 09:23:37.740 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 09:23:37.782 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 09:23:37.787 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 09:23:37.787 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 09:23:37.787 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 09:23:37.852 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 09:23:37.852 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 09:23:37.853 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 09:23:37.853 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 09:23:37.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 09:23:37.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 09:23:37.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 09:23:37.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 09:23:37.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 09:23:37.858 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 09:23:37.876 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 09:23:46.966 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 09:23:46.977 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 09:23:46.977 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 09:34:06.310 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 09:34:06.470 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 09:34:06.471 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 09:34:06.506 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 09:34:06.511 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 09:34:06.511 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 09:34:06.511 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 09:34:06.558 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 09:34:06.558 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 09:34:06.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 09:34:06.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 09:34:06.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 09:34:06.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 09:34:06.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 09:34:06.560 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 09:34:06.560 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 09:34:06.560 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 09:34:06.562 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 09:34:15.319 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 09:34:15.329 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 09:34:15.329 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 09:53:30.965 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 09:53:31.092 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 09:53:31.092 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 09:53:31.121 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 09:53:31.124 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 09:53:31.125 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 09:53:31.125 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 09:53:31.163 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 09:53:31.164 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 09:53:31.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 09:53:31.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 09:53:31.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 09:53:31.165 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 09:53:31.165 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 09:53:31.165 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 09:53:31.165 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 09:53:31.166 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 09:53:31.168 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 09:53:37.919 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 09:53:37.928 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 09:53:37.928 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 09:54:05.386 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 09:54:05.487 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 09:54:05.487 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 09:54:05.510 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 09:54:05.513 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 09:54:05.513 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 09:54:05.513 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 09:54:05.542 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 09:54:05.542 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 09:54:05.542 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 09:54:05.543 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 09:54:05.543 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 09:54:05.543 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 09:54:05.543 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 09:54:05.543 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 09:54:05.543 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 09:54:05.545 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 09:54:05.546 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 09:54:12.475 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 09:54:12.485 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 09:54:12.485 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:14:21.907 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:14:22.067 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:14:22.067 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:14:22.102 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:14:22.106 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:14:22.106 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:14:22.107 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:14:22.154 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:14:22.154 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:14:22.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:14:22.154 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:14:22.155 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:14:22.155 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:14:22.155 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:14:22.155 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:14:22.156 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:14:22.156 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:14:22.157 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:14:31.302 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:14:31.312 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:14:31.312 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:14:50.296 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:14:50.414 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:14:50.415 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:14:50.436 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:14:50.439 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:14:50.439 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:14:50.439 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:14:50.475 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:14:50.475 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:14:50.476 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:14:50.476 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:14:50.476 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:14:50.476 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:14:50.477 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:14:50.477 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:14:50.477 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:14:50.477 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:14:50.479 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:14:57.712 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:14:57.722 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:14:57.722 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:28:09.310 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:28:09.459 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:28:09.459 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:28:09.488 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:28:09.493 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:28:09.493 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:28:09.494 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:28:09.537 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:28:09.537 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:28:09.538 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:28:09.538 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:28:09.538 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:28:09.538 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:28:09.539 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:28:09.539 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:28:09.539 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:28:09.539 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:28:09.541 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:28:18.284 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:28:18.294 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:28:18.294 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:28:36.191 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:28:36.300 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:28:36.301 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:28:36.324 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:28:36.327 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:28:36.327 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:28:36.327 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:28:36.355 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:28:36.356 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:28:36.356 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:28:36.356 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:28:36.356 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:28:36.357 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:28:36.357 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:28:36.357 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:28:36.357 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:28:36.358 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:28:36.359 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:28:43.409 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:28:43.419 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:28:43.419 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:29:10.874 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:29:11.080 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:29:11.081 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:29:11.118 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:29:11.121 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:29:11.121 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:29:11.121 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:29:11.161 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:29:11.161 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:29:11.162 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:29:11.162 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:29:11.163 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:29:11.163 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:29:11.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:29:11.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:29:11.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:29:11.164 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:29:11.166 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:29:18.275 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:29:18.285 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:29:18.285 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:29:39.983 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:29:40.082 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:29:40.082 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:29:40.102 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:29:40.105 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:29:40.105 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:29:40.105 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:29:40.133 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:29:40.134 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:29:40.134 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:29:40.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:29:40.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:29:40.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:29:40.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:29:40.135 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:29:40.136 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:29:40.136 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:29:40.137 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:29:46.805 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:29:46.814 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:29:46.814 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:29:53.759 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:29:53.876 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:29:53.876 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:29:53.899 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:29:53.902 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:29:53.902 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:29:53.902 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:29:53.934 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:29:53.934 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:29:53.935 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:29:53.935 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:29:53.935 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:29:53.935 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:29:53.936 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:29:53.936 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:29:53.936 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:29:53.936 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:29:53.937 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:30:00.633 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:30:00.641 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:30:00.641 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:30:12.900 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:30:13.042 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:30:13.042 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:30:13.070 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:30:13.075 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:30:13.076 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:30:13.076 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:30:13.111 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:30:13.111 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:30:13.112 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:30:13.112 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:30:13.112 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:30:13.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:30:13.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:30:13.113 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:30:13.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:30:13.114 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:30:13.116 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:30:20.587 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:30:20.596 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:30:20.596 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:41:57.510 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:41:57.675 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:41:57.675 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:41:57.709 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:41:57.715 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:41:57.715 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:41:57.716 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:41:57.779 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:41:57.781 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:41:57.781 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:41:57.781 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:41:57.781 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:41:57.782 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:41:57.782 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:41:57.782 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:41:57.783 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:41:57.783 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:41:57.784 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:42:06.146 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:42:06.155 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:42:06.156 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:43:58.920 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:43:59.142 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:43:59.142 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:43:59.172 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:43:59.176 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:43:59.176 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:43:59.176 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:43:59.214 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:43:59.215 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:43:59.215 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:43:59.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:43:59.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:43:59.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:43:59.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:43:59.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:43:59.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:43:59.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:43:59.219 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:44:11.547 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:44:11.559 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:44:11.559 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:55:48.932 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:55:49.075 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:55:49.076 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:55:49.108 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:55:49.113 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:55:49.113 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:55:49.113 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:55:49.159 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:55:49.159 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:55:49.160 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:55:49.160 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:55:49.160 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:55:49.160 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:55:49.160 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:55:49.161 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:55:49.161 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:55:49.161 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:55:49.163 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:55:57.837 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:55:57.846 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:55:57.846 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:56:36.721 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:56:36.828 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:56:36.828 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:56:36.880 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:56:36.883 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:56:36.883 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:56:36.883 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:56:36.910 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:56:36.910 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:56:36.910 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:56:36.910 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:56:36.911 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:56:36.911 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:56:36.911 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:56:36.911 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:56:36.911 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:56:36.912 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:56:36.913 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:56:43.600 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:56:43.609 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:56:43.609 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:57:22.553 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:57:22.665 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:57:22.665 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:57:22.686 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:57:22.689 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:57:22.689 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:57:22.689 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:57:22.720 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:57:22.721 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:57:22.721 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:57:22.721 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:57:22.721 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:57:22.722 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:57:22.722 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:57:22.722 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:57:22.722 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:57:22.722 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:57:22.724 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:57:29.099 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:57:29.107 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:57:29.108 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:57:49.103 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:57:49.197 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:57:49.197 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:57:49.218 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:57:49.221 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:57:49.221 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:57:49.221 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:57:49.249 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:57:49.250 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:57:49.250 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:57:49.250 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:57:49.251 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:57:49.251 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:57:49.251 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:57:49.251 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:57:49.252 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:57:49.252 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:57:49.253 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:57:55.810 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:57:55.819 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:57:55.819 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-10 10:58:17.479 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-10 10:58:17.623 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-10 10:58:17.623 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-10 10:58:17.644 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-10 10:58:17.648 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-10 10:58:17.648 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-10 10:58:17.648 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-10 10:58:17.682 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-10 10:58:17.683 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-10 10:58:17.683 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-10 10:58:17.683 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-10 10:58:17.684 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-10 10:58:17.684 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-10 10:58:17.684 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-10 10:58:17.684 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-10 10:58:17.684 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-10 10:58:17.686 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-10 10:58:17.690 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-10 10:58:25.448 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-10 10:58:25.457 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-10 10:58:25.457 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
