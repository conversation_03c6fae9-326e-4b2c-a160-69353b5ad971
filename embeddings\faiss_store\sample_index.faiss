# FAISS索引文件 (二进制格式模拟)
# 这是一个示例FAISS索引文件的文本表示
# 实际的FAISS索引文件是二进制格式

FAISS_INDEX_HEADER:
  version: 1.7.4
  index_type: IndexFlatIP
  dimension: 384
  total_vectors: 1000
  metric_type: METRIC_INNER_PRODUCT
  created_time: 2024-01-15T16:30:00Z

INDEX_METADATA:
  training_size: 1000
  is_trained: true
  ntotal: 1000
  d: 384
  
VECTOR_DATA:
  # 向量数据以二进制格式存储
  # 每个向量384维，float32格式
  # 总共1000个向量
  
  vector_0: [0.0234, -0.1567, 0.2891, 0.0456, ...]  # 384维
  vector_1: [0.1234, -0.0567, 0.3891, 0.1456, ...]  # 384维
  vector_2: [0.2234, -0.2567, 0.1891, 0.2456, ...]  # 384维
  ...
  vector_999: [0.9234, -0.9567, 0.9891, 0.9456, ...]  # 384维

INDEX_STATISTICS:
  memory_usage: 1.5MB
  search_time_avg: 0.5ms
  build_time: 2.3s
  last_updated: 2024-01-15T16:30:00Z

# 注意：这只是示例文本表示
# 实际FAISS文件是二进制格式，需要使用faiss库读取
