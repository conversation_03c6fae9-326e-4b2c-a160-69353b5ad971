"""
向量处理模块

负责文本和图像的向量化、相似度计算等功能
"""

import os
import pickle
import re
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from loguru import logger


class VectorProcessor:
    """向量处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get("model_name", "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
        self.model_path = config.get("model_path", "./models/embedding")
        self.dimension = config.get("dimension", 384)
        self.batch_size = config.get("batch_size", 32)
        self.device = config.get("device", "cpu")

        # 向量化优化配置
        self.normalize_embeddings = config.get("normalize_embeddings", True)
        self.use_gpu = config.get("use_gpu", False) and self._check_gpu_availability()

        # FAISS索引优化配置
        self.index_type = config.get("index_type", "IndexHNSWFlat")  # 默认使用HNSW索引
        self.hnsw_m = config.get("hnsw_m", 16)  # HNSW连接数
        self.hnsw_ef_construction = config.get("hnsw_ef_construction", 200)  # 构建时搜索参数
        self.hnsw_ef_search = config.get("hnsw_ef_search", 100)  # 搜索时参数

        # 初始化嵌入模型
        self._init_embedding_model()

        # 初始化文本预处理器
        self._init_text_preprocessor()

    def _check_gpu_availability(self) -> bool:
        """检查GPU可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False

    def _init_text_preprocessor(self):
        """初始化文本预处理器"""
        # 电力专业术语词典
        self.power_terms = {
            '变压器', '断路器', '隔离开关', '电流互感器', '电压互感器',
            '避雷器', '电容器', '电抗器', '母线', '导线',
            '故障', '跳闸', '合闸', '保护', '动作', '运行', '检修',
            '绝缘', '接地', '短路', '过载', '过压', '欠压', '频率',
            'kV', 'MV', 'kW', 'MW', 'MVA', 'Hz', 'A', 'V', 'Ω'
        }

        # 停用词（针对电力领域优化）
        self.stop_words = {
            '的', '了', '在', '是', '有', '和', '与', '及', '或', '但',
            '因为', '所以', '如果', '那么', '这样', '那样', '这个', '那个',
            '可以', '应该', '需要', '必须', '能够', '可能', '也许', '大概'
        }

    def _init_embedding_model(self):
        """初始化嵌入模型"""
        try:
            # 如果model_path为空或不存在，直接使用在线模型
            if not self.model_path or not os.path.exists(self.model_path):
                logger.info("使用在线嵌入模型，跳过本地路径检查")

                # 尝试使用国内镜像源下载
                try:
                    # 设置HuggingFace镜像源
                    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

                    self.embedding_model = SentenceTransformer(self.model_name, device=self.device)
                    logger.info(f"成功加载在线嵌入模型: {self.model_name}")

                    # 如果指定了本地路径，保存模型
                    if self.model_path:
                        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
                        self.embedding_model.save(self.model_path)
                        logger.info(f"模型已保存到: {self.model_path}")

                except Exception as download_error:
                    logger.warning(f"在线模型加载失败: {download_error}")
                    # 使用TF-IDF作为备选方案
                    logger.info("使用TF-IDF向量化作为备选方案")
                    self._init_fallback_vectorizer()
            else:
                # 从本地路径加载
                self.embedding_model = SentenceTransformer(self.model_path, device=self.device)
                logger.info(f"从本地路径加载嵌入模型: {self.model_path}")

        except Exception as e:
            logger.error(f"嵌入模型初始化失败: {str(e)}")
            # 不抛出异常，使用备选方案
            self._init_fallback_vectorizer()

    def _init_fallback_vectorizer(self):
        """初始化备选的TF-IDF向量化器"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity

            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words=None,
                ngram_range=(1, 2)
            )
            self.embedding_model = None  # 标记使用备选方案
            logger.info("TF-IDF向量化器初始化成功")

        except ImportError:
            logger.error("sklearn未安装，无法使用TF-IDF备选方案")
            self.tfidf_vectorizer = None
            self.embedding_model = None
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """
        对文本进行向量化编码

        Args:
            texts: 文本列表

        Returns:
            向量矩阵
        """
        try:
            if not texts:
                return np.array([])

            # 使用SentenceTransformer模型
            if self.embedding_model is not None:
                embeddings = self.embedding_model.encode(
                    texts,
                    batch_size=self.batch_size,
                    show_progress_bar=True,
                    convert_to_numpy=True
                )
                logger.info(f"成功编码 {len(texts)} 个文本，向量维度: {embeddings.shape[1]}")
                return embeddings

            # 使用TF-IDF备选方案
            elif hasattr(self, 'tfidf_vectorizer') and self.tfidf_vectorizer is not None:
                # 如果是第一次使用，需要先fit
                if not hasattr(self.tfidf_vectorizer, 'vocabulary_'):
                    self.tfidf_vectorizer.fit(texts)

                embeddings = self.tfidf_vectorizer.transform(texts).toarray()
                logger.info(f"使用TF-IDF编码 {len(texts)} 个文本，向量维度: {embeddings.shape[1]}")
                return embeddings

            else:
                # 最后的备选方案：简单的词频向量
                logger.warning("使用简单词频向量作为最后备选方案")
                return self._simple_text_vectorize(texts)

        except Exception as e:
            logger.error(f"文本编码失败: {str(e)}")
            # 返回简单向量而不是抛出异常
            return self._simple_text_vectorize(texts)

    def _simple_text_vectorize(self, texts: List[str]) -> np.ndarray:
        """简单的文本向量化备选方案"""
        try:
            # 创建简单的词汇表
            all_words = set()
            for text in texts:
                words = text.lower().split()
                all_words.update(words)

            vocab = list(all_words)[:1000]  # 限制词汇表大小

            # 创建词频向量
            vectors = []
            for text in texts:
                words = text.lower().split()
                vector = [words.count(word) for word in vocab]
                vectors.append(vector)

            embeddings = np.array(vectors, dtype=np.float32)
            logger.info(f"使用简单词频向量编码 {len(texts)} 个文本，向量维度: {embeddings.shape[1]}")
            return embeddings

        except Exception as e:
            logger.error(f"简单向量化也失败: {str(e)}")
            # 返回零向量
            return np.zeros((len(texts), 100), dtype=np.float32)

    def encode_single_text(self, text: str) -> np.ndarray:
        """
        对单个文本进行向量化编码
        
        Args:
            text: 输入文本
            
        Returns:
            向量
        """
        try:
            embedding = self.embedding_model.encode([text], convert_to_numpy=True)
            return embedding[0]
            
        except Exception as e:
            logger.error(f"单文本编码失败: {str(e)}")
            return np.array([])
    
    def calculate_similarity(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            vector1: 向量1
            vector2: 向量2
            
        Returns:
            相似度分数
        """
        try:
            # 归一化向量
            norm1 = np.linalg.norm(vector1)
            norm2 = np.linalg.norm(vector2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            # 计算余弦相似度
            similarity = np.dot(vector1, vector2) / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"相似度计算失败: {str(e)}")
            return 0.0
    
    def batch_similarity(self, query_vector: np.ndarray, vectors: np.ndarray) -> np.ndarray:
        """
        批量计算相似度
        
        Args:
            query_vector: 查询向量
            vectors: 向量矩阵
            
        Returns:
            相似度数组
        """
        try:
            # 归一化
            query_norm = query_vector / np.linalg.norm(query_vector)
            vectors_norm = vectors / np.linalg.norm(vectors, axis=1, keepdims=True)
            
            # 计算余弦相似度
            similarities = np.dot(vectors_norm, query_norm)
            return similarities
            
        except Exception as e:
            logger.error(f"批量相似度计算失败: {str(e)}")
            return np.array([])
    
    def create_faiss_index(self, vectors: np.ndarray, index_type: str = None) -> faiss.Index:
        """
        创建优化的FAISS索引 - 根据数据规模自动选择最佳索引类型

        Args:
            vectors: 向量矩阵
            index_type: 索引类型（可选，自动选择）

        Returns:
            FAISS索引
        """
        try:
            if vectors.size == 0:
                raise ValueError("向量矩阵为空")

            dimension = vectors.shape[1]
            num_vectors = vectors.shape[0]

            # 自动选择索引类型
            if index_type is None:
                index_type = self._select_optimal_index_type(num_vectors, dimension)

            logger.info(f"创建FAISS索引: 类型={index_type}, 向量数量={num_vectors}, 维度={dimension}")

            # 预处理向量
            processed_vectors = self._preprocess_vectors_for_index(vectors, index_type)

            # 根据索引类型创建索引
            index = self._create_index_by_type(index_type, dimension, num_vectors)

            # 训练索引（如果需要）
            if hasattr(index, 'train') and hasattr(index, 'is_trained') and not index.is_trained:
                logger.info("训练FAISS索引...")
                index.train(processed_vectors)

            # 添加向量到索引
            index.add(processed_vectors)

            # 优化索引参数
            self._optimize_index_parameters(index, index_type)

            logger.info(f"成功创建FAISS索引，类型: {index_type}, 向量数量: {index.ntotal}")
            return index

        except Exception as e:
            logger.error(f"FAISS索引创建失败: {str(e)}")
            raise

    def _select_optimal_index_type(self, num_vectors: int, dimension: int) -> str:
        """根据数据规模自动选择最佳索引类型"""
        # 小规模数据：使用精确搜索
        if num_vectors < 1000:
            return "IndexFlatIP"

        # 中等规模数据：使用HNSW索引（速度和精度平衡）
        elif num_vectors < 100000:
            return "IndexHNSWFlat"

        # 大规模数据：使用IVF索引（内存效率高）
        else:
            return "IndexIVFFlat"

    def _preprocess_vectors_for_index(self, vectors: np.ndarray, index_type: str) -> np.ndarray:
        """为索引预处理向量"""
        vectors = vectors.astype(np.float32)

        # 对于内积索引，需要归一化向量
        if "IP" in index_type:
            faiss.normalize_L2(vectors)

        return vectors

    def _create_index_by_type(self, index_type: str, dimension: int, num_vectors: int) -> faiss.Index:
        """根据类型创建索引"""
        if index_type == "IndexFlatIP":
            return faiss.IndexFlatIP(dimension)

        elif index_type == "IndexFlatL2":
            return faiss.IndexFlatL2(dimension)

        elif index_type == "IndexHNSWFlat":
            index = faiss.IndexHNSWFlat(dimension, self.hnsw_m)
            index.hnsw.efConstruction = self.hnsw_ef_construction
            return index

        elif index_type == "IndexIVFFlat":
            # 计算合适的聚类中心数量
            nlist = min(max(int(np.sqrt(num_vectors)), 10), 1000)
            quantizer = faiss.IndexFlatL2(dimension)
            return faiss.IndexIVFFlat(quantizer, dimension, nlist)

        elif index_type == "IndexIVFPQ":
            # 使用乘积量化压缩
            nlist = min(max(int(np.sqrt(num_vectors)), 10), 1000)
            m = min(dimension // 4, 64)  # 子向量数量
            quantizer = faiss.IndexFlatL2(dimension)
            return faiss.IndexIVFPQ(quantizer, dimension, nlist, m, 8)

        else:
            logger.warning(f"不支持的索引类型: {index_type}，使用默认IndexFlatIP")
            return faiss.IndexFlatIP(dimension)

    def _optimize_index_parameters(self, index: faiss.Index, index_type: str):
        """优化索引参数"""
        if index_type == "IndexHNSWFlat":
            # 设置搜索参数
            index.hnsw.efSearch = self.hnsw_ef_search

        elif index_type.startswith("IndexIVF"):
            # 设置IVF搜索参数
            if hasattr(index, 'nprobe'):
                index.nprobe = min(max(index.nlist // 10, 1), 50)
    
    def search_similar(self, index: faiss.Index, query_vector: np.ndarray, k: int = 10) -> tuple:
        """
        在FAISS索引中搜索相似向量
        
        Args:
            index: FAISS索引
            query_vector: 查询向量
            k: 返回结果数量
            
        Returns:
            (相似度分数, 索引位置)
        """
        try:
            # 归一化查询向量
            query_vector = query_vector.reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_vector)
            
            # 搜索
            scores, indices = index.search(query_vector, k)
            
            return scores[0], indices[0]
            
        except Exception as e:
            logger.error(f"向量搜索失败: {str(e)}")
            return np.array([]), np.array([])
    
    def save_index(self, index: faiss.Index, file_path: str):
        """
        保存FAISS索引到文件
        
        Args:
            index: FAISS索引
            file_path: 保存路径
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            faiss.write_index(index, file_path)
            logger.info(f"FAISS索引已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"索引保存失败: {str(e)}")
    
    def load_index(self, file_path: str) -> Optional[faiss.Index]:
        """
        从文件加载FAISS索引
        
        Args:
            file_path: 索引文件路径
            
        Returns:
            FAISS索引
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"索引文件不存在: {file_path}")
                return None
            
            index = faiss.read_index(file_path)
            logger.info(f"成功加载FAISS索引: {file_path}, 向量数量: {index.ntotal}")
            return index
            
        except Exception as e:
            logger.error(f"索引加载失败: {str(e)}")
            return None
    
    def save_vectors_metadata(self, vectors: np.ndarray, metadata: List[Dict[str, Any]], file_path: str):
        """
        保存向量和元数据
        
        Args:
            vectors: 向量矩阵
            metadata: 元数据列表
            file_path: 保存路径
        """
        try:
            data = {
                "vectors": vectors,
                "metadata": metadata,
                "dimension": vectors.shape[1] if len(vectors) > 0 else 0,
                "count": len(vectors)
            }
            
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.info(f"向量和元数据已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"向量元数据保存失败: {str(e)}")
    
    def load_vectors_metadata(self, file_path: str) -> tuple:
        """
        加载向量和元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            (向量矩阵, 元数据列表)
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"向量文件不存在: {file_path}")
                return np.array([]), []
            
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            
            vectors = data.get("vectors", np.array([]))
            metadata = data.get("metadata", [])
            
            logger.info(f"成功加载向量和元数据: {file_path}, 向量数量: {len(vectors)}")
            return vectors, metadata
            
        except Exception as e:
            logger.error(f"向量元数据加载失败: {str(e)}")
            return np.array([]), []
    
    def process_documents(self, documents: List[Dict[str, Any]]) -> tuple:
        """
        处理文档列表，生成向量和元数据
        
        Args:
            documents: 文档列表
            
        Returns:
            (向量矩阵, 元数据列表)
        """
        try:
            if not documents:
                return np.array([]), []
            
            # 提取文本内容
            texts = [doc.get("content", "") for doc in documents]
            
            # 向量化
            vectors = self.encode_texts(texts)
            
            # 构建元数据
            metadata = []
            for i, doc in enumerate(documents):
                meta = {
                    "index": i,
                    "source": doc.get("source", ""),
                    "chunk_id": doc.get("chunk_id", 0),
                    "content": doc.get("content", ""),
                    "metadata": doc.get("metadata", {})
                }
                metadata.append(meta)
            
            logger.info(f"成功处理 {len(documents)} 个文档")
            return vectors, metadata
            
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            return np.array([]), []
