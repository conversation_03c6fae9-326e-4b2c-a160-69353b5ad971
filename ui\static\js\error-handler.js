// JavaScript错误处理和调试工具
// 用于捕获和报告前端JavaScript错误

(function() {
    'use strict';
    
    // 错误收集器
    const errorCollector = {
        errors: [],
        maxErrors: 50,
        
        // 添加错误
        addError: function(error) {
            this.errors.unshift({
                ...error,
                timestamp: new Date().toISOString(),
                id: Date.now() + Math.random()
            });
            
            // 限制错误数量
            if (this.errors.length > this.maxErrors) {
                this.errors = this.errors.slice(0, this.maxErrors);
            }
            
            // 在控制台显示错误
            console.group('🚨 JavaScript错误');
            console.error('错误类型:', error.type);
            console.error('错误消息:', error.message);
            console.error('文件:', error.filename);
            console.error('行号:', error.lineno);
            console.error('列号:', error.colno);
            if (error.stack) {
                console.error('堆栈:', error.stack);
            }
            console.groupEnd();
            
            // 显示用户友好的错误提示
            this.showUserError(error);
        },
        
        // 显示用户错误提示
        showUserError: function(error) {
            // 创建错误提示元素
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            errorDiv.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                font-size: 12px;
            `;
            
            errorDiv.innerHTML = `
                <strong>⚠️ 页面功能异常</strong><br>
                <small>${error.message}</small><br>
                <small class="text-muted">位置: ${error.filename}:${error.lineno}</small>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(errorDiv);
            
            // 5秒后自动移除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        },
        
        // 获取错误报告
        getReport: function() {
            return {
                total_errors: this.errors.length,
                errors: this.errors,
                user_agent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };
        }
    };
    
    // 全局错误处理器
    window.addEventListener('error', function(event) {
        errorCollector.addError({
            type: 'javascript_error',
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error ? event.error.stack : null
        });
    });
    
    // Promise错误处理器
    window.addEventListener('unhandledrejection', function(event) {
        errorCollector.addError({
            type: 'promise_rejection',
            message: event.reason ? event.reason.toString() : 'Promise rejected',
            filename: 'unknown',
            lineno: 0,
            colno: 0,
            stack: event.reason && event.reason.stack ? event.reason.stack : null
        });
    });
    
    // 函数存在性检查器
    const functionChecker = {
        requiredFunctions: [
            'updateDataIndicators',
            'updateStatusIndicators', 
            'loadEquipmentList',
            'updateEquipmentTableDisplay',
            'loadEquipmentHistory',
            'loadEquipmentBindings',
            'displayKnowledgeSearchResults',
            'showAlert',
            'showEnhancedNotification'
        ],
        
        // 检查所有必需函数
        checkAllFunctions: function() {
            const missing = [];
            const existing = [];
            
            this.requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    existing.push(funcName);
                } else {
                    missing.push(funcName);
                }
            });
            
            console.group('🔍 函数存在性检查');
            console.log('✅ 存在的函数:', existing);
            if (missing.length > 0) {
                console.error('❌ 缺失的函数:', missing);
            }
            console.groupEnd();
            
            return { existing, missing };
        }
    };
    
    // API连接测试器
    const apiTester = {
        baseUrl: '/api/v1',
        
        // 测试API连接
        testConnection: async function() {
            try {
                const response = await fetch(`${this.baseUrl}/status`);
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ API连接正常:', data);
                    return true;
                } else {
                    console.error('❌ API连接失败:', response.status);
                    return false;
                }
            } catch (error) {
                console.error('❌ API连接异常:', error);
                return false;
            }
        }
    };
    
    // 数据类型验证器
    const dataValidator = {
        // 验证设备列表数据
        validateEquipmentList: function(data) {
            if (!Array.isArray(data)) {
                console.error('❌ 设备列表不是数组:', typeof data, data);
                return false;
            }
            
            if (data.length === 0) {
                console.warn('⚠️ 设备列表为空');
                return true;
            }
            
            // 检查第一个设备的结构
            const firstEquipment = data[0];
            const requiredFields = ['id', 'name', 'type', 'status'];
            const missingFields = requiredFields.filter(field => !(field in firstEquipment));
            
            if (missingFields.length > 0) {
                console.error('❌ 设备数据缺少字段:', missingFields);
                return false;
            }
            
            console.log('✅ 设备列表数据格式正确');
            return true;
        }
    };
    
    // 暴露调试工具到全局
    window.debugTools = {
        errorCollector,
        functionChecker,
        apiTester,
        dataValidator,
        
        // 运行完整诊断
        runDiagnostics: async function() {
            console.group('🔧 运行完整诊断');
            
            // 检查函数
            const funcCheck = functionChecker.checkAllFunctions();
            
            // 测试API
            const apiOk = await apiTester.testConnection();
            
            // 检查设备列表
            if (window.equipmentList) {
                dataValidator.validateEquipmentList(window.equipmentList);
            }
            
            // 显示错误报告
            const report = errorCollector.getReport();
            if (report.total_errors > 0) {
                console.warn('⚠️ 发现错误:', report.total_errors);
                console.table(report.errors.slice(0, 5)); // 显示最近5个错误
            } else {
                console.log('✅ 暂无错误');
            }
            
            console.groupEnd();
            
            return {
                functions: funcCheck,
                api: apiOk,
                errors: report.total_errors
            };
        }
    };
    
    // 页面加载完成后自动运行诊断
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => window.debugTools.runDiagnostics(), 2000);
        });
    } else {
        setTimeout(() => window.debugTools.runDiagnostics(), 2000);
    }
    
})();
